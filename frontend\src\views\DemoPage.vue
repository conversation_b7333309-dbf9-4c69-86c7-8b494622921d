<template>
  <div class="demo-page">
    <!-- 演示页面头部 -->
    <div class="demo-header">
      <div class="header-content">
        <h1 class="demo-title">
          <el-icon><VideoPlay /></el-icon>
          系统演示
        </h1>
        <p class="demo-subtitle">体验多模态智能面试评测系统的完整功能</p>

        <!-- 演示统计信息 -->
        <div class="demo-stats" v-if="demoStats.totalVideos">
          <div class="stat-item">
            <span class="stat-number">{{ demoStats.totalVideos }}</span>
            <span class="stat-label">演示视频</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ demoStats.totalFeatures }}</span>
            <span class="stat-label">核心功能</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ demoStats.totalSteps }}</span>
            <span class="stat-label">体验步骤</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ demoStats.estimatedTime }}</span>
            <span class="stat-label">预计时长</span>
          </div>
        </div>

        <!-- 导航标签 -->
        <el-tabs v-model="activeTab" class="demo-tabs" @tab-click="handleTabClick">
          <el-tab-pane label="功能演示" name="features">
            <el-icon><Star /></el-icon>
          </el-tab-pane>
          <el-tab-pane label="视频教程" name="video">
            <el-icon><VideoCamera /></el-icon>
          </el-tab-pane>
          <el-tab-pane label="交互体验" name="interactive">
            <el-icon><Mouse /></el-icon>
          </el-tab-pane>
          <el-tab-pane label="技术架构" name="architecture">
            <el-icon><Setting /></el-icon>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 演示内容区域 -->
    <div class="demo-content">
      <!-- 功能演示 -->
      <div v-show="activeTab === 'features'" class="features-demo">
        <div class="features-grid">
          <div 
            v-for="(feature, index) in features" 
            :key="index"
            class="feature-card"
            @click="showFeatureDemo(feature)"
          >
            <div class="feature-icon">
              <el-icon :size="40">
                <component :is="feature.icon" />
              </el-icon>
            </div>
            <h3>{{ feature.title }}</h3>
            <p>{{ feature.description }}</p>
            <div class="feature-actions">
              <el-button type="primary" size="small" @click.stop="playFeatureDemo(feature)">
                <el-icon><VideoPlay /></el-icon>
                观看演示
              </el-button>
              <el-button size="small" @click.stop="tryFeature(feature)">
                <el-icon><Operation /></el-icon>
                立即体验
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 视频教程 -->
      <div v-show="activeTab === 'video'" class="video-demo">
        <div class="video-section">
          <div class="video-player">
            <div class="video-placeholder">
              <el-icon :size="80"><VideoCamera /></el-icon>
              <h3>系统演示视频</h3>
              <p>完整展示多模态面试评测系统的使用流程</p>
              <el-button type="primary" size="large" @click="playMainVideo">
                <el-icon><VideoPlay /></el-icon>
                播放演示视频
              </el-button>
            </div>
          </div>
          
          <div class="video-list">
            <h3>分步教程</h3>
            <div 
              v-for="(video, index) in videoTutorials" 
              :key="index"
              class="video-item"
              @click="playVideo(video)"
            >
              <div class="video-thumbnail">
                <el-icon><VideoPlay /></el-icon>
              </div>
              <div class="video-info">
                <h4>{{ video.title }}</h4>
                <p>{{ video.description }}</p>
                <span class="video-duration">{{ video.duration }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 交互体验 -->
      <div v-show="activeTab === 'interactive'" class="interactive-demo">
        <div class="demo-simulator">
          <h3>模拟面试体验</h3>
          <p>在这里您可以体验完整的面试流程，无需注册即可开始</p>
          
          <div class="simulator-steps">
            <div 
              v-for="(step, index) in simulatorSteps" 
              :key="index"
              class="step-card"
              :class="{ active: currentStep === index, completed: currentStep > index }"
              @click="goToStep(index)"
            >
              <div class="step-number">{{ index + 1 }}</div>
              <div class="step-content">
                <h4>{{ step.title }}</h4>
                <p>{{ step.description }}</p>
              </div>
              <div class="step-status">
                <el-icon v-if="currentStep > index"><Check /></el-icon>
                <el-icon v-else-if="currentStep === index"><ArrowRight /></el-icon>
              </div>
            </div>
          </div>
          
          <div class="simulator-actions">
            <el-button 
              v-if="currentStep < simulatorSteps.length - 1"
              type="primary" 
              @click="nextStep"
            >
              下一步
              <el-icon><ArrowRight /></el-icon>
            </el-button>
            <el-button 
              v-else
              type="success" 
              @click="startRealInterview"
            >
              开始真实面试
              <el-icon><VideoPlay /></el-icon>
            </el-button>
            <el-button @click="resetSimulator">重新开始</el-button>
          </div>
        </div>
      </div>

      <!-- 技术架构 -->
      <div v-show="activeTab === 'architecture'" class="architecture-demo">
        <div class="architecture-overview">
          <h3>技术架构图</h3>
          <div class="architecture-diagram">
            <div class="arch-layer frontend">
              <h4>前端层</h4>
              <div class="tech-stack">
                <span class="tech-item">Vue.js 3</span>
                <span class="tech-item">Element Plus</span>
                <span class="tech-item">WebRTC</span>
              </div>
            </div>
            
            <div class="arch-layer backend">
              <h4>后端层</h4>
              <div class="tech-stack">
                <span class="tech-item">FastAPI</span>
                <span class="tech-item">SQLAlchemy</span>
                <span class="tech-item">WebSocket</span>
              </div>
            </div>
            
            <div class="arch-layer ai">
              <h4>AI服务层</h4>
              <div class="tech-stack">
                <span class="tech-item">讯飞星火</span>
                <span class="tech-item">多模态分析</span>
                <span class="tech-item">智能评测</span>
              </div>
            </div>
            
            <div class="arch-layer data">
              <h4>数据层</h4>
              <div class="tech-stack">
                <span class="tech-item">SQLite</span>
                <span class="tech-item">文件存储</span>
                <span class="tech-item">缓存系统</span>
              </div>
            </div>
          </div>
          
          <div class="architecture-features">
            <div class="feature-highlight">
              <el-icon><Lightning /></el-icon>
              <h4>高性能</h4>
              <p>异步处理，实时响应</p>
            </div>
            <div class="feature-highlight">
              <el-icon><Shield /></el-icon>
              <h4>安全可靠</h4>
              <p>数据加密，隐私保护</p>
            </div>
            <div class="feature-highlight">
              <el-icon><Expand /></el-icon>
              <h4>可扩展</h4>
              <p>模块化设计，易于扩展</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 返回按钮 -->
    <div class="demo-footer">
      <el-button @click="goBack">
        <el-icon><ArrowLeft /></el-icon>
        返回首页
      </el-button>
      <el-button type="primary" @click="startInterview">
        <el-icon><VideoPlay /></el-icon>
        开始面试
      </el-button>
    </div>

    <!-- 功能演示对话框 -->
    <el-dialog
      v-model="showFeatureDialog"
      :title="selectedFeature?.title"
      width="90%"
      center
      :show-close="true"
    >
      <FeatureShowcase
        v-if="selectedFeature"
        :feature="selectedFeature"
        @start-demo="handleStartDemo"
        @try-feature="handleTryFeature"
        @view-details="handleViewDetails"
      />
    </el-dialog>

    <!-- 视频播放对话框 -->
    <el-dialog
      v-model="showVideoDialog"
      :title="selectedVideo?.title"
      width="90%"
      center
    >
      <div class="video-player-dialog">
        <div class="video-placeholder-large">
          <el-icon :size="100"><VideoCamera /></el-icon>
          <h3>{{ selectedVideo?.title }}</h3>
          <p>{{ selectedVideo?.description }}</p>
          <el-button type="primary" size="large" @click="startVideoPlayback">
            <el-icon><VideoPlay /></el-icon>
            开始播放
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  VideoPlay, VideoCamera, Star, Mouse, Setting, Operation,
  Check, ArrowRight, ArrowLeft, Lightning, Shield, Expand,
  ChatDotRound, TrendCharts, Document
} from '@element-plus/icons-vue'
import DemoService from '../services/demoService.js'
import FeatureShowcase from '../components/Demo/FeatureShowcase.vue'

const router = useRouter()

// 响应式数据
const activeTab = ref('features')
const currentStep = ref(0)
const showFeatureDialog = ref(false)
const showVideoDialog = ref(false)
const selectedFeature = ref(null)
const selectedVideo = ref(null)
const demoStats = ref({})

// 从服务获取数据
const features = ref([])
const videoTutorials = ref([])
const simulatorSteps = ref([])
const architectureData = ref({})

// 初始化数据
onMounted(() => {
  loadDemoData()
})

const loadDemoData = () => {
  // 从服务加载演示数据
  const featuresData = DemoService.getFeatures()
  features.value = featuresData.map(feature => ({
    icon: feature.icon,
    title: feature.title,
    description: feature.description,
    demoDescription: feature.highlights.join('、'),
    id: feature.id,
    highlights: feature.highlights,
    demoSteps: feature.demoSteps
  }))

  const videosData = DemoService.getVideos()
  videoTutorials.value = videosData.tutorials

  const stepsData = DemoService.getInteractiveSteps()
  simulatorSteps.value = stepsData.map(step => ({
    title: step.title,
    description: step.description,
    id: step.id,
    tips: step.tips,
    mockData: step.mockData
  }))

  architectureData.value = DemoService.getArchitecture()
  demoStats.value = DemoService.getStats()
}

// 方法
const handleTabClick = (tab) => {
  console.log('切换到标签:', tab.props.name)
}

const showFeatureDemo = (feature) => {
  selectedFeature.value = feature
  showFeatureDialog.value = true
}

const playFeatureDemo = (feature) => {
  const result = DemoService.startFeatureDemo(feature.id)
  if (result.success) {
    ElMessage.success(result.message)
    showFeatureDemo(feature)
  } else {
    ElMessage.error(result.message)
  }
}

const tryFeature = (feature) => {
  ElMessage.info(`即将体验 ${feature.title} 功能`)
  // 根据不同功能跳转到相应页面
  switch (feature.id) {
    case 'multimodal-input':
      router.push('/interview-selection')
      break
    case 'comprehensive-report':
      // 如果有测试报告，跳转到报告页面
      router.push('/test-report-to-learning')
      break
    case 'ai-interaction':
      router.push('/interview-selection')
      break
    default:
      router.push('/interview-selection')
  }
}

const playMainVideo = () => {
  const videosData = DemoService.getVideos()
  const result = DemoService.playVideo('main-demo')
  if (result.success) {
    ElMessage.success(result.message)
    selectedVideo.value = videosData.main
    showVideoDialog.value = true
  }
}

const playVideo = (video) => {
  const result = DemoService.playVideo(video.id)
  if (result.success) {
    ElMessage.success(result.message)
    selectedVideo.value = video
    showVideoDialog.value = true
  }
}

const nextStep = () => {
  if (currentStep.value < simulatorSteps.length - 1) {
    currentStep.value++
    ElMessage.success(`进入步骤 ${currentStep.value + 1}`)
  }
}

const goToStep = (index) => {
  currentStep.value = index
  ElMessage.info(`跳转到步骤 ${index + 1}`)
}

const resetSimulator = () => {
  currentStep.value = 0
  ElMessage.info('模拟器已重置')
}

const startRealInterview = () => {
  ElMessageBox.confirm(
    '您即将开始真实的面试流程，是否确认？',
    '开始面试',
    {
      confirmButtonText: '确认开始',
      cancelButtonText: '取消',
      type: 'info'
    }
  ).then(() => {
    router.push('/interview-selection')
  })
}

const goBack = () => {
  router.push('/')
}

const startInterview = () => {
  router.push('/interview-selection')
}

const playFeatureVideo = () => {
  ElMessage.success(`正在播放 ${selectedFeature.value?.title} 演示视频`)
  showFeatureDialog.value = false
}

const startVideoPlayback = () => {
  ElMessage.success(`正在播放: ${selectedVideo.value?.title}`)
  showVideoDialog.value = false
}

// FeatureShowcase 组件事件处理
const handleStartDemo = (feature) => {
  ElMessage.success(`开始演示: ${feature.title}`)
  showFeatureDialog.value = false
  // 这里可以启动具体的演示流程
}

const handleTryFeature = (feature) => {
  showFeatureDialog.value = false
  tryFeature(feature)
}

const handleViewDetails = (feature) => {
  ElMessage.info(`查看 ${feature.title} 详细信息`)
  // 这里可以显示更详细的功能说明
}
</script>

<style scoped>
.demo-page {
  min-height: 100vh;
  background: var(--bg-primary);
}

.demo-header {
  background: var(--gradient-primary);
  color: white;
  padding: var(--spacing-xl) 0;
  text-align: center;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.demo-title {
  font-size: 2.5rem;
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
}

.demo-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  margin-bottom: var(--spacing-lg);
}

.demo-stats {
  display: flex;
  justify-content: center;
  gap: var(--spacing-xl);
  margin: var(--spacing-lg) 0;
  padding: var(--spacing-lg);
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-lg);
  backdrop-filter: blur(10px);
}

.stat-item {
  text-align: center;
  color: white;
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.8;
}

.demo-tabs {
  margin-top: var(--spacing-lg);
}

.demo-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-xl);
}

/* 功能演示样式 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

.feature-card {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  text-align: center;
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
  cursor: pointer;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.feature-icon {
  color: var(--color-primary);
  margin-bottom: var(--spacing-lg);
}

.feature-actions {
  margin-top: var(--spacing-lg);
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
}

/* 视频演示样式 */
.video-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-xl);
  margin-top: var(--spacing-lg);
}

.video-player {
  background: white;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.video-placeholder {
  padding: var(--spacing-xxl);
  text-align: center;
  background: var(--bg-secondary);
}

.video-list {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
}

.video-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.video-item:hover {
  background: var(--bg-secondary);
}

.video-thumbnail {
  width: 60px;
  height: 40px;
  background: var(--color-primary);
  border-radius: var(--border-radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-right: var(--spacing-md);
}

/* 交互体验样式 */
.demo-simulator {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
  margin-top: var(--spacing-lg);
}

.simulator-steps {
  margin: var(--spacing-xl) 0;
}

.step-card {
  display: flex;
  align-items: center;
  padding: var(--spacing-lg);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-md);
  margin-bottom: var(--spacing-md);
  cursor: pointer;
  transition: all 0.3s ease;
}

.step-card.active {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
}

.step-card.completed {
  border-color: var(--color-success);
  background: var(--color-success-light);
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--color-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: var(--spacing-lg);
}

.step-content {
  flex: 1;
}

.simulator-actions {
  text-align: center;
  margin-top: var(--spacing-xl);
}

/* 技术架构样式 */
.architecture-overview {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
  margin-top: var(--spacing-lg);
}

.architecture-diagram {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-lg);
  margin: var(--spacing-xl) 0;
}

.arch-layer {
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-md);
  text-align: center;
}

.arch-layer.frontend {
  background: linear-gradient(135deg, #4CAF50, #8BC34A);
  color: white;
}

.arch-layer.backend {
  background: linear-gradient(135deg, #2196F3, #03A9F4);
  color: white;
}

.arch-layer.ai {
  background: linear-gradient(135deg, #FF9800, #FFC107);
  color: white;
}

.arch-layer.data {
  background: linear-gradient(135deg, #9C27B0, #E91E63);
  color: white;
}

.tech-stack {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  justify-content: center;
  margin-top: var(--spacing-md);
}

.tech-item {
  background: rgba(255, 255, 255, 0.2);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: 0.9rem;
}

.architecture-features {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

.feature-highlight {
  text-align: center;
  padding: var(--spacing-lg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
}

.demo-footer {
  text-align: center;
  padding: var(--spacing-xl);
  background: white;
  border-top: 1px solid var(--border-color);
}

/* 对话框样式 */
.feature-demo-content,
.video-player-dialog {
  text-align: center;
  padding: var(--spacing-xl);
}

.demo-video-placeholder,
.video-placeholder-large {
  background: var(--bg-secondary);
  padding: var(--spacing-xxl);
  border-radius: var(--border-radius-lg);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .video-section {
    grid-template-columns: 1fr;
  }
  
  .architecture-diagram {
    grid-template-columns: 1fr;
  }
  
  .architecture-features {
    grid-template-columns: 1fr;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
}
</style>
