<template>
  <div class="interview-page">
    <!-- 顶部状态栏 -->
    <div class="interview-header">
      <div class="header-content">
        <div class="interview-info">
          <div class="status-indicator">
            <div class="status-dot" :class="{ 'active': !interviewEnded }"></div>
            <span class="status-text">{{ interviewEnded ? '面试已结束' : '面试进行中' }}</span>
          </div>
          <h1 class="interview-title">{{ interviewTitle }}</h1>
        </div>

        <div class="header-actions">
          <div class="progress-info">
            <span class="progress-text">问题 {{ currentQuestionIndex + 1 }}</span>
          </div>
          <el-button
            type="danger"
            :icon="CircleCloseFilled"
            @click="endCurrentInterview"
            class="end-button"
            size="large"
          >
            结束面试
          </el-button>
        </div>
      </div>
    </div>

    <!-- 聊天区域 -->
    <div class="chat-container">
      <div class="message-container" ref="chatWindowRef">
        <!-- 欢迎消息 -->
        <div v-if="messages.length === 0" class="welcome-message fade-in">
          <div class="welcome-content">
            <div class="ai-avatar">
              <svg width="48" height="48" viewBox="0 0 48 48" fill="none">
                <circle cx="24" cy="24" r="24" fill="url(#welcomeGradient)"/>
                <path d="M16 20h16v2H16v-2zm0 4h16v2H16v-2zm0 4h12v2H16v-2z" fill="white"/>
                <defs>
                  <linearGradient id="welcomeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#1890ff"/>
                    <stop offset="100%" style="stop-color:#40a9ff"/>
                  </linearGradient>
                </defs>
              </svg>
            </div>
            <div class="welcome-text">
              <h3>欢迎参加智能面试评测</h3>
              <p>我是您的AI面试官，将为您提供专业的面试体验。请放松心情，展示您的最佳状态。</p>
              <div class="welcome-tips">
                <div class="tip-item">
                  <el-icon><ChatDotRound /></el-icon>
                  <span>回答问题时请尽量详细和具体</span>
                </div>
                <div class="tip-item">
                  <el-icon><Timer /></el-icon>
                  <span>每个问题建议回答时间2-3分钟</span>
                </div>
                <div class="tip-item">
                  <el-icon><Star /></el-icon>
                  <span>展示您的专业知识和实践经验</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 消息列表 -->
        <div v-for="(msg, index) in processedMessages" :key="index" :class="['message', msg.role]">
          <div v-if="msg.role === 'assistant'" class="assistant-message-wrapper">
            <div class="message-avatar">
              <div class="ai-avatar-small">
                <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                  <circle cx="16" cy="16" r="16" fill="url(#aiGradient)"/>
                  <path d="M10 12h12v2H10v-2zm0 3h12v2H10v-2zm0 3h8v2h-8v-2z" fill="white"/>
                  <defs>
                    <linearGradient id="aiGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                      <stop offset="0%" style="stop-color:#52c41a"/>
                      <stop offset="100%" style="stop-color:#73d13d"/>
                    </linearGradient>
                  </defs>
                </svg>
              </div>
            </div>
            <div class="message-bubble assistant-bubble">
              <details class="thinking-process" :open="!msg.thinkingDone">
                <summary class="thinking-summary">
                  <el-icon><StarFilled /></el-icon>
                  AI 分析思路
                </summary>
                <div class="message-content thinking-content" v-html="msg.thinkingHtml"></div>
              </details>
              <div v-if="msg.reply" class="message-content final-reply" v-html="msg.replyHtml"></div>
            </div>
          </div>

          <div v-else class="user-message-wrapper">
            <div class="message-bubble user-bubble">
              <div class="message-content">{{ msg.content }}</div>
            </div>
            <div class="message-avatar">
              <div class="user-avatar">
                <el-icon><User /></el-icon>
              </div>
            </div>
          </div>
        </div>

        <!-- 加载指示器 -->
        <div v-if="isThinking" class="typing-indicator fade-in">
          <div class="message-avatar">
            <div class="ai-avatar-small">
              <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                <circle cx="16" cy="16" r="16" fill="url(#typingGradient)"/>
                <defs>
                  <linearGradient id="typingGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#52c41a"/>
                    <stop offset="100%" style="stop-color:#73d13d"/>
                  </linearGradient>
                </defs>
              </svg>
            </div>
          </div>
          <div class="typing-bubble">
            <div class="typing-dots">
              <div class="dot"></div>
              <div class="dot"></div>
              <div class="dot"></div>
            </div>
            <span class="typing-text">AI正在思考中...</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="input-area">
      <div class="input-container">
        <div class="input-wrapper">
          <el-input
            v-model="userInput"
            placeholder="请输入您的回答，展示您的专业能力..."
            @keyup.enter="handleSendMessage"
            :disabled="isThinking || interviewEnded"
            class="input-box"
            size="large"
            type="textarea"
            :rows="3"
            resize="none"
          />
          <div class="input-actions">
            <div class="input-tips">
              <span class="tip-text">按 Enter 发送，Shift + Enter 换行</span>
            </div>
            <el-button
              type="primary"
              @click="handleSendMessage"
              :disabled="isThinking || interviewEnded || !userInput.trim()"
              class="send-button"
              size="large"
            >
              <el-icon><Promotion /></el-icon>
              发送回答
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  CircleCloseFilled,
  ChatDotRound,
  Timer,
  Star,
  StarFilled,
  User,
  Promotion
} from "@element-plus/icons-vue";
import { startInterview as startInterviewApi, nextQuestion as getNextQuestionApi, endInterview as endInterviewApi } from '@/services/api'
import 'katex/dist/katex.min.css'
import MarkdownIt from 'markdown-it'
import mk from 'markdown-it-katex'

// --- Vue Router and Markdown Initialization ---
const route = useRoute()
const router = useRouter()
const md = new MarkdownIt().use(mk);

// --- Component State ---
const messages = ref([])
const userInput = ref('')
const isThinking = ref(false)
const interviewEnded = ref(false)
const chatWindowRef = ref(null)
const currentQuestionIndex = ref(0)

// --- Computed Properties ---
const interviewTitle = computed(() => {
  const domain = route.query.domain || '未知领域';
  const position = route.query.position || '未知岗位';
  return `当前面试岗位：${position}（${domain}领域）`;
});

const processedMessages = computed(() => {
  return messages.value.map(msg => {
    if (msg.role === 'assistant') {
      return {
        ...msg,
        thinkingHtml: md.render(msg.thinking || ''),
        replyHtml: msg.reply ? md.render(msg.reply) : ''
      };
    }
    return msg;
  });
});

// --- Helper Functions ---
const scrollToBottom = () => {
  nextTick(() => {
    if (chatWindowRef.value) {
      chatWindowRef.value.scrollTop = chatWindowRef.value.scrollHeight;
    }
  });
};

// --- API Interaction ---
const startInterview = async () => {
  isThinking.value = true;
  const assistantMessageIndex = messages.value.length;
  messages.value.push({
    role: 'assistant',
    thinking: '面试官正在进入面试间，并准备第一个问题...',
    reply: '',
    thinkingDone: false
  });
  scrollToBottom();
  
  try {
    const payload = {
      domain: route.query.domain,
      position: route.query.position
    };
    // 给后端一点时间准备
    await new Promise(resolve => setTimeout(resolve, 1000));

    const response = await startInterviewApi(payload);
    const msg = messages.value[assistantMessageIndex];
    msg.thinking = response.data.thinking || '已为您准备好第一个问题。';
    msg.reply = response.data.question;
    msg.thinkingDone = true;
  } catch (error) {
    console.error('Failed to start interview:', error);
    const msg = messages.value[assistantMessageIndex];
    msg.thinking = '面试初始化失败。';
    msg.reply = '抱歉，面试初始化失败，请返回重试。';
    msg.thinkingDone = true;
    ElMessage.error('面试初始化失败');
  } finally {
    isThinking.value = false;
    scrollToBottom();
  }
};

const sendMessage = async () => {
  if (!userInput.value.trim() || isThinking.value) return;

  const userMessageContent = userInput.value;
  messages.value.push({ role: 'user', content: userMessageContent });
  userInput.value = '';
  await nextTick();
  scrollToBottom();
  
  isThinking.value = true;
  const assistantMessageIndex = messages.value.length;
  messages.value.push({
    role: 'assistant',
    thinking: '',
    reply: '',
    thinkingDone: false
  });
  await nextTick();
  scrollToBottom();

  // 模拟流式思考过程
  const thinkingText = "好的，我收到了您的回答。让我想一想...\n\n1. 正在分析您的回答与岗位要求的匹配度。\n2. 检索我的知识库，寻找相关的追问点。\n3. 构思下一个问题...";
  let currentThinkingText = '';
  const intervalId = setInterval(() => {
    if (currentThinkingText.length >= thinkingText.length) {
      clearInterval(intervalId);
      messages.value[assistantMessageIndex].thinking = thinkingText;
      fetchNextQuestion(assistantMessageIndex);
      return;
    }
    const nextChunk = thinkingText.substring(currentThinkingText.length, currentThinkingText.length + 5);
    currentThinkingText += nextChunk;
    messages.value[assistantMessageIndex].thinking = currentThinkingText + '...';
    scrollToBottom();
  }, 100);
};

const fetchNextQuestion = async (index) => {
  try {
    const history = messages.value.slice(0, index).map(m => {
      if (m.role === 'user') return { role: 'user', content: m.content };
      if (m.role === 'assistant' && m.reply) return { role: 'assistant', content: m.reply };
      return null;
    }).filter(Boolean);

    const payload = {
      messages: history,
      domain: route.query.domain,
      position: route.query.position
    };
    
    const response = await getNextQuestionApi(payload);
    const msg = messages.value[index];
    msg.thinking = response.data.thinking || '思考完毕！';
    msg.reply = response.data.question;
    msg.thinkingDone = true;
  } catch (error) {
    console.error('Failed to get next question:', error);
    const msg = messages.value[index];
    msg.thinking = "出现错误。";
    msg.reply = '抱歉，我遇到了一些麻烦，请稍后再试。';
    msg.thinkingDone = true;
    ElMessage.error('获取 AI 回复失败');
  } finally {
    isThinking.value = false;
    scrollToBottom();
  }
};

const endInterview = () => {
  ElMessageBox.confirm('您确定要结束本次面试吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    // 获取sessionId
    const sessionId = route.params.sessionId

    if (sessionId) {
      // 跳转到报告页面，传递sessionId
      router.push({
        name: 'Report',
        params: {
          sessionId: sessionId
        }
      });
    } else {
      // 如果没有sessionId，跳转到面试选择页面
      ElMessage.warning('未找到面试会话ID，返回面试选择页面')
      router.push('/interview-selection')
    }
  }).catch(() => {
    // User cancelled
  });
};

const handleSendMessage = () => {
  sendMessage();
};

const endCurrentInterview = () => {
  endInterview();
};

// --- Lifecycle Hooks ---
onMounted(() => {
  // 检查是否有sessionId参数
  const sessionId = route.params.sessionId

  if (sessionId) {
    // 如果有sessionId，说明是从面试选择页面跳转过来的
    // 可以开始面试或继续现有面试
    if (route.query.domain && route.query.position) {
      startInterview();
    } else {
      ElMessage.error('缺少面试配置信息');
      router.push('/interview-selection');
    }
  } else if (route.query.domain && route.query.position) {
    // 兼容旧的查询参数方式
    startInterview();
  } else {
    ElMessage.error('无效的面试设置，请返回面试选择页面');
    router.push('/interview-selection');
  }
});
</script>

<style scoped>
.interview-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: var(--bg-secondary);
  position: relative;
}

/* ==================== 顶部状态栏 ==================== */
.interview-header {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-lg) var(--spacing-xl);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.interview-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: var(--border-radius-full);
  background: var(--color-error);
  transition: var(--transition-base);
}

.status-dot.active {
  background: var(--color-success);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.status-text {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

.interview-title {
  margin: 0;
  color: var(--text-primary);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.progress-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-base);
}

.progress-text {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--bg-secondary);
  border-radius: var(--border-radius-base);
}

.end-button {
  font-weight: var(--font-weight-medium);
  border-radius: var(--border-radius-lg);
  transition: var(--transition-base);
}

.end-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* ==================== 聊天区域 ==================== */
.chat-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  padding: 0 var(--spacing-xl);
}

.message-container {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-xl) 0;
  scroll-behavior: smooth;
}

/* 欢迎消息 */
.welcome-message {
  margin-bottom: var(--spacing-2xl);
  padding: var(--spacing-2xl);
  background: var(--bg-primary);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-base);
  border: 1px solid var(--border-light);
}

.welcome-content {
  display: flex;
  gap: var(--spacing-lg);
  align-items: flex-start;
}

.ai-avatar {
  flex-shrink: 0;
}

.welcome-text h3 {
  margin: 0 0 var(--spacing-base) 0;
  color: var(--text-primary);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

.welcome-text p {
  margin: 0 0 var(--spacing-lg) 0;
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
}

.welcome-tips {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.tip-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.tip-item .el-icon {
  color: var(--primary-color);
}

/* 消息样式 */
.message {
  margin-bottom: var(--spacing-lg);
  animation: fade-in-up 0.3s ease-out;
}

.assistant-message-wrapper,
.user-message-wrapper {
  display: flex;
  gap: var(--spacing-base);
  align-items: flex-start;
  max-width: 85%;
}

.user-message-wrapper {
  margin-left: auto;
  flex-direction: row-reverse;
}

.message-avatar {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
}

.ai-avatar-small,
.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-sm);
}

.user-avatar {
  background: var(--gradient-primary);
  color: white;
  font-size: 18px;
}

.message-bubble {
  flex: 1;
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-base) var(--spacing-lg);
  position: relative;
  box-shadow: var(--shadow-sm);
  transition: var(--transition-base);
}

.assistant-bubble {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-bottom-left-radius: var(--border-radius-sm);
}

.user-bubble {
  background: var(--gradient-primary);
  color: white;
  border-bottom-right-radius: var(--border-radius-sm);
}

.message-bubble:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-base);
}

/* 思考过程样式 */
.thinking-process {
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius-base);
  margin-bottom: var(--spacing-base);
  overflow: hidden;
}

.thinking-summary {
  cursor: pointer;
  padding: var(--spacing-sm) var(--spacing-base);
  background: var(--bg-secondary);
  border: none;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  transition: var(--transition-base);
  width: 100%;
}

.thinking-summary:hover {
  background: var(--bg-hover);
}

.thinking-content {
  padding: var(--spacing-base);
  background: var(--bg-primary);
  border-top: 1px solid var(--border-light);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
}

.final-reply {
  background: transparent;
  padding: var(--spacing-base) 0;
  color: var(--text-primary);
  line-height: var(--line-height-relaxed);
}

.message-content {
  word-wrap: break-word;
  white-space: pre-wrap;
  line-height: var(--line-height-relaxed);
  font-size: var(--font-size-base);
}

/* 打字指示器 */
.typing-indicator {
  display: flex;
  gap: var(--spacing-base);
  align-items: flex-start;
  margin-bottom: var(--spacing-lg);
}

.typing-bubble {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-base) var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-base);
  box-shadow: var(--shadow-sm);
}

.typing-dots {
  display: flex;
  gap: 4px;
}

.typing-dots .dot {
  width: 6px;
  height: 6px;
  border-radius: var(--border-radius-full);
  background: var(--primary-color);
  animation: typing-dot 1.4s infinite ease-in-out;
}

.typing-dots .dot:nth-child(1) { animation-delay: 0s; }
.typing-dots .dot:nth-child(2) { animation-delay: 0.2s; }
.typing-dots .dot:nth-child(3) { animation-delay: 0.4s; }

@keyframes typing-dot {
  0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
  40% { transform: scale(1); opacity: 1; }
}

.typing-text {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* ==================== 输入区域 ==================== */
.input-area {
  background: var(--bg-primary);
  border-top: 1px solid var(--border-light);
  padding: var(--spacing-lg) 0;
  position: sticky;
  bottom: 0;
  z-index: 50;
  backdrop-filter: blur(10px);
}

.input-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-xl);
}

.input-wrapper {
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  transition: var(--transition-base);
}

.input-wrapper:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1);
}

.input-box {
  border: none;
  background: transparent;
}

.input-box :deep(.el-textarea__inner) {
  border: none;
  background: transparent;
  padding: var(--spacing-lg);
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
  resize: none;
  box-shadow: none;
}

.input-box :deep(.el-textarea__inner):focus {
  box-shadow: none;
}

.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-base) var(--spacing-lg);
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-light);
}

.input-tips {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.tip-text {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

.send-button {
  background: var(--gradient-primary);
  border: none;
  border-radius: var(--border-radius-lg);
  font-weight: var(--font-weight-medium);
  transition: var(--transition-base);
  position: relative;
  overflow: hidden;
}

.send-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: var(--transition-slow);
}

.send-button:hover::before {
  left: 100%;
}

.send-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.send-button:disabled {
  opacity: 0.6;
  transform: none;
  box-shadow: none;
}

.dot-flashing {
  position: relative;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: #909399;
  color: #909399;
  animation: dot-flashing 1s infinite linear alternate;
  animation-delay: 0.5s;
  display: inline-block;
  margin: 0 8px;
}

.dot-flashing::before, .dot-flashing::after {
  content: "";
  display: inline-block;
  position: absolute;
  top: 0;
}

.dot-flashing::before {
  left: -15px;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: #909399;
  color: #909399;
  animation: dot-flashing 1s infinite alternate;
  animation-delay: 0s;
}

.dot-flashing::after {
  left: 15px;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: #909399;
  color: #909399;
  animation: dot-flashing 1s infinite alternate;
  animation-delay: 1s;
}

@keyframes dot-flashing {
  0% {
    background-color: #909399;
  }
  50%, 100% {
    background-color: #dcdfe6;
  }
}

/* Styles for rendered markdown */
.message-content :deep(h1),
.message-content :deep(h2),
.message-content :deep(h3) {
  margin-top: 1.2em;
  margin-bottom: 0.6em;
  font-weight: 600;
}

.message-content :deep(p) {
  margin-bottom: 1em;
}

.message-content :deep(pre) {
  background-color: #f4f5f7;
  padding: 1em;
  border-radius: 8px;
  overflow-x: auto;
  font-family: 'Courier New', Courier, monospace;
}

.message-content :deep(code) {
  background-color: #f4f5f7;
  padding: 0.2em 0.4em;
  border-radius: 4px;
  font-family: 'Courier New', Courier, monospace;
}

.message-content :deep(pre) > :deep(code) {
  background-color: transparent;
  padding: 0;
}

.message-content :deep(table) {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1em;
  display: table;
}

.message-content :deep(th),
.message-content :deep(td) {
  border: 1px solid #dfe2e5;
  padding: 0.6em 1em;
}

.message-content :deep(th) {
  font-weight: 600;
  background-color: #f4f5f7;
}

.message-content :deep(ul),
.message-content :deep(ol) {
  padding-left: 2em;
  margin-bottom: 1em;
}

.message-content :deep(blockquote) {
  border-left: 4px solid #dfe2e5;
  padding-left: 1em;
  color: #6a737d;
  margin-left: 0;
  margin-right: 0;
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 768px) {
  .header-content {
    padding: var(--spacing-base) var(--spacing-lg);
    flex-direction: column;
    gap: var(--spacing-base);
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
  }

  .interview-title {
    font-size: var(--font-size-lg);
  }

  .chat-container {
    padding: 0 var(--spacing-lg);
  }

  .message-container {
    padding: var(--spacing-lg) 0;
  }

  .assistant-message-wrapper,
  .user-message-wrapper {
    max-width: 95%;
  }

  .welcome-content {
    flex-direction: column;
    text-align: center;
  }

  .welcome-tips {
    align-items: center;
  }

  .input-container {
    padding: 0 var(--spacing-lg);
  }

  .input-actions {
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: stretch;
  }

  .send-button {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: var(--spacing-sm) var(--spacing-base);
  }

  .interview-title {
    font-size: var(--font-size-base);
  }

  .chat-container {
    padding: 0 var(--spacing-base);
  }

  .input-container {
    padding: 0 var(--spacing-base);
  }

  .message-avatar {
    width: 32px;
    height: 32px;
  }

  .ai-avatar-small,
  .user-avatar {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }
}
</style>
