<template>
  <div class="demo-page">
    <!-- 演示页面头部 -->
    <div class="demo-header">
      <!-- 粒子背景 -->
      <ParticleBackground
        :particle-count="60"
        particle-color="#1890ff"
        :particle-size="3"
        :speed="0.8"
        :opacity="0.4"
      />

      <div class="header-content">
        <h1 class="demo-title">
          <el-icon><VideoPlay /></el-icon>
          系统演示
        </h1>
        <p class="demo-subtitle">体验多模态智能面试评测系统的完整功能</p>

        <!-- 演示统计信息 -->
        <div class="demo-stats" v-if="demoStats.totalVideos">
          <div class="stat-item">
            <span class="stat-number">{{ demoStats.totalVideos }}</span>
            <span class="stat-label">演示视频</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ demoStats.totalFeatures }}</span>
            <span class="stat-label">核心功能</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ demoStats.totalSteps }}</span>
            <span class="stat-label">体验步骤</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ Math.round(demoStats.estimatedTotalTime / 60) || 30 }}分钟</span>
            <span class="stat-label">预计时长</span>
          </div>
        </div>

        <!-- 导航标签 -->
        <el-tabs v-model="activeTab" class="demo-tabs" @tab-click="handleTabClick">
          <el-tab-pane label="功能演示" name="features">
            <el-icon><Star /></el-icon>
          </el-tab-pane>
          <el-tab-pane label="视频教程" name="video">
            <el-icon><VideoCamera /></el-icon>
          </el-tab-pane>
          <el-tab-pane label="交互体验" name="interactive">
            <el-icon><Mouse /></el-icon>
          </el-tab-pane>
          <el-tab-pane label="技术架构" name="architecture">
            <el-icon><Setting /></el-icon>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 演示内容区域 -->
    <div class="demo-content">
      <!-- 功能演示 -->
      <div v-show="activeTab === 'features'" class="features-demo">
        <div class="features-grid">
          <div
            v-for="(feature, index) in features"
            :key="index"
            class="feature-card animate-fade-in-up"
            :style="{ animationDelay: `${index * 0.1}s` }"
            @click="showFeatureDemo(feature)"
          >
            <!-- 功能图片展示 -->
            <div class="feature-media">
              <LazyImage
                :src="getFeatureImage(feature.key)"
                :fallback-src="getFeaturePlaceholder(feature.key)"
                :alt="feature.title + '功能展示'"
                :hover-zoom="true"
                :overlay="true"
                :overlay-title="feature.title"
                :overlay-description="feature.description"
                class="feature-image"
              />

              <!-- 功能图标覆盖 -->
              <div class="feature-icon-overlay">
                <div class="feature-icon">
                  <el-icon :size="40">
                    <component :is="feature.icon" />
                  </el-icon>
                </div>
              </div>
            </div>

            <!-- 功能信息 -->
            <div class="feature-info">
              <h3>{{ feature.title }}</h3>
              <p>{{ feature.description }}</p>

              <!-- 功能标签 -->
              <div class="feature-tags" v-if="feature.tags">
                <el-tag
                  v-for="tag in feature.tags"
                  :key="tag"
                  size="small"
                  class="feature-tag"
                >
                  {{ tag }}
                </el-tag>
              </div>

              <div class="feature-actions">
                <el-button type="primary" size="small" @click.stop="playFeatureDemo(feature)">
                  <el-icon><VideoPlay /></el-icon>
                  观看演示
                </el-button>
                <el-button type="success" size="small" @click.stop="tryFeature(feature)">
                  <el-icon><Operation /></el-icon>
                  立即体验
                </el-button>
                <el-button type="info" size="small" @click.stop="showFeatureDetails(feature)">
                  <el-icon><View /></el-icon>
                  查看详情
                </el-button>
                <el-button type="warning" size="small" @click.stop="showTechnicalSpecs(feature)">
                  <el-icon><Setting /></el-icon>
                  技术规格
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 视频教程 -->
      <div v-show="activeTab === 'video'" class="video-demo">
        <!-- 主演示视频区域 -->
        <ParallaxSection class="main-video-section" :speed="0.3">
          <template #background>
            <div class="video-background-pattern"></div>
          </template>

          <div class="video-player-container">
            <div class="video-player">
              <!-- 视频缩略图 -->
              <LazyImage
                :src="getVideoThumbnail('main')"
                :fallback-src="getVideoPlaceholder('main')"
                alt="主演示视频缩略图"
                :hover-zoom="true"
                :show-progress="true"
                class="video-thumbnail"
                @click="openVideoDialog"
              />

              <!-- 播放按钮覆盖层 -->
              <div class="video-play-overlay" @click="openVideoDialog">
                <div class="play-button-large">
                  <el-icon :size="60"><VideoPlay /></el-icon>
                </div>
                <div class="video-duration-badge">{{ mainVideoDetails.duration }}</div>
              </div>

              <div class="video-info-overlay">
                <h3>{{ mainVideoDetails.title }}</h3>
                <p>{{ mainVideoDetails.description }}</p>

                <!-- 视频信息卡片 -->
                <div class="video-info-card">
                  <div class="video-stats">
                    <span class="stat-item">
                      <el-icon><Clock /></el-icon>
                      {{ mainVideoDetails.duration }}
                    </span>
                    <span class="stat-item">
                      <el-icon><View /></el-icon>
                      {{ mainVideoDetails.views }}次观看
                    </span>
                    <span class="stat-item">
                      <el-icon><Star /></el-icon>
                      {{ mainVideoDetails.rating }}分
                    </span>
                    <span class="stat-item">
                      <el-icon><Calendar /></el-icon>
                      {{ mainVideoDetails.publishDate }}
                    </span>
                  </div>

                  <!-- 技术特性标签 -->
                  <div class="tech-features" v-if="mainVideoDetails.features">
                    <el-tag
                      v-for="feature in mainVideoDetails.features.filter(f => f.highlight)"
                      :key="feature.name"
                      type="primary"
                      size="small"
                      class="feature-tag"
                    >
                      {{ feature.name }}
                    </el-tag>
                  </div>
                </div>

                <div class="video-actions">
                  <el-button type="primary" size="large" @click="playMainVideo">
                    <el-icon><VideoPlay /></el-icon>
                    播放完整演示
                  </el-button>
                  <el-button size="large" @click="showChapterNavigation = !showChapterNavigation">
                    <el-icon><List /></el-icon>
                    章节导航
                  </el-button>
                </div>
              </div>
            </div>

            <!-- 章节导航面板 -->
            <div v-if="showChapterNavigation" class="chapter-navigation">
              <h4>
                <el-icon><List /></el-icon>
                视频章节 ({{ videoChapters.length }}个)
              </h4>
              <div class="chapters-list">
                <div
                  v-for="(chapter, index) in videoChapters"
                  :key="index"
                  class="chapter-item animate-fade-in-up"
                  :style="{ animationDelay: `${index * 0.1}s` }"
                  @click="jumpToChapter(index)"
                >
                  <div class="chapter-thumbnail">
                    <LazyImage
                      v-if="chapter.thumbnail"
                      :src="chapter.thumbnail"
                      :fallback-src="getChapterPlaceholder(index)"
                      :alt="chapter.title"
                      :hover-zoom="true"
                      class="chapter-thumb-image"
                    />
                    <div v-else class="chapter-placeholder">
                      <el-icon><VideoPlay /></el-icon>
                    </div>

                    <!-- 播放指示器 -->
                    <div class="chapter-play-indicator">
                      <el-icon><VideoPlay /></el-icon>
                    </div>
                  </div>

                  <div class="chapter-info">
                    <div class="chapter-header">
                      <span class="chapter-time">{{ chapter.time }}</span>
                      <span class="chapter-duration">{{ chapter.duration }}</span>
                    </div>
                    <h5>{{ chapter.title }}</h5>
                    <p>{{ chapter.description }}</p>
                    <div class="chapter-keypoints" v-if="chapter.keyPoints">
                      <el-tag
                        v-for="point in chapter.keyPoints"
                        :key="point"
                        size="mini"
                        type="info"
                        class="chapter-tag"
                      >
                        {{ point }}
                      </el-tag>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 技术规格展示 -->
          <div class="tech-specs-panel" v-if="mainVideoDetails.technicalSpecs">
            <h4>
              <el-icon><Setting /></el-icon>
              技术规格
            </h4>
            <div class="specs-grid">
              <div class="spec-item" v-for="(value, key) in mainVideoDetails.technicalSpecs" :key="key">
                <span class="spec-label">{{ getSpecLabel(key) }}</span>
                <span class="spec-value">{{ value }}</span>
              </div>
            </div>
          </div>
        </ParallaxSection>

        <!-- 演示场景选择 -->
        <div class="demo-scenarios-section">
          <h3>
            <el-icon><Guide /></el-icon>
            演示场景
          </h3>
          <div class="scenarios-grid">
            <div
              v-for="(scenario, key) in demoScenarios"
              :key="key"
              class="scenario-card"
              @click="startDemoScenario(key)"
            >
              <div class="scenario-header">
                <h4>{{ scenario.title }}</h4>
                <el-tag :type="getDifficultyType(scenario.difficulty)">
                  {{ scenario.difficulty }}
                </el-tag>
              </div>
              <p>{{ scenario.description }}</p>
              <div class="scenario-meta">
                <span class="scenario-time">
                  <el-icon><Clock /></el-icon>
                  {{ scenario.estimatedTime }}
                </span>
                <span class="scenario-steps">
                  <el-icon><List /></el-icon>
                  {{ scenario.steps.length }}个步骤
                </span>
              </div>
              <el-button type="primary" size="small" class="scenario-btn">
                开始演示
              </el-button>
            </div>
          </div>
        </div>

        <!-- 技术领域专门视频 -->
        <div class="domain-videos">
          <h3 class="section-title">
            <el-icon><Operation /></el-icon>
            技术领域专门演示
          </h3>
          <div class="domain-grid">
            <div
              v-for="domain in ['ai', 'bigdata', 'iot']"
              :key="domain"
              class="domain-card"
              @click="playDomainVideo(domain)"
            >
              <div class="domain-banner">
                <LazyImage
                  :src="getDomainMedia(domain).banner"
                  :fallback-src="getVideoThumbnail(`${domain}Demo`)"
                  :alt="`${domain.toUpperCase()}领域演示`"
                  :hover-zoom="true"
                  class="domain-banner-image"
                />
                <div class="domain-overlay">
                  <div class="play-button">
                    <el-icon><VideoPlay /></el-icon>
                  </div>
                  <div class="domain-badge">{{ getDomainName(domain) }}</div>
                </div>
              </div>

              <div class="domain-content">
                <h4>{{ getDomainName(domain) }}领域面试演示</h4>
                <p>专业的{{ getDomainName(domain) }}技术岗位面试流程和评测标准</p>

                <div class="domain-gallery">
                  <LazyImage
                    v-for="(galleryImage, index) in getDomainMedia(domain).gallery.slice(0, 3)"
                    :key="index"
                    :src="galleryImage"
                    :fallback-src="getFeaturePlaceholder('ai-analysis')"
                    :alt="`${domain}技术展示${index + 1}`"
                    class="gallery-thumb"
                  />
                  <div v-if="getDomainMedia(domain).gallery.length > 3" class="gallery-more">
                    +{{ getDomainMedia(domain).gallery.length - 3 }}
                  </div>
                </div>

                <div class="domain-features">
                  <el-tag
                    v-for="feature in getDomainFeatures(domain)"
                    :key="feature"
                    size="small"
                    type="primary"
                    class="domain-feature-tag"
                  >
                    {{ feature }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 分步教程 -->
        <div class="video-tutorials">
          <h3>
            <el-icon><VideoCamera /></el-icon>
            分步教程
          </h3>
          <div class="tutorials-grid">
            <div
              v-for="(video, index) in videoTutorials"
              :key="index"
              class="video-item animate-fade-in-up"
              :style="{ animationDelay: `${index * 0.15}s` }"
              @click="playVideo(video)"
            >
              <div class="video-thumbnail">
                <LazyImage
                  :src="getVideoThumbnail(video.key || `tutorial-${index}`)"
                  :fallback-src="getTutorialPlaceholder(index)"
                  :alt="video.title + '教程视频'"
                  :hover-zoom="true"
                  class="tutorial-thumbnail"
                />

                <!-- 播放按钮覆盖层 -->
                <div class="video-play-overlay">
                  <div class="play-button">
                    <el-icon><VideoPlay /></el-icon>
                  </div>
                  <div class="video-duration-badge">{{ video.duration }}</div>
                </div>

                <!-- 难度标识 -->
                <div class="difficulty-badge" :class="getDifficultyClass(video.difficulty)">
                  {{ video.difficulty }}
                </div>
              </div>

              <div class="video-info">
                <h4>{{ video.title }}</h4>
                <p class="video-description">{{ video.description }}</p>

                <div class="video-meta">
                  <span class="video-duration">
                    <el-icon><Clock /></el-icon>
                    {{ video.duration }}
                  </span>
                  <span class="video-views">
                    <el-icon><View /></el-icon>
                    {{ video.views }}次观看
                  </span>
                  <span class="video-rating">
                    <el-icon><Star /></el-icon>
                    {{ video.rating }}分
                  </span>
                </div>

                <div class="video-tags" v-if="video.tags">
                  <el-tag
                    v-for="tag in video.tags.slice(0, 3)"
                    :key="tag"
                    size="small"
                    type="primary"
                    class="video-tag"
                  >
                    {{ tag }}
                  </el-tag>
                </div>

                <!-- 视频章节预览 -->
                <div v-if="video.chapters && video.chapters.length > 0" class="video-chapters-preview">
                  <span class="chapters-count">
                    <el-icon><List /></el-icon>
                    {{ video.chapters.length }}个章节
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 交互体验 -->
      <div v-show="activeTab === 'interactive'" class="interactive-demo">
        <!-- 产品界面截图展示 -->
        <ParallaxSection class="interface-showcase" :speed="0.2">
          <template #background>
            <div class="showcase-background"></div>
          </template>

          <div class="interface-gallery">
            <h3>
              <el-icon><Picture /></el-icon>
              产品界面展示
            </h3>
            <div class="interface-grid">
              <div
                v-for="(screenshot, index) in interfaceScreenshots"
                :key="index"
                class="interface-item animate-fade-in-up"
                :style="{ animationDelay: `${index * 0.2}s` }"
              >
                <LazyImage
                  :src="screenshot.image"
                  :fallback-src="screenshot.placeholder"
                  :alt="screenshot.title"
                  :hover-zoom="true"
                  :overlay="true"
                  :overlay-title="screenshot.title"
                  :overlay-description="screenshot.description"
                  class="interface-screenshot"
                />
                <div class="interface-info">
                  <h4>{{ screenshot.title }}</h4>
                  <p>{{ screenshot.description }}</p>
                  <div class="interface-tags">
                    <el-tag
                      v-for="tag in screenshot.tags"
                      :key="tag"
                      size="small"
                      class="interface-tag"
                    >
                      {{ tag }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ParallaxSection>

        <div class="demo-simulator">
          <h3>
            <el-icon><Mouse /></el-icon>
            模拟面试体验
          </h3>
          <p>在这里您可以体验完整的面试流程，无需注册即可开始</p>

          <div class="simulator-steps">
            <div
              v-for="(step, index) in simulatorSteps"
              :key="index"
              class="step-card animate-slide-in-left"
              :style="{ animationDelay: `${index * 0.1}s` }"
              :class="{ active: currentStep === index, completed: currentStep > index }"
              @click="goToStep(index)"
            >
              <!-- 步骤截图 -->
              <div class="step-screenshot" v-if="step.screenshot">
                <LazyImage
                  :src="step.screenshot"
                  :fallback-src="getStepPlaceholder(index)"
                  :alt="step.title + '步骤截图'"
                  class="step-image"
                />
              </div>

              <div class="step-number">{{ index + 1 }}</div>
              <div class="step-content">
                <h4>{{ step.title }}</h4>
                <p>{{ step.description }}</p>

                <!-- 步骤详细信息 -->
                <div class="step-details" v-if="step.estimatedTime || step.difficulty">
                  <div class="step-meta">
                    <span v-if="step.estimatedTime" class="step-time">
                      <el-icon><Clock /></el-icon>
                      {{ step.estimatedTime }}
                    </span>
                    <span v-if="step.difficulty" class="step-difficulty">
                      <el-tag :type="getDifficultyType(step.difficulty)" size="small">
                        {{ step.difficulty }}
                      </el-tag>
                    </span>
                  </div>
                </div>

                <!-- 交互元素提示 -->
                <div v-if="step.interactiveElements && step.interactiveElements.length > 0" class="interactive-hint">
                  <el-icon><Operation /></el-icon>
                  <span>{{ step.interactiveElements.length }}个交互元素</span>
                </div>
              </div>
              <div class="step-status">
                <el-icon v-if="currentStep > index"><Check /></el-icon>
                <el-icon v-else-if="currentStep === index"><ArrowRight /></el-icon>
              </div>
            </div>
          </div>
          
          <!-- 实时反馈系统 -->
          <div class="real-time-feedback">
            <h4>
              <el-icon><TrendCharts /></el-icon>
              实时反馈系统
            </h4>
            <div class="feedback-panels">
              <div class="feedback-panel">
                <div class="panel-header">
                  <el-icon><Microphone /></el-icon>
                  <span>语音分析</span>
                </div>
                <div class="feedback-metrics">
                  <div class="metric-item">
                    <span class="metric-label">清晰度</span>
                    <el-progress :percentage="85" :show-text="false" />
                    <span class="metric-value">85%</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">语速</span>
                    <el-progress :percentage="78" :show-text="false" />
                    <span class="metric-value">适中</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">情感</span>
                    <el-progress :percentage="92" :show-text="false" />
                    <span class="metric-value">积极</span>
                  </div>
                </div>
              </div>

              <div class="feedback-panel">
                <div class="panel-header">
                  <el-icon><VideoCamera /></el-icon>
                  <span>视频分析</span>
                </div>
                <div class="feedback-metrics">
                  <div class="metric-item">
                    <span class="metric-label">眼神接触</span>
                    <el-progress :percentage="88" :show-text="false" />
                    <span class="metric-value">良好</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">表情自然度</span>
                    <el-progress :percentage="91" :show-text="false" />
                    <span class="metric-value">自然</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">姿态稳定性</span>
                    <el-progress :percentage="76" :show-text="false" />
                    <span class="metric-value">稳定</span>
                  </div>
                </div>
              </div>

              <div class="feedback-panel">
                <div class="panel-header">
                  <el-icon><Document /></el-icon>
                  <span>内容分析</span>
                </div>
                <div class="feedback-metrics">
                  <div class="metric-item">
                    <span class="metric-label">逻辑性</span>
                    <el-progress :percentage="89" :show-text="false" />
                    <span class="metric-value">清晰</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">专业度</span>
                    <el-progress :percentage="94" :show-text="false" />
                    <span class="metric-value">专业</span>
                  </div>
                  <div class="metric-item">
                    <span class="metric-label">完整性</span>
                    <el-progress :percentage="82" :show-text="false" />
                    <span class="metric-value">完整</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 模拟面试场景 -->
          <div class="interview-scenarios">
            <h4>
              <el-icon><ChatDotRound /></el-icon>
              模拟面试场景
            </h4>
            <div class="scenarios-grid">
              <div
                v-for="scenario in interviewScenarios"
                :key="scenario.id"
                class="scenario-card"
                @click="startInterviewScenario(scenario.id)"
              >
                <div class="scenario-icon">
                  <el-icon :size="32">
                    <component :is="scenario.icon" />
                  </el-icon>
                </div>
                <div class="scenario-content">
                  <h5>{{ scenario.title }}</h5>
                  <p>{{ scenario.description }}</p>
                  <div class="scenario-features">
                    <el-tag
                      v-for="feature in scenario.features"
                      :key="feature"
                      size="small"
                      type="info"
                    >
                      {{ feature }}
                    </el-tag>
                  </div>
                </div>
                <div class="scenario-action">
                  <el-button type="primary" size="small">开始体验</el-button>
                </div>
              </div>
            </div>
          </div>

          <div class="simulator-actions">
            <el-button
              v-if="currentStep < simulatorSteps.length - 1"
              type="primary"
              @click="nextStep"
            >
              下一步
              <el-icon><ArrowRight /></el-icon>
            </el-button>
            <el-button 
              v-else
              type="success" 
              @click="startRealInterview"
            >
              开始真实面试
              <el-icon><VideoPlay /></el-icon>
            </el-button>
            <el-button @click="resetSimulator">重新开始</el-button>
          </div>
        </div>
      </div>

      <!-- 技术架构 -->
      <div v-show="activeTab === 'architecture'" class="architecture-demo">
        <!-- 系统架构图 -->
        <ParallaxSection class="architecture-overview" :speed="0.1">
          <template #background>
            <div class="architecture-background"></div>
          </template>

          <div class="architecture-header">
            <h3>
              <el-icon><Setting /></el-icon>
              {{ architectureData.title || '技术架构图' }}
            </h3>
            <p v-if="architectureData.description" class="arch-description">
              {{ architectureData.description }}
            </p>
          </div>

          <!-- 架构图表展示 -->
          <div class="architecture-visualization">
            <div class="diagram-tabs">
              <el-tabs v-model="activeDiagramTab" @tab-click="handleDiagramTabClick">
                <el-tab-pane label="系统总览" name="overview">
                  <LazyImage
                    :src="getArchitectureDiagram('overview')"
                    :fallback-src="getArchitecturePlaceholder('overview')"
                    alt="系统架构总览图"
                    :hover-zoom="true"
                    class="architecture-main-diagram"
                  />
                </el-tab-pane>
                <el-tab-pane label="数据流图" name="dataflow">
                  <LazyImage
                    :src="getArchitectureDiagram('dataflow')"
                    :fallback-src="getArchitecturePlaceholder('dataflow')"
                    alt="数据流架构图"
                    :hover-zoom="true"
                    class="architecture-main-diagram"
                  />
                </el-tab-pane>
                <el-tab-pane label="部署架构" name="deployment">
                  <LazyImage
                    :src="getArchitectureDiagram('deployment')"
                    :fallback-src="getArchitecturePlaceholder('deployment')"
                    alt="部署架构图"
                    :hover-zoom="true"
                    class="architecture-main-diagram"
                  />
                </el-tab-pane>
                <el-tab-pane label="安全架构" name="security">
                  <LazyImage
                    :src="getArchitectureDiagram('security')"
                    :fallback-src="getArchitecturePlaceholder('security')"
                    alt="安全架构图"
                    :hover-zoom="true"
                    class="architecture-main-diagram"
                  />
                </el-tab-pane>
              </el-tabs>
            </div>

            <!-- 架构图交互式热点 -->
            <div class="architecture-hotspots">
              <div
                v-for="hotspot in architectureHotspots"
                :key="hotspot.id"
                class="hotspot"
                :style="{ left: hotspot.x + '%', top: hotspot.y + '%' }"
                @click="showHotspotDetails(hotspot)"
              >
                <div class="hotspot-marker">
                  <el-icon><InfoFilled /></el-icon>
                </div>
                <div class="hotspot-tooltip">{{ hotspot.title }}</div>
              </div>
            </div>
          </div>

          <div class="architecture-diagram">
            <div
              v-for="(layer, index) in architectureData.layers"
              :key="index"
              class="arch-layer animate-fade-in-up"
              :style="{ animationDelay: `${index * 0.2}s` }"
              :class="layer.name.toLowerCase().replace(/[^a-z]/g, '')"
            >
              <!-- 层级图标 -->
              <div class="layer-icon">
                <LazyImage
                  :src="getTechIcon(layer.name)"
                  :fallback-src="getTechIconPlaceholder(layer.name)"
                  :alt="layer.name + '技术栈图标'"
                  class="tech-icon"
                />
              </div>

              <div class="layer-content">
                <h4>{{ layer.name }}</h4>
                <p class="layer-description">{{ layer.description }}</p>
                <div class="tech-stack">
                  <div
                    v-for="tech in layer.technologies"
                    :key="tech"
                    class="tech-item"
                  >
                    <div class="tech-icon-small">
                      <LazyImage
                        :src="getTechIcon(tech)"
                        :fallback-src="getTechIconPlaceholder(tech)"
                        :alt="tech + '图标'"
                        class="tech-small-icon"
                      />
                    </div>
                    <span class="tech-name">{{ tech }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="architecture-features">
            <div
              v-for="(feature, index) in architectureData.features"
              :key="index"
              class="feature-highlight"
            >
              <el-icon><component :is="getFeatureIcon(feature.name)" /></el-icon>
              <h4>{{ feature.name }}</h4>
              <p>{{ feature.description }}</p>
              <div v-if="feature.metrics" class="feature-metrics">
                <span
                  v-for="(value, key) in feature.metrics"
                  :key="key"
                  class="metric-item"
                >
                  {{ key }}: {{ value }}
                </span>
              </div>
            </div>
          </div>

          <!-- 技术规格 -->
          <div v-if="architectureData.specifications" class="tech-specifications">
            <h4>技术规格</h4>
            <div class="spec-grid">
              <div
                v-for="(spec, index) in architectureData.specifications"
                :key="index"
                class="spec-item"
              >
                <strong>{{ spec.category }}</strong>
                <ul>
                  <li v-for="detail in spec.details" :key="detail">{{ detail }}</li>
                </ul>
              </div>
            </div>
          </div>
        </ParallaxSection>
      </div>
    </div>

    <!-- 返回按钮 -->
    <div class="demo-footer">
      <el-button @click="goBack">
        <el-icon><ArrowLeft /></el-icon>
        返回首页
      </el-button>
      <el-button type="primary" @click="startInterview">
        <el-icon><VideoPlay /></el-icon>
        开始面试
      </el-button>
    </div>

    <!-- 功能演示对话框 -->
    <el-dialog
      v-model="showFeatureDialog"
      :title="selectedFeature?.title"
      width="90%"
      center
      :show-close="true"
    >
      <FeatureShowcase
        v-if="selectedFeature"
        :feature="selectedFeature"
        @start-demo="handleStartDemo"
        @try-feature="handleTryFeature"
        @view-details="handleViewDetails"
      />
    </el-dialog>

    <!-- 增强的视频播放对话框 -->
    <el-dialog
      v-model="showVideoDialog"
      :title="selectedVideo?.title"
      width="95%"
      center
      class="video-dialog"
    >
      <div class="video-player-dialog">
        <!-- 主视频播放区域 -->
        <div class="video-main-area">
          <!-- 实际的HTML5视频播放器 -->
          <div class="video-player-container-main">
            <video
              ref="videoPlayer"
              class="main-video-player"
              :src="selectedVideo?.url"
              :poster="selectedVideo?.poster || selectedVideo?.thumbnail"
              controls
              preload="metadata"
              @loadedmetadata="onVideoLoaded"
              @timeupdate="onTimeUpdate"
              @play="onVideoPlay"
              @pause="onVideoPause"
              @ended="onVideoEnded"
            >
              <source :src="selectedVideo?.url" type="video/mp4">
              您的浏览器不支持视频播放。
            </video>

            <!-- 视频加载状态指示器 -->
            <div class="video-loading-overlay" v-if="!videoLoaded">
              <div class="loading-spinner">
                <el-icon class="animate-pulse"><Loading /></el-icon>
              </div>
              <p>视频加载中...</p>
            </div>

            <!-- 视频控制覆盖层 -->
            <div class="video-controls-overlay" v-if="!isVideoPlaying && videoLoaded">
              <div class="video-info-overlay animate-fade-in-up">
                <h3>{{ selectedVideo?.title }}</h3>
                <p>{{ selectedVideo?.description }}</p>
                <el-button type="primary" size="large" class="play-button animate-pulse" @click="startVideoPlayback">
                  <el-icon><VideoPlay /></el-icon>
                  开始播放
                </el-button>
              </div>
            </div>
          </div>

          <!-- 视频详细信息 -->
          <div class="video-details" v-if="selectedVideo">
              <div class="video-stats-row">
                <span class="stat-badge">
                  <el-icon><Clock /></el-icon>
                  {{ selectedVideo.duration }}
                </span>
                <span class="stat-badge">
                  <el-icon><View /></el-icon>
                  {{ selectedVideo.views }}次观看
                </span>
                <span class="stat-badge">
                  <el-icon><Star /></el-icon>
                  {{ selectedVideo.rating }}分
                </span>
                <el-tag v-if="selectedVideo.difficulty" :type="getDifficultyType(selectedVideo.difficulty)">
                  {{ selectedVideo.difficulty }}
                </el-tag>
              </div>

              <!-- 技术特性 -->
              <div class="video-features" v-if="selectedVideo.features">
                <h4>核心特性</h4>
                <div class="features-list">
                  <div
                    v-for="feature in selectedVideo.features"
                    :key="feature.name"
                    class="feature-item"
                    :class="{ highlight: feature.highlight }"
                  >
                    <strong>{{ feature.name }}</strong>
                    <span>{{ feature.description }}</span>
                  </div>
                </div>
              </div>

              <!-- 技术规格 -->
              <div class="video-tech-specs" v-if="selectedVideo.technicalSpecs">
                <h4>技术规格</h4>
                <div class="tech-specs-compact">
                  <div v-for="(value, key) in selectedVideo.technicalSpecs" :key="key" class="spec-row">
                    <span class="spec-key">{{ getSpecLabel(key) }}</span>
                    <span class="spec-val">{{ value }}</span>
                  </div>
                </div>
              </div>
            </div>

          <!-- 视频控制按钮 -->
          <div class="video-controls">
            <el-button size="large" v-if="selectedVideo?.chapters" @click="showChapterNavigation = !showChapterNavigation">
              <el-icon><List /></el-icon>
              {{ showChapterNavigation ? '隐藏章节' : '显示章节' }}
            </el-button>
            <el-button size="large" @click="resetVideoPlayer">
              <el-icon><Setting /></el-icon>
              重置播放器
            </el-button>
          </div>
        </div>

        <!-- 章节导航侧边栏 -->
        <div v-if="showChapterNavigation && selectedVideo?.chapters" class="video-chapters-sidebar">
          <h4>
            <el-icon><List /></el-icon>
            视频章节
          </h4>
          <div class="chapters-compact-list">
            <div
              v-for="(chapter, index) in selectedVideo.chapters"
              :key="index"
              class="chapter-compact-item"
              @click="jumpToChapter(index)"
            >
              <div class="chapter-time-badge">{{ chapter.time }}</div>
              <div class="chapter-content">
                <h5>{{ chapter.title }}</h5>
                <p>{{ chapter.description }}</p>
                <div class="chapter-tags" v-if="chapter.keyPoints">
                  <el-tag
                    v-for="point in chapter.keyPoints.slice(0, 2)"
                    :key="point"
                    size="mini"
                    type="info"
                  >
                    {{ point }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 演示场景对话框 -->
    <el-dialog
      v-model="showScenarioDialog"
      :title="selectedScenario?.title || '演示场景'"
      width="80%"
      :before-close="() => { showScenarioDialog = false }"
    >
      <div v-if="selectedScenario" class="scenario-dialog-content">
        <!-- 场景信息 -->
        <div class="scenario-header">
          <div class="scenario-meta">
            <el-tag :type="getDifficultyType(selectedScenario.difficulty)" size="large">
              {{ selectedScenario.difficulty }}
            </el-tag>
            <span class="scenario-time">
              <el-icon><Clock /></el-icon>
              预计时间: {{ selectedScenario.estimatedTime }}
            </span>
          </div>
          <p class="scenario-description">{{ selectedScenario.description }}</p>
        </div>

        <!-- 演示步骤 -->
        <div class="scenario-steps">
          <h4>
            <el-icon><List /></el-icon>
            演示步骤 ({{ selectedScenario.steps?.length || 0 }}步)
          </h4>
          <div class="steps-list">
            <div
              v-for="(step, index) in selectedScenario.steps"
              :key="step.id"
              class="step-item"
              :class="{
                active: index === scenarioProgress,
                completed: index < scenarioProgress
              }"
            >
              <div class="step-number">{{ index + 1 }}</div>
              <div class="step-content">
                <h5>{{ step.title }}</h5>
                <p>{{ step.description }}</p>
                <div class="step-meta">
                  <span class="step-duration">
                    <el-icon><Clock /></el-icon>
                    {{ step.duration }}
                  </span>
                  <div class="step-actions">
                    <el-tag
                      v-for="action in step.actions"
                      :key="action"
                      size="small"
                      type="info"
                    >
                      {{ action }}
                    </el-tag>
                  </div>
                </div>
                <div v-if="step.tips" class="step-tips">
                  <el-icon><Guide /></el-icon>
                  <span>{{ step.tips }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 控制按钮 -->
        <div class="scenario-controls">
          <el-button
            v-if="scenarioProgress > 0"
            @click="scenarioProgress--"
            :icon="ArrowLeft"
          >
            上一步
          </el-button>
          <el-button
            v-if="scenarioProgress < (selectedScenario.steps?.length || 0) - 1"
            type="primary"
            @click="scenarioProgress++"
            :icon="ArrowRight"
          >
            下一步
          </el-button>
          <el-button
            v-if="scenarioProgress >= (selectedScenario.steps?.length || 0) - 1"
            type="success"
            @click="completeScenario"
            :icon="Check"
          >
            完成演示
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 演示步骤详细对话框 -->
    <el-dialog
      v-model="showDemoStepsDialog"
      :title="`${selectedFeature?.title} - 演示步骤`"
      width="85%"
      center
      class="demo-steps-dialog"
    >
      <div v-if="selectedFeature" class="demo-steps-content">
        <!-- 进度指示器 -->
        <div class="steps-progress">
          <el-progress
            :percentage="demoStepsProgress"
            :stroke-width="10"
            :text-inside="true"
            status="success"
          />
          <p class="progress-info">
            步骤 {{ currentDemoStep + 1 }} / {{ selectedFeature.demoSteps.length }}
            - {{ selectedFeature.demoSteps[currentDemoStep]?.title }}
          </p>
        </div>

        <!-- 当前步骤详情 -->
        <div class="current-step-detail" v-if="selectedFeature.demoSteps[currentDemoStep]">
          <div class="step-header">
            <div class="step-number-large">{{ currentDemoStep + 1 }}</div>
            <div class="step-info">
              <h3>{{ selectedFeature.demoSteps[currentDemoStep].title }}</h3>
              <p class="step-description">{{ selectedFeature.demoSteps[currentDemoStep].description }}</p>

              <div class="step-meta" v-if="selectedFeature.demoSteps[currentDemoStep].duration || selectedFeature.demoSteps[currentDemoStep].tips">
                <span v-if="selectedFeature.demoSteps[currentDemoStep].duration" class="step-duration">
                  <el-icon><Clock /></el-icon>
                  {{ selectedFeature.demoSteps[currentDemoStep].duration }}
                </span>
                <span v-if="selectedFeature.demoSteps[currentDemoStep].interactiveElements" class="step-elements">
                  <el-icon><Operation /></el-icon>
                  {{ selectedFeature.demoSteps[currentDemoStep].interactiveElements.length }}个交互元素
                </span>
              </div>
            </div>
          </div>

          <!-- 步骤截图 -->
          <div class="step-screenshot-large">
            <LazyImage
              :src="selectedFeature.demoSteps[currentDemoStep].screenshot || getStepScreenshot(currentDemoStep + 1, selectedFeature.id)"
              :fallback-src="getStepPlaceholder(currentDemoStep + 1, selectedFeature.id)"
              :alt="selectedFeature.demoSteps[currentDemoStep].title + '演示截图'"
              class="step-image-large"
              :hover-zoom="true"
            />
          </div>

          <!-- 步骤提示 -->
          <div v-if="selectedFeature.demoSteps[currentDemoStep].tips" class="step-tips">
            <el-alert
              :title="selectedFeature.demoSteps[currentDemoStep].tips"
              type="info"
              :closable="false"
              show-icon
            />
          </div>

          <!-- 交互元素列表 -->
          <div v-if="selectedFeature.demoSteps[currentDemoStep].interactiveElements" class="interactive-elements">
            <h4>交互元素</h4>
            <div class="elements-list">
              <el-tag
                v-for="element in selectedFeature.demoSteps[currentDemoStep].interactiveElements"
                :key="element"
                type="primary"
                size="small"
                class="element-tag"
              >
                {{ element }}
              </el-tag>
            </div>
          </div>
        </div>

        <!-- 步骤导航 -->
        <div class="steps-navigation">
          <el-button
            @click="prevDemoStep"
            :disabled="currentDemoStep === 0"
            size="large"
          >
            <el-icon><ArrowLeft /></el-icon>
            上一步
          </el-button>

          <div class="steps-dots">
            <span
              v-for="(step, index) in selectedFeature.demoSteps"
              :key="index"
              class="step-dot"
              :class="{ active: index === currentDemoStep, completed: index < currentDemoStep }"
              @click="goToDemoStep(index)"
            >
              {{ index + 1 }}
            </span>
          </div>

          <el-button
            v-if="currentDemoStep < selectedFeature.demoSteps.length - 1"
            @click="nextDemoStep"
            type="primary"
            size="large"
          >
            下一步
            <el-icon><ArrowRight /></el-icon>
          </el-button>

          <el-button
            v-else
            @click="completeDemoSteps"
            type="success"
            size="large"
          >
            完成演示
            <el-icon><Check /></el-icon>
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 功能详情对话框 -->
    <el-dialog
      v-model="showFeatureDetailsDialog"
      :title="`${selectedFeature?.title} - 详细信息`"
      width="90%"
      center
      class="feature-details-dialog"
    >
      <div v-if="selectedFeature" class="feature-details-content">
        <!-- 功能概述 -->
        <div class="feature-overview">
          <div class="feature-header">
            <div class="feature-icon-large">
              <el-icon :size="80">
                <component :is="selectedFeature.icon" />
              </el-icon>
            </div>
            <div class="feature-basic-info">
              <h2>{{ selectedFeature.title }}</h2>
              <p class="feature-description">{{ selectedFeature.description }}</p>

              <div class="feature-tags">
                <el-tag v-if="selectedFeature.category" type="primary" size="large">{{ selectedFeature.category }}</el-tag>
                <el-tag v-if="selectedFeature.difficulty" :type="getDifficultyType(selectedFeature.difficulty)" size="large">
                  {{ selectedFeature.difficulty }}
                </el-tag>
                <el-tag v-if="selectedFeature.estimatedTime" type="info" size="large">
                  <el-icon><Clock /></el-icon>
                  {{ selectedFeature.estimatedTime }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>

        <!-- 核心亮点 -->
        <div class="feature-highlights">
          <h3>核心亮点</h3>
          <div class="highlights-grid">
            <div
              v-for="(highlight, index) in selectedFeature.highlights"
              :key="index"
              class="highlight-card"
            >
              <el-icon><Star /></el-icon>
              <span>{{ highlight }}</span>
            </div>
          </div>
        </div>

        <!-- 技术规格 -->
        <div v-if="selectedFeature.technicalSpecs" class="technical-specs">
          <h3>技术规格</h3>
          <div class="specs-grid">
            <div
              v-for="(value, key) in selectedFeature.technicalSpecs"
              :key="key"
              class="spec-item"
            >
              <span class="spec-label">{{ getSpecLabel(key) }}</span>
              <span class="spec-value">{{ value }}</span>
            </div>
          </div>
        </div>

        <!-- 使用场景 -->
        <div class="use-cases">
          <h3>使用场景</h3>
          <div class="cases-list">
            <div class="case-item">
              <h4>企业招聘</h4>
              <p>为企业提供标准化、客观的面试评估，提高招聘效率和质量</p>
            </div>
            <div class="case-item">
              <h4>教育培训</h4>
              <p>帮助学生和求职者提升面试技能，获得专业的反馈和建议</p>
            </div>
            <div class="case-item">
              <h4>人才评估</h4>
              <p>为人力资源部门提供科学的人才评估工具和数据支持</p>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="details-actions">
          <el-button @click="showFeatureDetailsDialog = false" size="large">关闭</el-button>
          <el-button @click="handleStartDemo(selectedFeature)" type="primary" size="large">
            <el-icon><VideoPlay /></el-icon>
            开始演示
          </el-button>
          <el-button @click="handleTryFeature(selectedFeature)" type="success" size="large">
            <el-icon><Operation /></el-icon>
            立即体验
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  VideoPlay, VideoCamera, Star, Mouse, Setting, Operation,
  Check, ArrowRight, ArrowLeft, Clock, View, List,
  Calendar, Guide, Loading, Picture
} from '@element-plus/icons-vue'
import DemoService from '../services/demoService.js'
import MediaService from '../services/mediaService.js'
import FeatureShowcase from '../components/Demo/FeatureShowcase.vue'
import ParticleBackground from '../components/Demo/ParticleBackground.vue'
import LazyImage from '../components/Demo/LazyImage.vue'
import ParallaxSection from '../components/Demo/ParallaxSection.vue'

const router = useRouter()

// 响应式数据
const activeTab = ref('features')
const currentStep = ref(0)
const showFeatureDialog = ref(false)
const showVideoDialog = ref(false)
const showScenarioDialog = ref(false)
const showDemoStepsDialog = ref(false)
const showFeatureDetailsDialog = ref(false)
const selectedFeature = ref(null)
const selectedVideo = ref(null)
const selectedScenario = ref(null)
const currentDemoStep = ref(0)
const demoStepsProgress = ref(0)
const demoStats = ref({})

// 新增的演示功能数据
const showChapterNavigation = ref(false)
const mainVideoDetails = ref({})
const videoChapters = ref([])
const demoScenarios = ref({})
const currentScenario = ref(null)
const scenarioProgress = ref(0)

// 视频播放相关数据
const videoPlayer = ref(null)
const isVideoPlaying = ref(false)
const currentVideoTime = ref(0)
const videoDuration = ref(0)
const videoLoaded = ref(false)

// 从服务获取数据
const features = ref([])
const videoTutorials = ref([])
const simulatorSteps = ref([])
const architectureData = ref({})

// 初始化数据
onMounted(() => {
  loadDemoData()
})

const loadDemoData = () => {
  // 从服务加载演示数据
  const featuresData = DemoService.getFeatures()
  features.value = featuresData.map(feature => ({
    icon: feature.icon,
    title: feature.title,
    description: feature.description,
    demoDescription: feature.highlights.join('、'),
    id: feature.id,
    highlights: feature.highlights,
    demoSteps: feature.demoSteps,
    category: feature.category,
    difficulty: feature.difficulty,
    estimatedTime: feature.estimatedTime,
    technicalSpecs: feature.technicalSpecs
  }))

  const videosData = DemoService.getVideos()
  videoTutorials.value = videosData || []

  const stepsData = DemoService.getInteractiveSteps()
  simulatorSteps.value = stepsData.map(step => ({
    title: step.title,
    description: step.description,
    id: step.id,
    tips: step.tips,
    mockData: step.mockData,
    estimatedTime: step.estimatedTime,
    difficulty: step.difficulty,
    interactiveElements: step.interactiveElements
  }))

  architectureData.value = DemoService.getArchitecture()
  demoStats.value = DemoService.getDemoStats()

  // 加载新的演示功能数据
  mainVideoDetails.value = DemoService.getMainVideoDetails()
  videoChapters.value = DemoService.getVideoChapters('main-demo')
  demoScenarios.value = DemoService.getDemoScenarios()

  console.log('演示场景数据加载完成:', demoScenarios.value)
  console.log('演示场景数量:', Object.keys(demoScenarios.value || {}).length)

  // 初始化媒体资源
  initializeMediaResources()
}

// 媒体资源相关方法
const initializeMediaResources = () => {
  // 为功能添加标签
  features.value = features.value.map(feature => ({
    ...feature,
    key: feature.id,
    tags: ['AI技术', '智能分析', '实时处理']
  }))

  // 为视频教程添加标签
  videoTutorials.value = videoTutorials.value.map((video, index) => ({
    ...video,
    key: `tutorial-${index}`,
    tags: ['教程', '演示', '操作指南']
  }))

  // 为模拟器步骤添加截图
  simulatorSteps.value = simulatorSteps.value.map((step, index) => ({
    ...step,
    screenshot: getStepScreenshot(index)
  }))
}

// 获取功能图片
const getFeatureImage = (featureKey) => {
  const placeholderMedia = MediaService.getPlaceholderMedia()
  return placeholderMedia.features[featureKey]?.image ||
         MediaService.createUnsplashImage('technology,ai,interface', 400, 300)
}

// 获取功能图片库
const getFeatureGallery = (featureKey) => {
  return MediaService.getFeatureGallery(featureKey)
}

// 获取功能占位符
const getFeaturePlaceholder = (featureKey) => {
  return MediaService.createPlaceholder(400, 300, '功能演示', '1890ff')
}

// 获取视频缩略图
const getVideoThumbnail = (videoKey) => {
  const placeholderMedia = MediaService.getPlaceholderMedia()
  return placeholderMedia.videos[videoKey] || placeholderMedia.videos.mainThumbnail ||
         MediaService.createUnsplashImage('video,demo,technology', 800, 450)
}

// 获取章节缩略图
const getChapterThumbnail = (chapterKey) => {
  return MediaService.getChapterThumbnail(chapterKey)
}

// 获取视频占位符
const getVideoPlaceholder = (videoKey) => {
  return MediaService.createPlaceholder(800, 450, '视频演示', '667eea')
}

// 获取教程占位符
const getTutorialPlaceholder = (index) => {
  const colors = ['1890ff', '52c41a', 'faad14', 'ff4d4f', '722ed1', '13c2c2']
  const color = colors[index % colors.length]
  return MediaService.createPlaceholder(400, 225, `教程 ${index + 1}`, color)
}

// 获取章节占位符
const getChapterPlaceholder = (index) => {
  return MediaService.createPlaceholder(120, 68, `章节 ${index + 1}`, '40a9ff')
}

// 获取步骤截图
const getStepScreenshot = (index) => {
  return MediaService.getInterfaceScreenshot('dashboard')
}

// 获取步骤占位符
const getStepPlaceholder = (index) => {
  return MediaService.createPlaceholder(300, 200, `步骤 ${index + 1}`, 'b37feb')
}

// 获取架构图表
const getArchitectureDiagram = (type) => {
  const placeholderMedia = MediaService.getPlaceholderMedia()
  return placeholderMedia.architecture[type] ||
         MediaService.createUnsplashImage('architecture,diagram,system', 800, 600)
}

// 获取架构占位符
const getArchitecturePlaceholder = (type) => {
  return MediaService.createPlaceholder(800, 600, '架构图表', '667eea')
}

// 获取技术图标
const getTechIcon = (techName) => {
  return MediaService.getTechStackIcon(techName)
}

// 获取技术图标占位符
const getTechIconPlaceholder = (techName) => {
  return MediaService.createPlaceholder(48, 48, techName.charAt(0), 'f0f0f0', '666666')
}

// 获取技术领域媒体资源
const getDomainMedia = (domain) => {
  return MediaService.getDomainMedia(domain)
}

// 获取界面截图
const getInterfaceScreenshot = (screenType) => {
  return MediaService.getInterfaceScreenshot(screenType)
}

// 获取技术领域名称
const getDomainName = (domain) => {
  const domainNames = {
    ai: '人工智能',
    bigdata: '大数据',
    iot: '物联网'
  }
  return domainNames[domain] || domain.toUpperCase()
}

// 获取技术领域特性
const getDomainFeatures = (domain) => {
  const domainFeatures = {
    ai: ['机器学习', '深度学习', '神经网络', '算法优化'],
    bigdata: ['数据处理', '分布式计算', '实时分析', '数据挖掘'],
    iot: ['嵌入式开发', '传感器', '边缘计算', '无线通信']
  }
  return domainFeatures[domain] || []
}

// 播放技术领域视频
const playDomainVideo = (domain) => {
  const domainVideo = {
    ai: {
      title: '人工智能领域面试演示',
      description: 'AI技术岗位的专业面试流程和评测标准',
      duration: '8:45',
      url: '/demo/videos/ai-domain.mp4',
      thumbnail: getDomainMedia(domain).banner
    },
    bigdata: {
      title: '大数据领域面试演示',
      description: '大数据技术岗位的专业面试和技能评测',
      duration: '7:52',
      url: '/demo/videos/bigdata-domain.mp4',
      thumbnail: getDomainMedia(domain).banner
    },
    iot: {
      title: '物联网领域面试演示',
      description: 'IoT技术岗位的专业面试和能力评估',
      duration: '6:33',
      url: '/demo/videos/iot-domain.mp4',
      thumbnail: getDomainMedia(domain).banner
    }
  }

  selectedVideo.value = domainVideo[domain]
  showVideoDialog.value = true
}

// 获取难度类型样式
const getDifficultyType = (difficulty) => {
  const types = {
    '简单': 'success',
    '中等': 'warning',
    '困难': 'danger',
    '入门': 'success',
    '初级': 'info',
    '中级': 'warning',
    '高级': 'danger',
    '专家': 'danger'
  }
  return types[difficulty] || 'info'
}

// 获取难度样式类
const getDifficultyClass = (difficulty) => {
  const classes = {
    '简单': 'difficulty-easy',
    '中等': 'difficulty-medium',
    '困难': 'difficulty-hard',
    '入门': 'difficulty-beginner'
  }
  return classes[difficulty] || 'difficulty-beginner'
}

// 界面截图数据
const interfaceScreenshots = ref([
  {
    title: '面试主界面',
    description: '清晰直观的面试操作界面',
    image: MediaService.createUnsplashImage('interview,interface,dashboard', 600, 400),
    placeholder: MediaService.createPlaceholder(600, 400, '面试界面', '1890ff'),
    tags: ['主界面', '用户体验', '交互设计']
  },
  {
    title: 'AI分析结果',
    description: '智能分析结果可视化展示',
    image: MediaService.createUnsplashImage('analytics,charts,ai', 600, 400),
    placeholder: MediaService.createPlaceholder(600, 400, 'AI分析', '52c41a'),
    tags: ['数据分析', '可视化', 'AI技术']
  },
  {
    title: '综合报告',
    description: '详细的评估报告和建议',
    image: MediaService.createUnsplashImage('report,document,analysis', 600, 400),
    placeholder: MediaService.createPlaceholder(600, 400, '综合报告', 'faad14'),
    tags: ['报告生成', '评估结果', '数据展示']
  },
  {
    title: '学习路径',
    description: '个性化的学习建议和路径规划',
    image: MediaService.createUnsplashImage('learning,path,education', 600, 400),
    placeholder: MediaService.createPlaceholder(600, 400, '学习路径', '722ed1'),
    tags: ['个性化', '学习规划', '智能推荐']
  }
])

// 面试场景数据
const interviewScenarios = ref([
  {
    id: 'ai-interview',
    title: 'AI算法工程师面试',
    description: '深度学习、机器学习算法相关技术面试',
    icon: 'TrendCharts',
    features: ['算法原理', '代码实现', '项目经验', '优化思路']
  },
  {
    id: 'frontend-interview',
    title: '前端开发工程师面试',
    description: 'Vue.js、React、JavaScript等前端技术面试',
    icon: 'Monitor',
    features: ['框架原理', '性能优化', '工程化', '用户体验']
  },
  {
    id: 'backend-interview',
    title: '后端开发工程师面试',
    description: 'Java、Python、数据库、微服务等后端技术面试',
    icon: 'Server',
    features: ['系统设计', '数据库', '并发处理', '架构设计']
  },
  {
    id: 'product-interview',
    title: '产品经理面试',
    description: '产品设计、用户研究、商业分析等产品岗位面试',
    icon: 'Briefcase',
    features: ['产品思维', '用户体验', '数据分析', '项目管理']
  }
])

// 架构图相关数据
const activeDiagramTab = ref('overview')
const architectureHotspots = ref([
  {
    id: 'frontend',
    title: '前端应用层',
    x: 20,
    y: 15,
    description: 'Vue.js + Element Plus构建的响应式前端界面',
    technologies: ['Vue.js 3', 'Element Plus', 'Vite', 'TypeScript']
  },
  {
    id: 'api-gateway',
    title: 'API网关',
    x: 50,
    y: 30,
    description: '统一的API入口，负责路由、认证和限流',
    technologies: ['FastAPI', 'JWT认证', '限流控制', 'CORS处理']
  },
  {
    id: 'ai-engine',
    title: 'iFlytek AI引擎',
    x: 75,
    y: 45,
    description: '核心AI分析引擎，提供多模态智能分析',
    technologies: ['iFlytek星火', '语音识别', '图像分析', 'NLP处理']
  },
  {
    id: 'database',
    title: '数据存储层',
    x: 30,
    y: 70,
    description: '高性能数据库集群，支持海量数据存储',
    technologies: ['PostgreSQL', 'Redis缓存', '数据备份', '读写分离']
  }
])

// 方法
const handleTabClick = (tab) => {
  console.log('切换到标签:', tab.props.name)
}

const handleDiagramTabClick = (tab) => {
  console.log('切换架构图:', tab.props.name)
}

const showHotspotDetails = (hotspot) => {
  ElMessage.info(`查看 ${hotspot.title} 详细信息`)
  // 这里可以显示详细的技术信息对话框
}

const showFeatureDemo = (feature) => {
  selectedFeature.value = feature
  showFeatureDialog.value = true
}

const showFeatureDetails = (feature) => {
  selectedFeature.value = feature
  showFeatureDetailsDialog.value = true
  ElMessage.success(`查看 ${feature.title} 详细信息`)
}

const showTechnicalSpecs = (feature) => {
  selectedFeature.value = feature
  showFeatureDetailsDialog.value = true
  // 滚动到技术规格部分
  setTimeout(() => {
    const specsElement = document.querySelector('.technical-specs')
    if (specsElement) {
      specsElement.scrollIntoView({ behavior: 'smooth', block: 'start' })
    }
  }, 300)
  ElMessage.info(`查看 ${feature.title} 技术规格`)
}

const playFeatureDemo = (feature) => {
  const result = DemoService.startFeatureDemo(feature.id)
  if (result.success) {
    ElMessage.success(result.message)
    showFeatureDemo(feature)
  } else {
    ElMessage.error(result.message)
  }
}

const tryFeature = (feature) => {
  ElMessage.info(`即将体验 ${feature.title} 功能`)
  // 根据不同功能跳转到相应页面
  switch (feature.id) {
    case 'multimodal-input':
      router.push('/interview-selection')
      break
    case 'comprehensive-report':
      // 如果有测试报告，跳转到报告页面
      router.push('/test-report-to-learning')
      break
    case 'ai-interaction':
      router.push('/interview-selection')
      break
    default:
      router.push('/interview-selection')
  }
}

const startInterviewScenario = (scenarioId) => {
  const scenario = interviewScenarios.value.find(s => s.id === scenarioId)
  if (scenario) {
    ElMessage.success(`开始 ${scenario.title} 模拟面试`)
    // 这里可以跳转到具体的面试场景
    router.push(`/interview-selection?scenario=${scenarioId}`)
  }
}

// 打开视频对话框
const openVideoDialog = () => {
  const mainVideo = {
    title: mainVideoDetails.value.title || '系统演示视频',
    url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
    poster: getVideoThumbnail('main'),
    thumbnail: getVideoThumbnail('main'),
    description: mainVideoDetails.value.description || '完整的系统功能演示'
  }
  selectedVideo.value = mainVideo
  showVideoDialog.value = true
}

const playMainVideo = () => {
  const result = DemoService.playVideo('main-demo')
  if (result.success) {
    ElMessage.success(result.message)
    selectedVideo.value = result.video
    showVideoDialog.value = true
  } else {
    ElMessage.error(result.message)
  }
}

const playVideo = (video) => {
  const result = DemoService.playVideo(video.id)
  if (result.success) {
    ElMessage.success(result.message)
    selectedVideo.value = result.video
    showVideoDialog.value = true
  } else {
    ElMessage.error(result.message)
  }
}

// 跳转到视频章节
const jumpToChapter = (chapterIndex) => {
  const result = DemoService.jumpToChapter('main-demo', chapterIndex)
  if (result.success) {
    ElMessage.success(result.message)

    // 实际的视频跳转逻辑
    if (videoPlayer.value && videoLoaded.value) {
      const timeInSeconds = parseTimeToSeconds(result.timestamp)
      videoPlayer.value.currentTime = timeInSeconds

      // 如果视频没有在播放，则开始播放
      if (!isVideoPlaying.value) {
        videoPlayer.value.play()
      }
    }
  } else {
    ElMessage.error(result.message)
  }
}

// 将时间字符串转换为秒数
const parseTimeToSeconds = (timeString) => {
  const parts = timeString.split(':')
  if (parts.length === 2) {
    return parseInt(parts[0]) * 60 + parseInt(parts[1])
  } else if (parts.length === 3) {
    return parseInt(parts[0]) * 3600 + parseInt(parts[1]) * 60 + parseInt(parts[2])
  }
  return 0
}

// 开始演示场景
const startDemoScenario = (scenarioId) => {
  const result = DemoService.startDemoScenario(scenarioId)
  if (result.success) {
    ElMessage.success(result.message)
    currentScenario.value = result.scenario
    selectedScenario.value = result.scenario
    scenarioProgress.value = 0
    showScenarioDialog.value = true
    console.log(`开始演示场景: ${result.scenario.title}`)
  } else {
    ElMessage.error(result.message)
  }
}

// 获取技术规格标签
const getSpecLabel = (key) => {
  const labels = {
    aiModel: 'AI模型',
    responseTime: '响应时间',
    accuracy: '准确率',
    supportedLanguages: '支持语言',
    concurrentUsers: '并发用户',
    dataPrivacy: '数据安全',
    deployment: '部署方式'
  }
  return labels[key] || key
}

const nextStep = () => {
  if (currentStep.value < simulatorSteps.length - 1) {
    currentStep.value++
    ElMessage.success(`进入步骤 ${currentStep.value + 1}`)
  }
}

const goToStep = (index) => {
  currentStep.value = index
  ElMessage.info(`跳转到步骤 ${index + 1}`)
}

const resetSimulator = () => {
  currentStep.value = 0
  ElMessage.info('模拟器已重置')
}

const getFeatureIcon = (featureName) => {
  const iconMap = {
    '高性能': 'Lightning',
    '安全可靠': 'Lock',
    '可扩展': 'Expand',
    '智能化': 'Star',
    '实时性': 'VideoPlay',
    '稳定性': 'Check'
  }
  return iconMap[featureName] || 'Setting'
}

const startRealInterview = () => {
  ElMessageBox.confirm(
    '您即将开始真实的面试流程，是否确认？',
    '开始面试',
    {
      confirmButtonText: '确认开始',
      cancelButtonText: '取消',
      type: 'info'
    }
  ).then(() => {
    router.push('/interview-selection')
  })
}

const goBack = () => {
  router.push('/')
}

const startInterview = () => {
  router.push('/interview-selection')
}

const playFeatureVideo = () => {
  ElMessage.success(`正在播放 ${selectedFeature.value?.title} 演示视频`)
  showFeatureDialog.value = false
}

// 完成演示场景
const completeScenario = () => {
  ElMessage.success(`演示场景 "${selectedScenario.value?.title}" 已完成！`)
  showScenarioDialog.value = false
  scenarioProgress.value = 0
  selectedScenario.value = null
}



// 视频播放控制方法
const startVideoPlayback = () => {
  if (videoPlayer.value) {
    videoPlayer.value.play()
    ElMessage.success(`开始播放: ${selectedVideo.value?.title}`)
  }
}

const onVideoLoaded = () => {
  if (videoPlayer.value) {
    videoDuration.value = videoPlayer.value.duration
    videoLoaded.value = true
    ElMessage.success('视频加载完成')
  }
}

const onTimeUpdate = () => {
  if (videoPlayer.value) {
    currentVideoTime.value = videoPlayer.value.currentTime
  }
}

const onVideoPlay = () => {
  isVideoPlaying.value = true
}

const onVideoPause = () => {
  isVideoPlaying.value = false
}

const onVideoEnded = () => {
  isVideoPlaying.value = false
  ElMessage.success('视频播放完成')
}

const resetVideoPlayer = () => {
  if (videoPlayer.value) {
    videoPlayer.value.currentTime = 0
    videoPlayer.value.pause()
    isVideoPlaying.value = false
    currentVideoTime.value = 0
    ElMessage.info('播放器已重置')
  }
}

// FeatureShowcase 组件事件处理
const handleStartDemo = (feature) => {
  selectedFeature.value = feature
  currentDemoStep.value = 0
  demoStepsProgress.value = 0
  showFeatureDialog.value = false
  showDemoStepsDialog.value = true
  ElMessage.success(`开始演示: ${feature.title}`)
}

const handleTryFeature = (feature) => {
  showFeatureDialog.value = false
  tryFeature(feature)
}

const handleViewDetails = (feature) => {
  selectedFeature.value = feature
  showFeatureDialog.value = false
  showFeatureDetailsDialog.value = true
  ElMessage.success(`查看 ${feature.title} 详细信息`)
}

// 演示步骤导航方法
const nextDemoStep = () => {
  if (selectedFeature.value && currentDemoStep.value < selectedFeature.value.demoSteps.length - 1) {
    currentDemoStep.value++
    demoStepsProgress.value = Math.round((currentDemoStep.value + 1) / selectedFeature.value.demoSteps.length * 100)
    ElMessage.success(`进入步骤 ${currentDemoStep.value + 1}`)
  }
}

const prevDemoStep = () => {
  if (currentDemoStep.value > 0) {
    currentDemoStep.value--
    demoStepsProgress.value = Math.round((currentDemoStep.value + 1) / selectedFeature.value.demoSteps.length * 100)
    ElMessage.info(`返回步骤 ${currentDemoStep.value + 1}`)
  }
}

const completeDemoSteps = () => {
  ElMessage.success(`演示完成！您已完成 "${selectedFeature.value?.title}" 的所有演示步骤`)
  showDemoStepsDialog.value = false
  currentDemoStep.value = 0
  demoStepsProgress.value = 0
}

const goToDemoStep = (stepIndex) => {
  currentDemoStep.value = stepIndex
  demoStepsProgress.value = Math.round((stepIndex + 1) / selectedFeature.value.demoSteps.length * 100)
  ElMessage.info(`跳转到步骤 ${stepIndex + 1}`)
}

// 获取步骤截图
const getStepScreenshot = (stepNumber, featureId) => {
  const keywords = getStepKeywords(stepNumber, featureId)
  return MediaService.createUnsplashImage(keywords, 600, 400)
}

// 获取步骤占位符
const getStepPlaceholder = (stepNumber, featureId) => {
  return MediaService.createPlaceholder(600, 400, `步骤 ${stepNumber}`, '1890ff')
}

// 根据功能ID和步骤获取关键词
const getStepKeywords = (stepNumber, featureId) => {
  const keywordMap = {
    'multimodal-input': {
      1: 'interface,selection,mode,technology',
      2: 'device,setup,microphone,camera',
      3: 'recording,video,audio,capture',
      4: 'processing,analysis,ai,data'
    },
    'ai-analysis': {
      1: 'artificial-intelligence,brain,analysis',
      2: 'data,processing,algorithm,machine',
      3: 'results,evaluation,metrics,score',
      4: 'report,visualization,chart,graph'
    },
    'comprehensive-report': {
      1: 'report,document,analysis,professional',
      2: 'chart,graph,visualization,data',
      3: 'recommendation,advice,improvement',
      4: 'export,download,pdf,sharing'
    }
  }

  return keywordMap[featureId]?.[stepNumber] || 'technology,demo,interface,modern'
}

// 获取技术规格标签
const getSpecLabel = (key) => {
  const labelMap = {
    'accuracy': '准确率',
    'responseTime': '响应时间',
    'supportedFormats': '支持格式',
    'maxFileSize': '最大文件大小',
    'languages': '支持语言',
    'platforms': '支持平台',
    'apiVersion': 'API版本',
    'concurrency': '并发处理',
    'storage': '存储容量',
    'bandwidth': '带宽要求',
    'security': '安全等级',
    'compliance': '合规标准'
  }

  return labelMap[key] || key
}
</script>

<style scoped>
/* ==================== CSS变量映射 ==================== */
.demo-page {
  /* 映射设计系统变量 */
  --color-primary: var(--primary-color);
  --color-primary-light: var(--primary-light);
  --color-primary-lighter: var(--primary-lighter);
  --color-primary-dark: var(--primary-dark);
  --color-success: var(--success-color);
  --color-warning: var(--warning-color);
  --color-error: var(--error-color);

  --text-primary: var(--text-primary);
  --text-secondary: var(--text-secondary);
  --text-tertiary: var(--text-tertiary);

  --bg-primary: var(--bg-primary);
  --bg-secondary: var(--bg-secondary);
  --bg-tertiary: var(--bg-tertiary);

  --border-color: var(--border-base);
  --border-light: var(--border-light);

  --gradient-primary: var(--gradient-primary);
  --gradient-tech: var(--gradient-tech);
  --gradient-warm: var(--gradient-warm);

  --spacing-xs: var(--spacing-xs);
  --spacing-sm: var(--spacing-sm);
  --spacing-md: var(--spacing-base);
  --spacing-lg: var(--spacing-lg);
  --spacing-xl: var(--spacing-xl);
  --spacing-xxl: var(--spacing-2xl);

  --border-radius-sm: var(--border-radius-sm);
  --border-radius-md: var(--border-radius-base);
  --border-radius-lg: var(--border-radius-lg);
  --border-radius-xl: var(--border-radius-xl);

  --shadow-sm: var(--shadow-sm);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);

  --transition-base: var(--transition-base);
  --transition-fast: var(--transition-fast);

  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family: var(--font-family-base);
}

/* ==================== 页面头部优化 ==================== */
.demo-header {
  background: var(--gradient-tech);
  color: white;
  padding: var(--spacing-3xl) 0 var(--spacing-2xl);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.demo-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  position: relative;
  z-index: 1;
}

.demo-title {
  font-size: clamp(2rem, 5vw, 3.5rem);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  animation: fadeInUp 0.8s ease-out;
}

.demo-subtitle {
  font-size: clamp(1rem, 3vw, 1.4rem);
  font-weight: var(--font-weight-medium);
  opacity: 0.95;
  margin-bottom: var(--spacing-xl);
  line-height: var(--line-height-relaxed);
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.demo-stats {
  display: flex;
  justify-content: center;
  gap: var(--spacing-xl);
  margin: var(--spacing-xl) 0;
  padding: var(--spacing-xl);
  background: rgba(255, 255, 255, 0.15);
  border-radius: var(--border-radius-xl);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  animation: fadeInUp 0.8s ease-out 0.4s both;
}

.stat-item {
  text-align: center;
  color: white;
  padding: var(--spacing-md);
  border-radius: var(--border-radius-lg);
  transition: transform var(--transition-base);
}

.stat-item:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.1);
}

.stat-number {
  display: block;
  font-size: clamp(1.5rem, 4vw, 2.5rem);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-xs);
  background: linear-gradient(45deg, #fff, #e3f2fd);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  font-size: var(--font-size-sm);
  opacity: 0.9;
  font-weight: var(--font-weight-medium);
}

.demo-tabs {
  margin-top: var(--spacing-xl);
  animation: fadeInUp 0.8s ease-out 0.6s both;
}

.demo-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-xl);
}

/* ==================== 功能演示样式优化 ==================== */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--spacing-xl);
  margin-top: var(--spacing-xl);
  padding: var(--spacing-lg) 0;
}

.feature-card {
  background: linear-gradient(145deg, #ffffff, #f8fafc);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-2xl);
  text-align: center;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all var(--transition-base);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
  transform: scaleX(0);
  transition: transform var(--transition-base);
}

.feature-card:hover::before {
  transform: scaleX(1);
}

.feature-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  background: linear-gradient(145deg, #ffffff, #f1f5f9);
}

.feature-icon {
  color: var(--color-primary);
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, var(--color-primary-light), var(--color-primary));
  border-radius: var(--border-radius-full);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 16px rgba(24, 144, 255, 0.3);
  transition: all var(--transition-base);
}

.feature-card:hover .feature-icon {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 12px 24px rgba(24, 144, 255, 0.4);
}

.feature-icon .el-icon {
  color: white;
  font-size: 2.5rem;
}

.feature-card h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: var(--spacing-lg) 0;
  line-height: var(--line-height-tight);
}

.feature-card p {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-xl);
}

.feature-actions {
  margin-top: var(--spacing-xl);
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  flex-wrap: wrap;
}

.feature-actions .el-button {
  border-radius: var(--border-radius-lg);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-sm) var(--spacing-lg);
  transition: all var(--transition-base);
}

.feature-actions .el-button--primary {
  background: var(--gradient-primary);
  border: none;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.feature-actions .el-button--primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
}

.feature-actions .el-button--success {
  background: linear-gradient(135deg, var(--el-color-success), var(--el-color-success-light-3));
  border: none;
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
}

.feature-actions .el-button--success:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(103, 194, 58, 0.4);
}

.feature-actions .el-button--info {
  background: linear-gradient(135deg, var(--el-color-info), var(--el-color-info-light-3));
  border: none;
  box-shadow: 0 4px 12px rgba(144, 147, 153, 0.3);
}

.feature-actions .el-button--info:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(144, 147, 153, 0.4);
}

.feature-actions .el-button--warning {
  background: linear-gradient(135deg, var(--el-color-warning), var(--el-color-warning-light-3));
  border: none;
  box-shadow: 0 4px 12px rgba(230, 162, 60, 0.3);
}

.feature-actions .el-button--warning:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(230, 162, 60, 0.4);
}

/* ==================== 视频演示样式优化 ==================== */
.video-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-2xl);
  margin-top: var(--spacing-xl);
  align-items: start;
}

.main-video-section {
  background: linear-gradient(145deg, #ffffff, #f8fafc);
  border-radius: var(--border-radius-2xl);
  padding: var(--spacing-2xl);
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.video-player-container {
  position: relative;
  border-radius: var(--border-radius-xl);
  overflow: hidden;
  background: #000;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.video-player {
  background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
  border-radius: var(--border-radius-xl);
  overflow: hidden;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  aspect-ratio: 16 / 9;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.video-placeholder {
  padding: var(--spacing-4xl);
  text-align: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.video-placeholder::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  opacity: 0.3;
}

.video-placeholder > * {
  position: relative;
  z-index: 1;
}

.video-placeholder .el-icon {
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-xl);
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-full);
  backdrop-filter: blur(10px);
}

.video-placeholder h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-lg);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.video-placeholder p {
  font-size: var(--font-size-lg);
  opacity: 0.9;
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-xl);
}

.video-list {
  background: linear-gradient(145deg, #ffffff, #f8fafc);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-xl);
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.video-list h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.video-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  cursor: pointer;
  transition: all var(--transition-base);
  margin-bottom: var(--spacing-sm);
  border: 1px solid transparent;
  background: rgba(255, 255, 255, 0.5);
}

.video-item:hover {
  background: linear-gradient(145deg, #f1f5f9, #e2e8f0);
  transform: translateX(4px);
  border-color: var(--color-primary-light);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

.video-item .el-icon {
  color: var(--color-primary);
  font-size: 1.5rem;
  padding: var(--spacing-sm);
  background: var(--color-primary-light);
  border-radius: var(--border-radius-lg);
  margin-right: var(--spacing-md);
}

.video-info {
  flex: 1;
}

.video-info h4 {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  line-height: var(--line-height-tight);
}

.video-info p {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: var(--line-height-base);
}

/* ==================== 视频标签和元数据优化 ==================== */
.video-tags {
  margin: var(--spacing-md) 0;
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.video-tags .el-tag {
  border-radius: var(--border-radius-lg);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-xs) var(--spacing-md);
  border: none;
  background: linear-gradient(135deg, var(--color-primary-light), var(--color-primary));
  color: white;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
  transition: all var(--transition-base);
  font-size: var(--font-size-sm);
}

.video-tags .el-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

.video-meta {
  display: flex;
  gap: var(--spacing-lg);
  align-items: center;
  flex-wrap: wrap;
  margin-top: var(--spacing-md);
  padding: var(--spacing-md);
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--border-radius-lg);
  backdrop-filter: blur(10px);
}

.video-duration,
.video-views,
.video-rating {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  padding: var(--spacing-xs) var(--spacing-sm);
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-sm);
  transition: all var(--transition-base);
}

.video-duration:hover,
.video-views:hover,
.video-rating:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.video-rating {
  color: var(--color-warning);
}

.video-rating .el-icon {
  color: #ffd700;
}

.video-thumbnail {
  width: 80px;
  height: 60px;
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
  border-radius: var(--border-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-right: var(--spacing-lg);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}

.video-thumbnail::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.video-thumbnail:hover::before {
  transform: translateX(100%);
}

.video-thumbnail:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
}

.video-thumbnail .el-icon {
  font-size: 2rem;
}

/* ==================== 交互体验样式优化 ==================== */
.demo-simulator {
  background: linear-gradient(145deg, #ffffff, #f8fafc);
  border-radius: var(--border-radius-2xl);
  padding: var(--spacing-2xl);
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  margin-top: var(--spacing-xl);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.demo-simulator::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
}

.demo-simulator h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xl);
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
}

/* 实时反馈系统样式 */
.real-time-feedback {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-xl);
  margin: var(--spacing-xl) 0;
}

.real-time-feedback h4 {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
  font-size: 1.2rem;
  font-weight: 600;
}

.feedback-panels {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.feedback-panel {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.feedback-panel:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.panel-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
  color: var(--color-primary);
  font-weight: 600;
}

.feedback-metrics {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.metric-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.metric-label {
  min-width: 80px;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.metric-value {
  min-width: 50px;
  text-align: right;
  font-weight: 600;
  color: var(--color-primary);
  font-size: 0.9rem;
}

/* 面试场景样式 */
.interview-scenarios {
  background: linear-gradient(135deg, #ffffff, #f1f5f9);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-xl);
  margin: var(--spacing-xl) 0;
}

.interview-scenarios h4 {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
  font-size: 1.2rem;
  font-weight: 600;
}

.scenarios-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-lg);
}

.scenario-card {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px solid transparent;
}

.scenario-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
  border-color: var(--color-primary);
}

.scenario-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
  border-radius: var(--border-radius-lg);
  color: white;
  margin-bottom: var(--spacing-md);
}

.scenario-content h5 {
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
  font-size: 1.1rem;
  font-weight: 600;
}

.scenario-content p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
  line-height: 1.5;
}

.scenario-features {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-md);
}

.scenario-action {
  text-align: center;
}

/* 架构图交互式样式 */
.architecture-visualization {
  position: relative;
  margin: var(--spacing-xl) 0;
}

.diagram-tabs {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.architecture-hotspots {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.hotspot {
  position: absolute;
  pointer-events: all;
  cursor: pointer;
}

.hotspot-marker {
  width: 24px;
  height: 24px;
  background: var(--color-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  animation: pulse 2s infinite;
}

.hotspot-marker:hover {
  transform: scale(1.2);
  background: var(--color-primary-dark);
}

.hotspot-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: 0.8rem;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  margin-bottom: var(--spacing-xs);
}

.hotspot:hover .hotspot-tooltip {
  opacity: 1;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(24, 144, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
  }
}

.simulator-steps {
  margin: var(--spacing-xl) 0;
  position: relative;
}

.simulator-steps::before {
  content: '';
  position: absolute;
  left: 20px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, var(--color-primary), var(--color-primary-light));
  z-index: 1;
}

.step-card {
  display: flex;
  align-items: center;
  padding: var(--spacing-xl);
  border: 2px solid transparent;
  border-radius: var(--border-radius-xl);
  margin-bottom: var(--spacing-lg);
  cursor: pointer;
  transition: all var(--transition-base);
  background: linear-gradient(145deg, #ffffff, #f1f5f9);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
}

.step-card:hover {
  transform: translateX(8px);
  box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
  border-color: var(--color-primary-light);
}

.step-card.active {
  border-color: var(--color-primary);
  background: linear-gradient(145deg, #e3f2fd, #bbdefb);
  box-shadow: 0 8px 25px rgba(24, 144, 255, 0.2);
}

.step-card.completed {
  border-color: var(--color-success);
  background: linear-gradient(145deg, #e8f5e8, #c8e6c9);
  box-shadow: 0 8px 25px rgba(76, 175, 80, 0.2);
}

.step-number {
  width: 50px;
  height: 50px;
  border-radius: var(--border-radius-full);
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-lg);
  margin-right: var(--spacing-xl);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  transition: all var(--transition-base);
}

.step-card.completed .step-number {
  background: linear-gradient(135deg, var(--color-success), #388e3c);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.step-card:hover .step-number {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
}

.step-content {
  flex: 1;
}

.step-content h4 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
}

.step-content p {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--text-secondary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
}

.step-details {
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-md);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.step-meta {
  display: flex;
  gap: var(--spacing-lg);
  align-items: center;
  flex-wrap: wrap;
}

.step-time {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--bg-tertiary);
  border-radius: var(--border-radius-sm);
}

.interactive-hint {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-primary);
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--color-primary-light);
  border-radius: var(--border-radius-lg);
  opacity: 0.9;
}

.simulator-actions {
  text-align: center;
  margin-top: var(--spacing-2xl);
  padding-top: var(--spacing-xl);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.simulator-actions .el-button {
  margin: 0 var(--spacing-sm);
  border-radius: var(--border-radius-lg);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-md) var(--spacing-xl);
  transition: all var(--transition-base);
}

.simulator-actions .el-button--primary {
  background: var(--gradient-primary);
  border: none;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.simulator-actions .el-button--primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
}

/* 技术架构样式 */
.architecture-overview {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
  margin-top: var(--spacing-lg);
}

.architecture-diagram {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-lg);
  margin: var(--spacing-xl) 0;
}

.arch-layer {
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-md);
  text-align: center;
}

.arch-layer.frontend {
  background: linear-gradient(135deg, #4CAF50, #8BC34A);
  color: white;
}

.arch-layer.backend {
  background: linear-gradient(135deg, #2196F3, #03A9F4);
  color: white;
}

.arch-layer.ai {
  background: linear-gradient(135deg, #FF9800, #FFC107);
  color: white;
}

.arch-layer.data {
  background: linear-gradient(135deg, #9C27B0, #E91E63);
  color: white;
}

.tech-stack {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  justify-content: center;
  margin-top: var(--spacing-md);
}

.tech-item {
  background: rgba(255, 255, 255, 0.2);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: 0.9rem;
}

.architecture-features {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

.feature-highlight {
  text-align: center;
  padding: var(--spacing-lg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
}

.feature-highlight h4 {
  margin: var(--spacing-sm) 0;
  color: var(--text-primary);
}

.feature-highlight p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}

.feature-metrics {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-sm);
}

.metric-item {
  font-size: 0.8rem;
  color: var(--color-primary);
  background: var(--color-primary-light);
  padding: 2px 6px;
  border-radius: var(--border-radius-sm);
}

.arch-description {
  text-align: center;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
  font-size: 1.1rem;
}

.layer-description {
  font-size: 0.9rem;
  margin: var(--spacing-sm) 0;
  opacity: 0.9;
}

.tech-specifications {
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-xl);
  border-top: 1px solid var(--border-color);
}

.tech-specifications h4 {
  text-align: center;
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
}

.spec-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.spec-item {
  background: var(--bg-secondary);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-md);
}

.spec-item strong {
  color: var(--text-primary);
  display: block;
  margin-bottom: var(--spacing-sm);
}

.spec-item ul {
  margin: 0;
  padding-left: var(--spacing-lg);
}

.spec-item li {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.demo-footer {
  text-align: center;
  padding: var(--spacing-xl);
  background: white;
  border-top: 1px solid var(--border-color);
}

/* 对话框样式 */
.feature-demo-content,
.video-player-dialog {
  text-align: center;
  padding: var(--spacing-xl);
}

.demo-video-placeholder,
.video-placeholder-large {
  background: var(--bg-secondary);
  padding: var(--spacing-xxl);
  border-radius: var(--border-radius-lg);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .video-section {
    grid-template-columns: 1fr;
  }
  
  .architecture-diagram {
    grid-template-columns: 1fr;
  }
  
  .architecture-features {
    grid-template-columns: 1fr;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
}

/* 新增的演示功能样式 */

/* 主视频区域 */
.main-video-section {
  margin-bottom: var(--spacing-xl);
}

.video-player-container {
  display: flex;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
}

.video-info-card {
  background: var(--bg-secondary);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  margin: var(--spacing-md) 0;
}

.video-stats {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.tech-features {
  display: flex;
  gap: var(--spacing-xs);
  flex-wrap: wrap;
}

.feature-tag {
  font-weight: 500;
}

.video-actions {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-md);
  justify-content: center;
}

/* 章节导航 */
.chapter-navigation {
  flex: 1;
  background: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  max-height: 600px;
  overflow-y: auto;
}

.chapter-navigation h4 {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

.chapters-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.chapter-item {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: all 0.3s ease;
}

.chapter-item:hover {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
  transform: translateY(-2px);
}

.chapter-thumbnail {
  width: 80px;
  height: 60px;
  background: var(--bg-tertiary);
  border-radius: var(--border-radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.chapter-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.chapter-info {
  flex: 1;
}

.chapter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xs);
}

.chapter-time {
  font-weight: 600;
  color: var(--color-primary);
}

.chapter-duration {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.chapter-info h5 {
  margin: 0 0 var(--spacing-xs) 0;
  color: var(--text-primary);
}

.chapter-info p {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.chapter-keypoints {
  display: flex;
  gap: var(--spacing-xs);
  flex-wrap: wrap;
}

/* 技术规格面板 */
.tech-specs-panel {
  background: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-xl);
  margin-top: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
}

.tech-specs-panel h4 {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.4;
}

.specs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-lg);
}

.spec-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--bg-primary);
  border-radius: var(--border-radius-md);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
  min-height: 60px;
}

.spec-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: var(--color-primary);
}

.spec-label {
  font-weight: 500;
  color: var(--text-secondary);
  font-size: 0.95rem;
  line-height: 1.5;
  margin-right: var(--spacing-md);
}

.spec-value {
  font-weight: 600;
  color: var(--color-primary);
  font-size: 0.9rem;
  line-height: 1.4;
  text-align: right;
  flex-shrink: 0;
}

/* 演示场景 */
.demo-scenarios-section {
  margin-bottom: var(--spacing-xl);
}

.demo-scenarios-section h3 {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
}

.scenarios-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.scenario-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.scenario-card:hover {
  border-color: var(--color-primary);
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.scenario-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-sm);
}

.scenario-header h4 {
  margin: 0;
  color: var(--text-primary);
}

.scenario-card p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
  line-height: 1.6;
}

.scenario-meta {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  font-size: 0.9rem;
}

.scenario-time,
.scenario-steps {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--text-secondary);
}

.scenario-btn {
  width: 100%;
}

/* 增强的教程网格 */
.tutorials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--spacing-lg);
}

.video-item {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.video-item:hover {
  border-color: var(--color-primary);
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.video-thumbnail {
  position: relative;
  aspect-ratio: 16 / 9;
  width: 100%;
  background: var(--bg-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-primary);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.video-thumbnail:hover {
  transform: scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.video-overlay {
  position: absolute;
  bottom: var(--spacing-sm);
  right: var(--spacing-sm);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: var(--border-radius-sm);
  font-size: 0.8rem;
}

.video-info {
  padding: var(--spacing-md);
}

.video-info h4 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
}

.video-info p {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
  line-height: 1.5;
}

.video-meta {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
  margin-bottom: var(--spacing-sm);
  font-size: 0.8rem;
}

.video-views,
.video-rating {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--text-secondary);
}

.video-tags {
  display: flex;
  gap: var(--spacing-xs);
  flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .video-player-container {
    flex-direction: column;
  }

  .video-stats {
    justify-content: center;
  }

  .scenarios-grid,
  .tutorials-grid {
    grid-template-columns: 1fr;
  }

  .specs-grid {
    grid-template-columns: 1fr;
  }

  .video-actions {
    flex-direction: column;
    align-items: center;
  }
}

/* 增强的视频对话框样式 */
.video-dialog .el-dialog__body {
  padding: 0;
}

.video-player-dialog {
  display: flex;
  min-height: 600px;
}

.video-main-area {
  flex: 1;
  padding: var(--spacing-xl);
}

/* ==================== 视频播放器容器样式优化 ==================== */
.video-player-container-main {
  position: relative;
  width: 100%;
  background: linear-gradient(145deg, #000000, #1a1a1a);
  border-radius: var(--border-radius-2xl);
  overflow: hidden;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.3),
    0 10px 10px -5px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.1);
  transition: all var(--transition-base);
}

.video-player-container-main:hover {
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.4),
    0 15px 15px -5px rgba(0, 0, 0, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
  border-color: rgba(24, 144, 255, 0.3);
}

.main-video-player {
  width: 100%;
  height: auto;
  min-height: 450px;
  max-height: 650px;
  display: block;
  background: #000;
  border-radius: var(--border-radius-xl);
  transition: all var(--transition-base);
}

.main-video-player:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* 视频播放器控制栏美化 */
.main-video-player::-webkit-media-controls-panel {
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  border-radius: 0 0 var(--border-radius-xl) var(--border-radius-xl);
}

.main-video-player::-webkit-media-controls-play-button,
.main-video-player::-webkit-media-controls-pause-button {
  background: var(--gradient-primary);
  border-radius: var(--border-radius-full);
  margin: 0 var(--spacing-sm);
}

.main-video-player::-webkit-media-controls-timeline {
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--border-radius-sm);
  margin: 0 var(--spacing-md);
}

.main-video-player::-webkit-media-controls-current-time-display,
.main-video-player::-webkit-media-controls-time-remaining-display {
  color: white;
  font-weight: var(--font-weight-medium);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* ==================== 视频加载和控制覆盖层 ==================== */
.video-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(24, 144, 255, 0.1));
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 15;
  backdrop-filter: blur(10px);
  color: white;
}

.loading-spinner {
  margin-bottom: var(--spacing-lg);
}

.loading-spinner .el-icon {
  font-size: 3rem;
  color: var(--color-primary);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.video-loading-overlay p {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
}

.video-controls-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(24, 144, 255, 0.1));
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  transition: all var(--transition-base);
  backdrop-filter: blur(10px);
}

.video-controls-overlay:hover {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(24, 144, 255, 0.15));
}

.video-info-overlay {
  text-align: center;
  color: white;
  padding: var(--spacing-2xl);
  max-width: 600px;
  margin: 0 auto;
}

.video-info-overlay h3 {
  color: white;
  margin-bottom: var(--spacing-lg);
  font-size: clamp(1.5rem, 4vw, 2.5rem);
  font-weight: var(--font-weight-bold);
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  line-height: var(--line-height-tight);
}

.video-info-overlay p {
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: var(--spacing-2xl);
  font-size: clamp(1rem, 2.5vw, 1.2rem);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-relaxed);
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
}

.video-info-overlay .play-button {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
  border: none;
  padding: var(--spacing-lg) var(--spacing-2xl);
  border-radius: var(--border-radius-2xl);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: white;
  box-shadow:
    0 8px 16px rgba(24, 144, 255, 0.4),
    0 4px 8px rgba(0, 0, 0, 0.2);
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}

.video-info-overlay .play-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.video-info-overlay .play-button:hover::before {
  left: 100%;
}

.video-info-overlay .play-button:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow:
    0 12px 24px rgba(24, 144, 255, 0.5),
    0 6px 12px rgba(0, 0, 0, 0.3);
}

.video-info-overlay .play-button .el-icon {
  font-size: 1.5rem;
  margin-right: var(--spacing-sm);
}

.video-placeholder-large {
  text-align: center;
  padding: var(--spacing-xl);
  background: var(--bg-secondary);
  border-radius: var(--border-radius-md);
}

.video-details {
  margin: var(--spacing-lg) 0;
  text-align: left;
}

.video-stats-row {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  margin-bottom: var(--spacing-md);
  flex-wrap: wrap;
}

.stat-badge {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  background: var(--bg-primary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.video-features,
.video-tech-specs {
  margin: var(--spacing-lg) 0;
  padding: var(--spacing-md);
  background: var(--bg-primary);
  border-radius: var(--border-radius-md);
}

.video-features h4,
.video-tech-specs h4 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--text-primary);
  font-size: 1.1rem;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.feature-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--border-color);
}

.feature-item.highlight {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
}

.feature-item strong {
  color: var(--text-primary);
}

.feature-item span {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.tech-specs-compact {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-sm);
}

.spec-row {
  display: flex;
  justify-content: space-between;
  padding: var(--spacing-xs);
  background: var(--bg-secondary);
  border-radius: var(--border-radius-sm);
}

.spec-key {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.spec-val {
  color: var(--color-primary);
  font-weight: 500;
  font-size: 0.9rem;
}

.video-controls {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  margin-top: var(--spacing-lg);
}

/* 章节侧边栏 */
.video-chapters-sidebar {
  width: 350px;
  background: var(--bg-tertiary);
  padding: var(--spacing-lg);
  border-left: 1px solid var(--border-color);
  overflow-y: auto;
}

.video-chapters-sidebar h4 {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin: 0 0 var(--spacing-md) 0;
  color: var(--text-primary);
}

.chapters-compact-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.chapter-compact-item {
  display: flex;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  transition: all 0.3s ease;
}

.chapter-compact-item:hover {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
}

.chapter-time-badge {
  background: var(--color-primary);
  color: white;
  padding: 2px 6px;
  border-radius: var(--border-radius-sm);
  font-size: 0.8rem;
  font-weight: 500;
  white-space: nowrap;
  align-self: flex-start;
}

.chapter-content {
  flex: 1;
}

.chapter-content h5 {
  margin: 0 0 var(--spacing-xs) 0;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.chapter-content p {
  margin: 0 0 var(--spacing-xs) 0;
  color: var(--text-secondary);
  font-size: 0.8rem;
  line-height: 1.4;
}

.chapter-tags {
  display: flex;
  gap: var(--spacing-xs);
  flex-wrap: wrap;
}

/* ==================== 动画效果 ==================== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* ==================== 响应式设计优化 ==================== */
@media (max-width: 1024px) {
  .demo-title {
    font-size: clamp(1.8rem, 4vw, 2.5rem);
  }

  .demo-subtitle {
    font-size: clamp(0.9rem, 2.5vw, 1.2rem);
  }

  .features-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
  }

  .video-section {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
  }

  .architecture-diagram {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
}

@media (max-width: 768px) {
  .demo-header {
    padding: var(--spacing-2xl) 0 var(--spacing-xl);
  }

  .demo-stats {
    flex-direction: column;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
  }

  .stat-item {
    padding: var(--spacing-sm);
  }

  .demo-content {
    padding: var(--spacing-lg);
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .feature-card {
    padding: var(--spacing-xl);
  }

  .video-player-dialog {
    flex-direction: column;
  }

  .video-player-container-main {
    margin-bottom: var(--spacing-md);
  }

  .main-video-player {
    min-height: 250px;
    max-height: 400px;
  }

  .video-info-overlay h3 {
    font-size: var(--font-size-xl);
  }

  .video-info-overlay p {
    font-size: var(--font-size-base);
  }

  .video-chapters-sidebar {
    width: 100%;
    border-left: none;
    border-top: 1px solid var(--border-color);
  }

  .video-stats-row {
    justify-content: center;
  }

  .tech-specs-compact {
    grid-template-columns: 1fr;
  }

  .video-controls {
    flex-direction: column;
    align-items: center;
  }

  .step-card {
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
  }

  .step-number {
    width: 40px;
    height: 40px;
    margin-right: var(--spacing-lg);
  }

  .simulator-steps::before {
    left: 20px;
  }
}

@media (max-width: 480px) {
  .demo-title {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .demo-stats {
    gap: var(--spacing-md);
  }

  .feature-actions {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .feature-actions .el-button {
    width: 100%;
  }

  .video-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .step-card {
    flex-direction: column;
    text-align: center;
  }

  .step-number {
    margin-right: 0;
    margin-bottom: var(--spacing-md);
  }

  .simulator-steps::before {
    display: none;
  }
}

/* 新增视觉组件样式 */

/* 功能卡片媒体样式 */
.feature-media {
  position: relative;
  margin-bottom: var(--spacing-md);
  border-radius: var(--border-radius-md);
  overflow: hidden;
}

.feature-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.feature-icon-overlay {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  padding: var(--spacing-sm);
  backdrop-filter: blur(10px);
}

.feature-info {
  padding: var(--spacing-md);
}

.feature-tags {
  margin: var(--spacing-sm) 0;
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.feature-tag {
  font-size: 0.8rem;
}

/* 视频播放覆盖层样式 */
.video-play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
  opacity: 0;
  transition: opacity 0.3s ease;
  cursor: pointer;
}

.video-play-overlay:hover {
  opacity: 1;
}

.play-button-large {
  background: rgba(24, 144, 255, 0.9);
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  transition: transform 0.3s ease;
}

.play-button-large:hover {
  transform: scale(1.1);
}

.play-button {
  background: rgba(24, 144, 255, 0.9);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  transition: transform 0.3s ease;
}

.play-button:hover {
  transform: scale(1.1);
}

.video-duration-badge {
  position: absolute;
  bottom: var(--spacing-sm);
  right: var(--spacing-sm);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: 0.8rem;
}

.video-info-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: var(--spacing-lg) var(--spacing-md) var(--spacing-md);
}

.video-info-overlay h3 {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: 1.2rem;
}

.video-info-overlay p {
  margin: 0;
  font-size: 0.9rem;
  opacity: 0.9;
}

/* 章节导航增强样式 */
.chapter-item {
  transition: all 0.3s ease;
}

.chapter-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.chapter-thumbnail {
  position: relative;
}

.chapter-thumb-image {
  width: 120px;
  height: 68px;
  object-fit: cover;
  border-radius: var(--border-radius-sm);
}

.chapter-placeholder {
  width: 120px;
  height: 68px;
  background: var(--bg-secondary);
  border-radius: var(--border-radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
}

.chapter-play-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(24, 144, 255, 0.9);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.chapter-item:hover .chapter-play-indicator {
  opacity: 1;
}

.chapter-tag {
  font-size: 0.7rem;
  margin: 2px;
}

/* 教程视频增强样式 */
.tutorial-thumbnail {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.difficulty-badge {
  position: absolute;
  top: var(--spacing-sm);
  left: var(--spacing-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: 0.7rem;
  font-weight: bold;
  color: white;
}

.difficulty-easy {
  background: #52c41a;
}

.difficulty-medium {
  background: #faad14;
}

.difficulty-hard {
  background: #ff4d4f;
}

.difficulty-beginner {
  background: #1890ff;
}

.video-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-xs);
}

.video-tag {
  font-size: 0.7rem;
}

/* 界面截图展示样式 */
.interface-showcase {
  margin: var(--spacing-xl) 0;
  padding: var(--spacing-xl) 0;
  position: relative;
}

.showcase-background {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  opacity: 0.1;
}

.interface-gallery h3 {
  text-align: center;
  margin-bottom: var(--spacing-xl);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
}

.interface-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.interface-item {
  background: white;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.interface-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.interface-screenshot {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.interface-info {
  padding: var(--spacing-md);
}

.interface-info h4 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
}

.interface-info p {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.interface-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.interface-tag {
  font-size: 0.7rem;
}

/* 步骤截图样式 */
.step-screenshot {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  width: 80px;
  height: 60px;
  border-radius: var(--border-radius-sm);
  overflow: hidden;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.step-card:hover .step-screenshot {
  opacity: 1;
}

.step-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 架构图表样式 */
.architecture-background {
  background: linear-gradient(45deg, #f0f2f5 0%, #e6f7ff 100%);
  opacity: 0.5;
}

.architecture-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.architecture-header h3 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.architecture-visualization {
  text-align: center;
  margin: var(--spacing-xl) 0;
}

.architecture-main-diagram {
  max-width: 100%;
  height: auto;
  border-radius: var(--border-radius-lg);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.layer-icon {
  width: 60px;
  height: 60px;
  margin-bottom: var(--spacing-md);
  border-radius: 50%;
  overflow: hidden;
  background: var(--bg-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.tech-icon {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.layer-content {
  flex: 1;
}

.tech-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin: var(--spacing-xs) 0;
  padding: var(--spacing-xs);
  border-radius: var(--border-radius-sm);
  transition: background-color 0.3s ease;
}

.tech-item:hover {
  background: var(--bg-primary);
}

.tech-icon-small {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  overflow: hidden;
  background: var(--bg-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.tech-small-icon {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.tech-name {
  font-size: 0.9rem;
  color: var(--text-primary);
}

/* 视差背景图案 */
.video-background-pattern {
  background:
    radial-gradient(circle at 20% 50%, rgba(24, 144, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(82, 196, 26, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(250, 173, 20, 0.1) 0%, transparent 50%);
}

/* 动画增强 */
@keyframes animate-slide-in-left {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slide-in-left {
  animation: animate-slide-in-left 0.6s ease-out forwards;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .interface-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .feature-media {
    margin-bottom: var(--spacing-sm);
  }

  .feature-image {
    height: 150px;
  }

  .step-screenshot {
    display: none;
  }

  .architecture-main-diagram {
    max-width: 100%;
    height: auto;
  }

  .domain-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .domain-card {
    margin-bottom: var(--spacing-md);
  }

  .domain-gallery {
    justify-content: center;
  }
}

/* 技术领域视频样式 */
.domain-videos {
  margin: var(--spacing-xl) 0;
}

.domain-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

.domain-card {
  background: var(--el-bg-color);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--el-box-shadow-light);
  transition: all 0.3s ease;
  cursor: pointer;
}

.domain-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--el-box-shadow);
}

.domain-banner {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.domain-banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.domain-card:hover .domain-banner-image {
  transform: scale(1.05);
}

.domain-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.1));
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.domain-card:hover .domain-overlay {
  opacity: 1;
}

.domain-overlay .play-button {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--el-color-primary);
  font-size: 24px;
  transition: all 0.3s ease;
}

.domain-overlay .play-button:hover {
  background: white;
  transform: scale(1.1);
}

.domain-badge {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  background: var(--el-color-primary);
  color: white;
  padding: 4px 12px;
  border-radius: var(--border-radius);
  font-size: 0.8rem;
  font-weight: 500;
}

.domain-content {
  padding: var(--spacing-lg);
}

.domain-content h4 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--el-text-color-primary);
  font-size: 1.1rem;
  font-weight: 600;
}

.domain-content p {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--el-text-color-regular);
  font-size: 0.9rem;
  line-height: 1.5;
}

.domain-gallery {
  display: flex;
  gap: var(--spacing-xs);
  margin: var(--spacing-md) 0;
  align-items: center;
}

.gallery-thumb {
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius);
  object-fit: cover;
  border: 2px solid var(--el-border-color-light);
}

.gallery-more {
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius);
  background: var(--el-color-info-light-9);
  color: var(--el-color-info);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: 500;
}

.domain-features {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.domain-feature-tag {
  font-size: 0.75rem;
}

/* 演示场景对话框样式 */
.scenario-dialog-content {
  max-height: 70vh;
  overflow-y: auto;
}

.scenario-header {
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--el-border-color-light);
}

.scenario-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
}

.scenario-time {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--el-text-color-regular);
  font-size: 0.9rem;
}

.scenario-description {
  margin: 0;
  color: var(--el-text-color-regular);
  line-height: 1.6;
}

.scenario-steps h4 {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin: 0 0 var(--spacing-md) 0;
  color: var(--el-text-color-primary);
}

.steps-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.step-item {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-lg);
  border: 2px solid var(--el-border-color-light);
  transition: all 0.3s ease;
}

.step-item.active {
  border-color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);
}

.step-item.completed {
  border-color: var(--el-color-success);
  background: var(--el-color-success-light-9);
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--el-color-info-light-8);
  color: var(--el-color-info);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  flex-shrink: 0;
}

.step-item.active .step-number {
  background: var(--el-color-primary);
  color: white;
}

.step-item.completed .step-number {
  background: var(--el-color-success);
  color: white;
}

.step-content {
  flex: 1;
}

.step-content h5 {
  margin: 0 0 var(--spacing-xs) 0;
  color: var(--el-text-color-primary);
  font-size: 1rem;
  font-weight: 600;
}

.step-content p {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--el-text-color-regular);
  line-height: 1.5;
}

.step-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
}

.step-duration {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--el-text-color-secondary);
  font-size: 0.85rem;
}

.step-actions {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.step-tips {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm);
  background: var(--el-color-warning-light-9);
  border-radius: var(--border-radius);
  color: var(--el-color-warning-dark-2);
  font-size: 0.9rem;
}

.scenario-controls {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--el-border-color-light);
}

/* 演示步骤对话框样式 */
.demo-steps-dialog .el-dialog__body {
  padding: var(--spacing-xl);
}

.demo-steps-content {
  max-height: 80vh;
  overflow-y: auto;
}

.steps-progress {
  margin-bottom: var(--spacing-xl);
  text-align: center;
}

.progress-info {
  margin-top: var(--spacing-sm);
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--el-color-primary);
}

.current-step-detail {
  background: var(--el-bg-color-page);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
}

.step-header {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.step-number-large {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-light-3));
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
  flex-shrink: 0;
}

.step-info h3 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--el-text-color-primary);
  font-size: 1.3rem;
}

.step-description {
  color: var(--el-text-color-regular);
  line-height: 1.6;
  margin-bottom: var(--spacing-md);
}

.step-meta {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.step-duration, .step-elements {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--el-text-color-secondary);
  font-size: 0.9rem;
}

.step-screenshot-large {
  margin: var(--spacing-lg) 0;
  text-align: center;
}

.step-image-large {
  max-width: 100%;
  height: auto;
  border-radius: var(--border-radius);
  box-shadow: var(--el-box-shadow-light);
  transition: transform 0.3s ease;
}

.step-image-large:hover {
  transform: scale(1.02);
}

.step-tips {
  margin: var(--spacing-lg) 0;
}

.interactive-elements {
  margin-top: var(--spacing-lg);
}

.interactive-elements h4 {
  margin-bottom: var(--spacing-sm);
  color: var(--el-text-color-primary);
}

.elements-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.element-tag {
  margin: 0;
}

.steps-navigation {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  background: var(--el-bg-color);
  border-radius: var(--border-radius);
  margin-top: var(--spacing-xl);
}

.steps-dots {
  display: flex;
  gap: var(--spacing-sm);
}

.step-dot {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: var(--el-color-info-light-8);
  color: var(--el-text-color-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.step-dot:hover {
  background: var(--el-color-primary-light-7);
  color: var(--el-color-primary);
}

.step-dot.active {
  background: var(--el-color-primary);
  color: white;
  transform: scale(1.1);
}

.step-dot.completed {
  background: var(--el-color-success);
  color: white;
}

/* 功能详情对话框样式 */
.feature-details-dialog .el-dialog__body {
  padding: var(--spacing-xl);
}

.feature-details-content {
  max-height: 80vh;
  overflow-y: auto;
}

.feature-overview {
  margin-bottom: var(--spacing-xl);
}

.feature-header {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
}

.feature-icon-large {
  background: linear-gradient(135deg, var(--el-color-primary-light-8), var(--el-color-primary-light-9));
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  color: var(--el-color-primary);
}

.feature-basic-info h2 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--el-text-color-primary);
}

.feature-description {
  color: var(--el-text-color-regular);
  line-height: 1.6;
  margin-bottom: var(--spacing-md);
}

.feature-tags {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.feature-highlights, .technical-specs, .use-cases {
  margin-bottom: var(--spacing-xl);
}

.feature-highlights h3, .technical-specs h3, .use-cases h3 {
  margin-bottom: var(--spacing-lg);
  margin-top: var(--spacing-xl);
  color: var(--el-text-color-primary);
  border-bottom: 2px solid var(--el-color-primary-light-8);
  padding-bottom: var(--spacing-md);
  font-size: 1.4rem;
  font-weight: 700;
  line-height: 1.3;
  letter-spacing: 0.02em;
}

.technical-specs {
  padding: var(--spacing-xl);
  background: linear-gradient(135deg, var(--el-bg-color-page), var(--el-color-primary-light-9));
  border-radius: var(--border-radius-xl);
  margin: var(--spacing-xl) 0;
}

.highlights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-md);
}

.highlight-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background: var(--el-bg-color-page);
  border-radius: var(--border-radius);
  border-left: 4px solid var(--el-color-primary);
}

.specs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-md);
}

.spec-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-xl);
  background: var(--el-bg-color-page);
  border-radius: var(--border-radius-lg);
  transition: all 0.3s ease;
  min-height: 70px;
  margin-bottom: var(--spacing-md);
}

.spec-item:hover {
  background: var(--el-color-primary-light-9);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.spec-label {
  font-weight: 600;
  color: var(--el-text-color-primary);
  font-size: 1rem;
  line-height: 1.6;
  letter-spacing: 0.02em;
}

.spec-value {
  color: var(--el-color-primary);
  text-align: right;
  font-size: 0.95rem;
  font-weight: 500;
  line-height: 1.5;
  max-width: 60%;
  word-break: break-word;
}

.cases-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.case-item {
  padding: var(--spacing-lg);
  background: var(--el-bg-color-page);
  border-radius: var(--border-radius);
  border: 1px solid var(--el-border-color-lighter);
}

.case-item h4 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--el-color-primary);
}

.case-item p {
  margin: 0;
  color: var(--el-text-color-regular);
  line-height: 1.6;
}

.details-actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  padding-top: var(--spacing-xl);
  border-top: 1px solid var(--el-border-color-lighter);
  margin-top: var(--spacing-xl);
}

/* 视频教程优化样式 */
.video-description {
  color: var(--el-text-color-regular);
  line-height: 1.5;
  margin-bottom: var(--spacing-sm);
  font-size: 0.9rem;
}

.video-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  flex-wrap: wrap;
}

.video-duration, .video-views, .video-rating {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--el-text-color-secondary);
  font-size: 0.85rem;
}

.video-chapters-preview {
  margin-top: var(--spacing-sm);
}

.chapters-count {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--el-color-primary);
  font-size: 0.85rem;
  font-weight: 500;
}

.video-tags {
  margin-top: var(--spacing-sm);
}

.video-tag {
  margin-right: var(--spacing-xs);
  margin-bottom: var(--spacing-xs);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .step-header {
    flex-direction: column;
    text-align: center;
  }

  .feature-header {
    flex-direction: column;
    text-align: center;
  }

  .steps-navigation {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .steps-dots {
    order: -1;
  }

  .highlights-grid,
  .specs-grid,
  .cases-list {
    grid-template-columns: 1fr;
  }
}
</style>
