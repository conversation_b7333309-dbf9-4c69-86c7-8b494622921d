<template>
  <div class="feature-showcase">
    <div class="showcase-header">
      <div class="feature-icon">
        <el-icon :size="60">
          <component :is="feature.icon" />
        </el-icon>
      </div>
      <h2>{{ feature.title }}</h2>
      <p>{{ feature.description }}</p>
    </div>

    <div class="showcase-content">
      <!-- 功能亮点 -->
      <div class="highlights-section">
        <h3>核心亮点</h3>
        <div class="highlights-grid">
          <div 
            v-for="(highlight, index) in feature.highlights" 
            :key="index"
            class="highlight-item"
          >
            <el-icon><Star /></el-icon>
            <span>{{ highlight }}</span>
          </div>
        </div>
      </div>

      <!-- 演示步骤 -->
      <div class="steps-section">
        <h3>演示步骤</h3>
        <div class="steps-timeline">
          <div 
            v-for="(step, index) in feature.demoSteps" 
            :key="index"
            class="step-item"
            :class="{ active: currentStep === index }"
            @click="goToStep(index)"
          >
            <div class="step-number">{{ step.step }}</div>
            <div class="step-content">
              <h4>{{ step.title }}</h4>
              <p>{{ step.description }}</p>
              <div class="step-screenshot" v-if="step.screenshot">
                <img :src="step.screenshot" :alt="step.title" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="showcase-actions">
        <el-button 
          type="primary" 
          size="large"
          @click="startDemo"
        >
          <el-icon><VideoPlay /></el-icon>
          开始演示
        </el-button>
        <el-button 
          size="large"
          @click="tryFeature"
        >
          <el-icon><Operation /></el-icon>
          立即体验
        </el-button>
        <el-button 
          size="large"
          @click="viewDetails"
        >
          <el-icon><Document /></el-icon>
          查看详情
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Star, VideoPlay, Operation, Document
} from '@element-plus/icons-vue'

const props = defineProps({
  feature: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['start-demo', 'try-feature', 'view-details'])

const currentStep = ref(0)

const goToStep = (index) => {
  currentStep.value = index
  ElMessage.info(`查看步骤 ${index + 1}: ${props.feature.demoSteps[index].title}`)
}

const startDemo = () => {
  emit('start-demo', props.feature)
}

const tryFeature = () => {
  emit('try-feature', props.feature)
}

const viewDetails = () => {
  emit('view-details', props.feature)
}
</script>

<style scoped>
.feature-showcase {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-lg);
  max-width: 800px;
  margin: 0 auto;
}

.showcase-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.feature-icon {
  color: var(--color-primary);
  margin-bottom: var(--spacing-lg);
}

.showcase-header h2 {
  font-size: 2rem;
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

.showcase-header p {
  font-size: 1.1rem;
  color: var(--text-secondary);
}

.highlights-section,
.steps-section {
  margin-bottom: var(--spacing-xl);
}

.highlights-section h3,
.steps-section h3 {
  font-size: 1.3rem;
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
}

.highlights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.highlight-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  transition: all 0.3s ease;
}

.highlight-item:hover {
  background: var(--color-primary-light);
  transform: translateY(-2px);
}

.highlight-item .el-icon {
  color: var(--color-primary);
}

.steps-timeline {
  position: relative;
}

.step-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-lg);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: all 0.3s ease;
}

.step-item:hover {
  border-color: var(--color-primary);
  box-shadow: var(--shadow-md);
}

.step-item.active {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--color-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: var(--spacing-lg);
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-content h4 {
  font-size: 1.1rem;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);
}

.step-content p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
}

.step-screenshot {
  width: 100%;
  max-width: 300px;
  border-radius: var(--border-radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.step-screenshot img {
  width: 100%;
  height: auto;
  display: block;
}

.showcase-actions {
  text-align: center;
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
}

.showcase-actions .el-button {
  margin: 0 var(--spacing-sm);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .feature-showcase {
    padding: var(--spacing-lg);
  }
  
  .highlights-grid {
    grid-template-columns: 1fr;
  }
  
  .step-item {
    flex-direction: column;
    text-align: center;
  }
  
  .step-number {
    margin-right: 0;
    margin-bottom: var(--spacing-md);
  }
  
  .showcase-actions .el-button {
    display: block;
    width: 100%;
    margin: var(--spacing-sm) 0;
  }
}
</style>
