<template>
  <div class="interview-main">
    <!-- 面试头部信息 -->
    <div class="interview-header">
      <div class="session-info">
        <h2>{{ domainName }} - {{ positionName }}</h2>
        <div class="progress-info">
          <span>题目进度: {{ currentQuestionIndex + 1 }} / {{ totalQuestions }}</span>
          <el-progress 
            :percentage="progressPercentage" 
            :stroke-width="8"
            color="#409EFF"
          />
        </div>
      </div>
      <div class="timer">
        <i class="el-icon-time"></i>
        <span>{{ formatTime(elapsedTime) }}</span>
      </div>
    </div>

    <!-- 面试内容区域 -->
    <div class="interview-content">
      <!-- 题目显示区 -->
      <div class="question-section">
        <div class="question-header">
          <h3>面试题目</h3>
          <el-tag :type="difficultyColor">{{ currentQuestion?.difficulty || '中等' }}</el-tag>
        </div>
        <div class="question-content">
          <p>{{ currentQuestion?.question || '正在加载题目...' }}</p>
        </div>
      </div>

      <!-- 多模态输入区 -->
      <div class="input-section">
        <el-tabs v-model="activeTab" @tab-click="handleTabClick">
          <!-- 文本回答 -->
          <el-tab-pane label="文本回答" name="text">
            <div class="text-input">
              <el-input
                v-model="textResponse"
                type="textarea"
                :rows="8"
                placeholder="请在此输入您的回答..."
                :disabled="isSubmitting"
              />
              <div class="input-stats">
                <span>字数: {{ textResponse.length }}</span>
                <span>预计用时: {{ Math.ceil(textResponse.length / 5) }}秒</span>
              </div>
            </div>
          </el-tab-pane>

          <!-- 语音回答 -->
          <el-tab-pane label="语音回答" name="audio">
            <div class="audio-input">
              <div class="audio-controls">
                <el-button 
                  :type="isRecording ? 'danger' : 'primary'"
                  :icon="isRecording ? 'el-icon-video-pause' : 'el-icon-microphone'"
                  @click="toggleRecording"
                  :disabled="isSubmitting"
                >
                  {{ isRecording ? '停止录音' : '开始录音' }}
                </el-button>
                <span v-if="isRecording" class="recording-time">
                  录音时长: {{ formatTime(recordingTime) }}
                </span>
              </div>
              
              <div v-if="audioBlob" class="audio-preview">
                <audio :src="audioUrl" controls></audio>
                <el-button 
                  type="text" 
                  icon="el-icon-delete"
                  @click="clearAudio"
                >
                  重新录音
                </el-button>
              </div>
            </div>
          </el-tab-pane>

          <!-- 视频回答 -->
          <el-tab-pane label="视频回答" name="video">
            <div class="video-input">
              <div class="video-preview">
                <video 
                  ref="videoElement"
                  :srcObject="videoStream"
                  autoplay
                  muted
                  :class="{ recording: isVideoRecording }"
                ></video>
              </div>
              
              <div class="video-controls">
                <el-button 
                  :type="isVideoRecording ? 'danger' : 'primary'"
                  :icon="isVideoRecording ? 'el-icon-video-pause' : 'el-icon-video-camera'"
                  @click="toggleVideoRecording"
                  :disabled="isSubmitting"
                >
                  {{ isVideoRecording ? '停止录制' : '开始录制' }}
                </el-button>
                <span v-if="isVideoRecording" class="recording-time">
                  录制时长: {{ formatTime(videoRecordingTime) }}
                </span>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button 
          type="primary" 
          @click="submitResponse"
          :loading="isSubmitting"
          :disabled="!hasValidResponse"
        >
          提交回答
        </el-button>
        
        <el-button 
          @click="skipQuestion"
          :disabled="isSubmitting"
        >
          跳过此题
        </el-button>
        
        <el-button 
          type="danger"
          @click="endInterview"
          :disabled="isSubmitting"
        >
          结束面试
        </el-button>
      </div>
    </div>

    <!-- 实时分析面板（可选显示） -->
    <div v-if="showAnalysis" class="analysis-panel">
      <h4>实时分析反馈</h4>
      <div class="analysis-content">
        <div v-if="lastAnalysis?.text_analysis" class="text-analysis">
          <h5>文本分析</h5>
          <p>内容相关性: {{ lastAnalysis.text_analysis.content_relevance?.overall_relevance?.toFixed(1) }}%</p>
          <p>逻辑结构: {{ lastAnalysis.text_analysis.logical_structure?.structure_score?.toFixed(1) }}%</p>
        </div>
        
        <div v-if="lastAnalysis?.audio_analysis" class="audio-analysis">
          <h5>语音分析</h5>
          <p>语音清晰度: {{ lastAnalysis.audio_analysis.speech_clarity }}%</p>
          <p>语速: {{ lastAnalysis.audio_analysis.speech_speed }} 字/分钟</p>
        </div>
        
        <div v-if="lastAnalysis?.video_analysis" class="video-analysis">
          <h5>视频分析</h5>
          <p>眼神交流: {{ lastAnalysis.video_analysis.eye_contact_score }}%</p>
          <p>面部表情: {{ lastAnalysis.video_analysis.facial_expression_score }}%</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import { interviewApi } from '@/api/interview'

export default {
  name: 'InterviewMain',
  props: {
    sessionId: {
      type: Number,
      required: true
    },
    domain: {
      type: String,
      required: true
    },
    position: {
      type: String,
      required: true
    }
  },
  setup(props) {
    const router = useRouter()
    
    // 响应式数据
    const currentQuestion = ref(null)
    const currentQuestionIndex = ref(0)
    const totalQuestions = ref(0)
    const activeTab = ref('text')
    const textResponse = ref('')
    const isSubmitting = ref(false)
    const showAnalysis = ref(false)
    const lastAnalysis = ref(null)
    
    // 计时相关
    const startTime = ref(Date.now())
    const elapsedTime = ref(0)
    const timer = ref(null)
    
    // 录音相关
    const isRecording = ref(false)
    const recordingTime = ref(0)
    const audioBlob = ref(null)
    const audioUrl = ref('')
    const mediaRecorder = ref(null)
    const recordingTimer = ref(null)
    
    // 视频相关
    const isVideoRecording = ref(false)
    const videoRecordingTime = ref(0)
    const videoStream = ref(null)
    const videoRecorder = ref(null)
    const videoBlob = ref(null)
    const videoElement = ref(null)
    
    // 计算属性
    const domainName = computed(() => props.domain)
    const positionName = computed(() => props.position)
    const progressPercentage = computed(() => {
      return totalQuestions.value > 0 ? 
        ((currentQuestionIndex.value + 1) / totalQuestions.value) * 100 : 0
    })
    
    const difficultyColor = computed(() => {
      const difficulty = currentQuestion.value?.difficulty
      switch(difficulty) {
        case 'easy': return 'success'
        case 'medium': return 'warning'
        case 'hard': return 'danger'
        default: return 'info'
      }
    })
    
    const hasValidResponse = computed(() => {
      switch(activeTab.value) {
        case 'text':
          return textResponse.value.trim().length > 0
        case 'audio':
          return audioBlob.value !== null
        case 'video':
          return videoBlob.value !== null
        default:
          return false
      }
    })
    
    // 方法
    const formatTime = (seconds) => {
      const mins = Math.floor(seconds / 60)
      const secs = seconds % 60
      return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    
    const startTimer = () => {
      timer.value = setInterval(() => {
        elapsedTime.value = Math.floor((Date.now() - startTime.value) / 1000)
      }, 1000)
    }
    
    const loadCurrentQuestion = async () => {
      try {
        const response = await interviewApi.getCurrentQuestion(props.sessionId)
        if (response.finished) {
          await endInterview()
          return
        }
        
        currentQuestion.value = response.question
        currentQuestionIndex.value = response.current_index
        totalQuestions.value = response.total_questions
      } catch (error) {
        ElMessage.error('加载题目失败: ' + error.message)
      }
    }
    
    const submitResponse = async () => {
      if (!hasValidResponse.value) {
        ElMessage.warning('请先完成回答')
        return
      }
      
      isSubmitting.value = true
      
      try {
        // 准备提交数据
        const submitData = {
          session_id: props.sessionId,
          question_text: currentQuestion.value.question,
          text_data: textResponse.value
        }
        
        // 如果有音频数据
        if (activeTab.value === 'audio' && audioBlob.value) {
          const audioBase64 = await blobToBase64(audioBlob.value)
          submitData.audio_data = audioBase64
        }
        
        // 如果有视频数据
        if (activeTab.value === 'video' && videoBlob.value) {
          const videoBase64 = await blobToBase64(videoBlob.value)
          submitData.video_data = videoBase64
        }
        
        // 提交多模态分析
        const analysisResult = await interviewApi.analyzeMultimodal(submitData)
        lastAnalysis.value = analysisResult.analysis_results
        
        ElMessage.success('回答提交成功')
        
        // 移动到下一题
        await moveToNextQuestion()
        
      } catch (error) {
        ElMessage.error('提交失败: ' + error.message)
      } finally {
        isSubmitting.value = false
      }
    }
    
    const moveToNextQuestion = async () => {
      try {
        const response = await interviewApi.moveToNextQuestion(props.sessionId)
        
        if (response.has_next) {
          currentQuestion.value = response.question
          currentQuestionIndex.value = response.current_index
          clearAllInputs()
        } else {
          ElMessage.success('面试已完成!')
          await endInterview()
        }
      } catch (error) {
        ElMessage.error('获取下一题失败: ' + error.message)
      }
    }
    
    const clearAllInputs = () => {
      textResponse.value = ''
      clearAudio()
      clearVideo()
    }
    
    const blobToBase64 = (blob) => {
      return new Promise((resolve) => {
        const reader = new FileReader()
        reader.onloadend = () => resolve(reader.result.split(',')[1])
        reader.readAsDataURL(blob)
      })
    }
    
    // 生命周期
    onMounted(async () => {
      startTimer()
      await loadCurrentQuestion()
      await initMediaDevices()
    })
    
    onUnmounted(() => {
      if (timer.value) clearInterval(timer.value)
      if (recordingTimer.value) clearInterval(recordingTimer.value)
      if (videoStream.value) {
        videoStream.value.getTracks().forEach(track => track.stop())
      }
    })
    
    return {
      // 数据
      currentQuestion,
      currentQuestionIndex,
      totalQuestions,
      activeTab,
      textResponse,
      isSubmitting,
      showAnalysis,
      lastAnalysis,
      elapsedTime,
      isRecording,
      recordingTime,
      audioBlob,
      audioUrl,
      isVideoRecording,
      videoRecordingTime,
      videoStream,
      videoElement,
      
      // 计算属性
      domainName,
      positionName,
      progressPercentage,
      difficultyColor,
      hasValidResponse,
      
      // 方法
      formatTime,
      submitResponse,
      // ... 其他方法需要在这里添加
    }
  }
}
</script>

<style scoped>
.interview-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.interview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.session-info h2 {
  margin: 0 0 10px 0;
  color: #333;
}

.progress-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.timer {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: bold;
  color: #409EFF;
}

.interview-content {
  display: grid;
  gap: 30px;
}

.question-section {
  background: white;
  padding: 25px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.question-content p {
  font-size: 16px;
  line-height: 1.6;
  color: #333;
  margin: 0;
}

.input-section {
  background: white;
  padding: 25px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.text-input {
  position: relative;
}

.input-stats {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  font-size: 12px;
  color: #666;
}

.audio-controls, .video-controls {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.recording-time {
  color: #f56c6c;
  font-weight: bold;
}

.video-preview video {
  width: 100%;
  max-width: 400px;
  border-radius: 8px;
  border: 2px solid #ddd;
}

.video-preview video.recording {
  border-color: #f56c6c;
  box-shadow: 0 0 10px rgba(245, 108, 108, 0.3);
}

.action-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
  padding: 20px;
}

.analysis-panel {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-top: 20px;
}

.analysis-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 15px;
}

.analysis-content > div {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
}

.analysis-content h5 {
  margin: 0 0 10px 0;
  color: #409EFF;
}

.analysis-content p {
  margin: 5px 0;
  font-size: 14px;
}
</style>
