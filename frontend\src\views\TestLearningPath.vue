<template>
  <div class="test-container">
    <h1>学习路径API测试</h1>
    
    <div class="test-section">
      <h2>1. 测试获取学习路径列表</h2>
      <el-button @click="testGetPaths" :loading="loading1">测试获取人工智能路径</el-button>
      <div v-if="pathsResult" class="result">
        <h3>结果:</h3>
        <pre>{{ JSON.stringify(pathsResult, null, 2) }}</pre>
      </div>
    </div>
    
    <div class="test-section">
      <h2>2. 测试生成个性化路径</h2>
      <el-button @click="testGeneratePath" :loading="loading2">测试生成个性化路径</el-button>
      <div v-if="generateResult" class="result">
        <h3>结果:</h3>
        <pre>{{ JSON.stringify(generateResult, null, 2) }}</pre>
      </div>
    </div>
    
    <div class="test-section">
      <h2>3. 测试获取路径详情</h2>
      <el-button @click="testGetDetail" :loading="loading3">测试获取路径详情</el-button>
      <div v-if="detailResult" class="result">
        <h3>结果:</h3>
        <pre>{{ JSON.stringify(detailResult, null, 2) }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import request from '@/utils/request'

const loading1 = ref(false)
const loading2 = ref(false)
const loading3 = ref(false)
const pathsResult = ref(null)
const generateResult = ref(null)
const detailResult = ref(null)

const testGetPaths = async () => {
  loading1.value = true
  try {
    const response = await request.get('/api/v1/learning-paths/domains/人工智能')
    pathsResult.value = response
    ElMessage.success('获取路径列表成功')
  } catch (error) {
    console.error('获取路径列表失败:', error)
    ElMessage.error('获取路径列表失败')
  } finally {
    loading1.value = false
  }
}

const testGeneratePath = async () => {
  loading2.value = true
  try {
    const response = await request.post('/api/v1/learning-paths/personalized', {
      domain: '人工智能',
      position: '技术岗',
      skill_level: '中级',
      evaluation_scores: {
        professional_knowledge: 75.0,
        skill_matching: 80.0,
        language_expression: 70.0,
        logical_thinking: 85.0,
        innovation_thinking: 65.0,
        learning_ability: 90.0
      }
    })
    generateResult.value = response
    ElMessage.success('生成个性化路径成功')
  } catch (error) {
    console.error('生成个性化路径失败:', error)
    ElMessage.error('生成个性化路径失败')
  } finally {
    loading2.value = false
  }
}

const testGetDetail = async () => {
  loading3.value = true
  try {
    const response = await request.get('/api/v1/learning-paths/1')
    detailResult.value = response
    ElMessage.success('获取路径详情成功')
  } catch (error) {
    console.error('获取路径详情失败:', error)
    ElMessage.error('获取路径详情失败')
  } finally {
    loading3.value = false
  }
}
</script>

<style scoped>
.test-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.result {
  margin-top: 15px;
  padding: 15px;
  background: #f5f5f5;
  border-radius: 4px;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 400px;
  overflow-y: auto;
}
</style>
