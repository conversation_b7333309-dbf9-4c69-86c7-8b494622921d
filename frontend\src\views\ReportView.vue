<template>
  <div class="report-view">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-loading-spinner size="large" />
      <p>正在生成评估报告...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <el-result
        icon="error"
        title="报告生成失败"
        :sub-title="error"
      >
        <template #extra>
          <el-button type="primary" @click="retryGeneration">重新生成</el-button>
          <el-button @click="goBack">返回面试</el-button>
        </template>
      </el-result>
    </div>

    <!-- 报告内容 -->
    <div v-else-if="reportData" class="report-container">
      <InterviewReport :report-data="reportData" />
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-container">
      <el-empty description="暂无报告数据">
        <el-button type="primary" @click="generateReport">生成报告</el-button>
      </el-empty>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import InterviewReport from '@/components/Report/InterviewReport.vue'
import { interviewApi } from '@/api/interview'

export default {
  name: 'ReportView',
  components: {
    InterviewReport
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    
    // 响应式数据
    const loading = ref(false)
    const error = ref('')
    const reportData = ref(null)
    const sessionId = ref(null)
    
    // 方法
    const loadReport = async () => {
      if (!sessionId.value) {
        error.value = '缺少会话ID参数'
        return
      }
      
      loading.value = true
      error.value = ''
      
      try {
        // 首先尝试获取已存在的报告
        const response = await interviewApi.getInterviewReport(sessionId.value)
        reportData.value = response.report
        
        ElMessage.success('报告加载成功')
      } catch (err) {
        if (err.response?.status === 404) {
          // 报告不存在，尝试生成新报告
          await generateReport()
        } else {
          error.value = err.message || '加载报告失败'
          ElMessage.error('加载报告失败: ' + error.value)
        }
      } finally {
        loading.value = false
      }
    }
    
    const generateReport = async () => {
      if (!sessionId.value) {
        error.value = '缺少会话ID参数'
        return
      }
      
      loading.value = true
      error.value = ''
      
      try {
        // 显示生成进度提示
        ElMessage.info('正在分析面试数据，请稍候...')
        
        // 生成报告
        const response = await interviewApi.generateReport(sessionId.value)
        reportData.value = response.report
        
        ElMessage.success('报告生成成功')
      } catch (err) {
        error.value = err.message || '生成报告失败'
        ElMessage.error('生成报告失败: ' + error.value)
      } finally {
        loading.value = false
      }
    }
    
    const retryGeneration = async () => {
      await generateReport()
    }
    
    const goBack = () => {
      if (sessionId.value) {
        router.push(`/interview/${sessionId.value}`)
      } else {
        router.push('/interview-selection')
      }
    }
    
    const confirmRegenerate = async () => {
      try {
        await ElMessageBox.confirm(
          '重新生成报告将覆盖当前报告，是否继续？',
          '确认操作',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        await generateReport()
      } catch {
        // 用户取消操作
      }
    }
    
    // 生命周期
    onMounted(() => {
      // 从路由参数获取会话ID
      sessionId.value = route.params.sessionId
      
      if (!sessionId.value) {
        error.value = '缺少会话ID参数'
        return
      }
      
      // 加载报告
      loadReport()
    })
    
    return {
      loading,
      error,
      reportData,
      sessionId,
      loadReport,
      generateReport,
      retryGeneration,
      goBack,
      confirmRegenerate
    }
  }
}
</script>

<style scoped>
.report-view {
  min-height: 100vh;
  background: #f5f7fa;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  gap: 20px;
}

.loading-container p {
  font-size: 16px;
  color: #666;
  margin: 0;
}

.error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 40px;
}

.empty-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 40px;
}

.report-container {
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .loading-container,
  .error-container,
  .empty-container {
    min-height: 50vh;
    padding: 20px;
  }
  
  .loading-container p {
    font-size: 14px;
  }
}
</style>
