<template>
  <div class="learning-path-container">
    <!-- 页面头部 -->
    <el-card class="header-card">
      <div class="header-content">
        <h1>智能学习路径推荐</h1>
        <p>基于您的面试表现，为您量身定制个性化学习路径</p>
      </div>
    </el-card>

    <!-- 学习路径选择 -->
    <el-card class="selection-card" v-if="!selectedPath">
      <template #header>
        <h2>选择学习路径</h2>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form :model="pathForm" label-width="100px">
            <el-form-item label="技术领域">
              <el-select v-model="pathForm.domain" placeholder="请选择技术领域" @change="onDomainChange">
                <el-option label="人工智能" value="人工智能"></el-option>
                <el-option label="大数据" value="大数据"></el-option>
                <el-option label="物联网" value="物联网"></el-option>
              </el-select>
            </el-form-item>
            
            <el-form-item label="目标岗位">
              <el-select v-model="pathForm.position" placeholder="请选择目标岗位">
                <el-option label="技术岗" value="技术岗"></el-option>
                <el-option label="产品岗" value="产品岗"></el-option>
                <el-option label="运维测试岗" value="运维测试岗"></el-option>
              </el-select>
            </el-form-item>
            
            <el-form-item label="技能水平">
              <el-select v-model="pathForm.skillLevel" placeholder="请选择技能水平">
                <el-option label="初级" value="初级"></el-option>
                <el-option label="中级" value="中级"></el-option>
                <el-option label="高级" value="高级"></el-option>
              </el-select>
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" @click="generatePath" :loading="loading">
                生成学习路径
              </el-button>
              <el-button @click="loadRecommendedPath" v-if="sessionId">
                基于面试结果推荐
              </el-button>
            </el-form-item>
          </el-form>
        </el-col>
        
        <el-col :span="16">
          <div class="domain-info" v-if="pathForm.domain">
            <h3>{{ pathForm.domain }}领域介绍</h3>
            <p>{{ getDomainDescription(pathForm.domain) }}</p>
            
            <div class="career-prospects">
              <h4>职业前景</h4>
              <el-tag v-for="career in getCareerProspects(pathForm.domain)" :key="career" class="career-tag">
                {{ career }}
              </el-tag>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 学习路径详情 -->
    <div v-if="selectedPath" class="path-detail">
      <!-- 路径概览 -->
      <el-card class="path-overview">
        <template #header>
          <div class="path-header">
            <h2>{{ selectedPath.title }}</h2>
            <el-button @click="backToSelection">重新选择</el-button>
          </div>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ selectedPath.duration_weeks }}周</div>
              <div class="stat-label">预计学习周期</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ selectedPath.difficulty_level }}/5</div>
              <div class="stat-label">难度等级</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ selectedPath.modules?.length || 0 }}</div>
              <div class="stat-label">学习模块</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ getTotalHours() }}小时</div>
              <div class="stat-label">总学习时长</div>
            </div>
          </el-col>
        </el-row>
        
        <div class="path-description">
          <p>{{ selectedPath.description }}</p>
        </div>
      </el-card>

      <!-- 薄弱环节分析 -->
      <el-card v-if="weakAreas && weakAreas.length > 0" class="weak-areas-card">
        <template #header>
          <h3>需要重点提升的能力</h3>
        </template>
        <el-tag v-for="area in weakAreas" :key="area" type="warning" class="weak-area-tag">
          {{ area }}
        </el-tag>
      </el-card>

      <!-- 学习模块 -->
      <el-card class="modules-card">
        <template #header>
          <h3>学习模块</h3>
        </template>
        
        <div class="modules-timeline">
          <div v-for="(module, index) in selectedPath.modules" :key="index" class="module-item">
            <div class="module-number">{{ index + 1 }}</div>
            <div class="module-content">
              <div class="module-header">
                <h4>{{ module.title }}</h4>
                <el-tag :type="getModuleTypeColor(module.type)">{{ getModuleTypeName(module.type) }}</el-tag>
                <span class="module-duration">{{ module.duration_hours }}小时</span>
              </div>
              <p class="module-description">{{ module.description }}</p>
              
              <!-- 学习资源 -->
              <div v-if="module.resources && module.resources.length > 0" class="module-resources">
                <h5>学习资源</h5>
                <div class="resource-list">
                  <div v-for="resource in module.resources" :key="resource.title" class="resource-item">
                    <el-tag size="small" :type="getResourceTypeColor(resource.type)">
                      {{ getResourceTypeName(resource.type) }}
                    </el-tag>
                    <span class="resource-title">{{ resource.title }}</span>
                    <span v-if="resource.author" class="resource-author">- {{ resource.author }}</span>
                  </div>
                </div>
              </div>
              
              <!-- 项目实战 -->
              <div v-if="module.projects && module.projects.length > 0" class="module-projects">
                <h5>实战项目</h5>
                <el-tag v-for="project in module.projects" :key="project" class="project-tag">
                  {{ project }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 职业目标 -->
      <el-card v-if="selectedPath.career_goals" class="career-goals-card">
        <template #header>
          <h3>职业发展目标</h3>
        </template>
        <div class="career-goals">
          <el-tag v-for="goal in selectedPath.career_goals" :key="goal" type="success" class="career-goal-tag">
            {{ goal }}
          </el-tag>
        </div>
      </el-card>

      <!-- 开始学习按钮 -->
      <div class="action-buttons">
        <el-button type="primary" size="large" @click="startLearning">
          开始学习之旅
        </el-button>
        <el-button size="large" @click="downloadPath">
          下载学习计划
        </el-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-loading text="正在生成个性化学习路径..."></el-loading>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import request from '@/utils/request'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const selectedPath = ref(null)
const weakAreas = ref([])
const sessionId = ref(null)

const pathForm = reactive({
  domain: '',
  position: '',
  skillLevel: '中级'
})

// 生命周期
onMounted(() => {
  sessionId.value = route.params.sessionId || route.query.sessionId
  
  // 如果有sessionId，自动加载推荐路径
  if (sessionId.value) {
    loadRecommendedPath()
  }
})

// 方法
const onDomainChange = () => {
  pathForm.position = ''
}

const generatePath = async () => {
  if (!pathForm.domain || !pathForm.position) {
    ElMessage.warning('请选择技术领域和目标岗位')
    return
  }
  
  loading.value = true
  try {
    const response = await request.post('/api/v1/learning-paths/personalized', {
      domain: pathForm.domain,
      position: pathForm.position,
      skill_level: pathForm.skillLevel,
      session_id: sessionId.value
    })
    
    if (response.data.success) {
      selectedPath.value = response.data.data
      ElMessage.success('学习路径生成成功')
    }
  } catch (error) {
    console.error('生成学习路径失败:', error)
    ElMessage.error('生成学习路径失败，请重试')
  } finally {
    loading.value = false
  }
}

const loadRecommendedPath = async () => {
  if (!sessionId.value) {
    ElMessage.warning('未找到面试会话信息')
    return
  }
  
  loading.value = true
  try {
    const response = await request.get(`/api/v1/learning-paths/recommendations/${sessionId.value}`)
    
    if (response.data.success) {
      const data = response.data.data
      selectedPath.value = data.learning_path
      weakAreas.value = data.weak_areas || []
      
      // 自动填充表单
      pathForm.domain = data.session_info.domain
      pathForm.position = data.session_info.position
      pathForm.skillLevel = data.skill_level
      
      ElMessage.success('基于您的面试表现，已为您推荐最适合的学习路径')
    }
  } catch (error) {
    console.error('加载推荐路径失败:', error)
    ElMessage.error('加载推荐路径失败，请重试')
  } finally {
    loading.value = false
  }
}

const backToSelection = () => {
  selectedPath.value = null
  weakAreas.value = []
}

const getTotalHours = () => {
  if (!selectedPath.value?.modules) return 0
  return selectedPath.value.modules.reduce((total, module) => total + (module.duration_hours || 0), 0)
}

const getDomainDescription = (domain) => {
  const descriptions = {
    '人工智能': '人工智能是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。包括机器学习、深度学习、自然语言处理等技术。',
    '大数据': '大数据技术专注于处理和分析大规模、复杂的数据集，帮助企业从数据中获取有价值的洞察和商业价值。',
    '物联网': '物联网是通过互联网连接各种物理设备，实现设备间的数据交换和智能控制，构建智能化的生活和工作环境。'
  }
  return descriptions[domain] || ''
}

const getCareerProspects = (domain) => {
  const prospects = {
    '人工智能': ['AI算法工程师', '机器学习工程师', '深度学习研究员', 'AI产品经理', '数据科学家'],
    '大数据': ['大数据工程师', '数据分析师', '数据架构师', '大数据产品经理', 'BI工程师'],
    '物联网': ['IoT工程师', '嵌入式开发工程师', '物联网架构师', '智能硬件工程师', '物联网产品经理']
  }
  return prospects[domain] || []
}

const getModuleTypeColor = (type) => {
  const colors = {
    'theory': 'primary',
    'practice': 'success',
    'project': 'warning'
  }
  return colors[type] || 'info'
}

const getModuleTypeName = (type) => {
  const names = {
    'theory': '理论学习',
    'practice': '实践练习',
    'project': '项目实战'
  }
  return names[type] || '其他'
}

const getResourceTypeColor = (type) => {
  const colors = {
    'video': 'primary',
    'book': 'success',
    'course': 'warning',
    'practice': 'info',
    'tool': 'danger'
  }
  return colors[type] || 'info'
}

const getResourceTypeName = (type) => {
  const names = {
    'video': '视频教程',
    'book': '推荐书籍',
    'course': '在线课程',
    'practice': '练习题集',
    'tool': '工具文档'
  }
  return names[type] || '其他'
}

const startLearning = () => {
  ElMessage.success('学习功能正在开发中，敬请期待！')
}

const downloadPath = () => {
  ElMessage.success('下载功能正在开发中，敬请期待！')
}
</script>

<style scoped>
.learning-path-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.header-card {
  margin-bottom: 20px;
}

.header-content {
  text-align: center;
}

.header-content h1 {
  color: #409EFF;
  margin-bottom: 10px;
}

.selection-card {
  margin-bottom: 20px;
}

.domain-info {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.career-prospects {
  margin-top: 15px;
}

.career-tag {
  margin: 5px;
}

.path-detail {
  margin-top: 20px;
}

.path-overview {
  margin-bottom: 20px;
}

.path-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-item {
  text-align: center;
  padding: 20px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
}

.stat-label {
  color: #666;
  margin-top: 5px;
}

.path-description {
  margin-top: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.weak-areas-card {
  margin-bottom: 20px;
}

.weak-area-tag {
  margin: 5px;
}

.modules-card {
  margin-bottom: 20px;
}

.modules-timeline {
  position: relative;
}

.module-item {
  display: flex;
  margin-bottom: 30px;
  position: relative;
}

.module-number {
  width: 40px;
  height: 40px;
  background: #409EFF;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 20px;
  flex-shrink: 0;
}

.module-content {
  flex: 1;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.module-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.module-header h4 {
  margin: 0;
  flex: 1;
}

.module-duration {
  color: #666;
  font-size: 14px;
}

.module-description {
  color: #666;
  margin-bottom: 15px;
}

.module-resources, .module-projects {
  margin-top: 15px;
}

.module-resources h5, .module-projects h5 {
  margin: 0 0 10px 0;
  color: #333;
}

.resource-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.resource-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.resource-title {
  font-weight: 500;
}

.resource-author {
  color: #666;
  font-size: 14px;
}

.project-tag {
  margin: 5px;
}

.career-goals-card {
  margin-bottom: 20px;
}

.career-goal-tag {
  margin: 5px;
}

.action-buttons {
  text-align: center;
  margin-top: 30px;
}

.action-buttons .el-button {
  margin: 0 10px;
}

.loading-container {
  text-align: center;
  padding: 50px;
}
</style>
