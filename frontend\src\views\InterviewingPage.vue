<template>
  <div class="interview-page">
    <div class="header">
      <h1 class="title">{{ interviewTitle }}</h1>
      <el-button type="danger" :icon="CircleCloseFilled" @click="endCurrentInterview" class="end-button">
        结束面试
      </el-button>
    </div>

    <div class="chat-container">
      <div class="message-container" ref="chatWindowRef">
        <div v-for="(msg, index) in processedMessages" :key="index" :class="['message', msg.role]">
          <div v-if="msg.role === 'assistant'" class="assistant-message-wrapper">
            <details class="thinking-process" :open="!msg.thinkingDone">
              <summary>AI 思考过程</summary>
              <div class="message-content thinking-content" v-html="msg.thinkingHtml"></div>
            </details>
            <div v-if="msg.reply" class="message-content final-reply" v-html="msg.replyHtml"></div>
          </div>
          <div v-else class="message-content">{{ msg.content }}</div>
        </div>
      </div>
    </div>

    <div class="input-area">
      <el-input
          v-model="userInput"
          placeholder="请输入您的回答..."
          @keyup.enter="handleSendMessage"
          :disabled="isThinking || interviewEnded"
          class="input-box"
          size="large"
      >
        <template #append>
          <el-button @click="handleSendMessage" :disabled="isThinking || interviewEnded">
            发送
          </el-button>
        </template>
      </el-input>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { CircleCloseFilled } from "@element-plus/icons-vue";
import { startInterview as startInterviewApi, nextQuestion as getNextQuestionApi, endInterview as endInterviewApi } from '@/services/api'
import 'katex/dist/katex.min.css'
import MarkdownIt from 'markdown-it'
import mk from 'markdown-it-katex'

// --- Vue Router and Markdown Initialization ---
const route = useRoute()
const router = useRouter()
const md = new MarkdownIt().use(mk);

// --- Component State ---
const messages = ref([])
const userInput = ref('')
const isThinking = ref(false)
const interviewEnded = ref(false)
const chatWindowRef = ref(null)

// --- Computed Properties ---
const interviewTitle = computed(() => {
  const domain = route.query.domain || '未知领域';
  const position = route.query.position || '未知岗位';
  return `当前面试岗位：${position}（${domain}领域）`;
});

const processedMessages = computed(() => {
  return messages.value.map(msg => {
    if (msg.role === 'assistant') {
      return {
        ...msg,
        thinkingHtml: md.render(msg.thinking || ''),
        replyHtml: msg.reply ? md.render(msg.reply) : ''
      };
    }
    return msg;
  });
});

// --- Helper Functions ---
const scrollToBottom = () => {
  nextTick(() => {
    if (chatWindowRef.value) {
      chatWindowRef.value.scrollTop = chatWindowRef.value.scrollHeight;
    }
  });
};

// --- API Interaction ---
const startInterview = async () => {
  isThinking.value = true;
  const assistantMessageIndex = messages.value.length;
  messages.value.push({
    role: 'assistant',
    thinking: '面试官正在进入面试间，并准备第一个问题...',
    reply: '',
    thinkingDone: false
  });
  scrollToBottom();
  
  try {
    const payload = {
      domain: route.query.domain,
      position: route.query.position
    };
    // 给后端一点时间准备
    await new Promise(resolve => setTimeout(resolve, 1000));

    const response = await startInterviewApi(payload);
    const msg = messages.value[assistantMessageIndex];
    msg.thinking = response.data.thinking || '已为您准备好第一个问题。';
    msg.reply = response.data.question;
    msg.thinkingDone = true;
  } catch (error) {
    console.error('Failed to start interview:', error);
    const msg = messages.value[assistantMessageIndex];
    msg.thinking = '面试初始化失败。';
    msg.reply = '抱歉，面试初始化失败，请返回重试。';
    msg.thinkingDone = true;
    ElMessage.error('面试初始化失败');
  } finally {
    isThinking.value = false;
    scrollToBottom();
  }
};

const sendMessage = async () => {
  if (!userInput.value.trim() || isThinking.value) return;

  const userMessageContent = userInput.value;
  messages.value.push({ role: 'user', content: userMessageContent });
  userInput.value = '';
  await nextTick();
  scrollToBottom();
  
  isThinking.value = true;
  const assistantMessageIndex = messages.value.length;
  messages.value.push({
    role: 'assistant',
    thinking: '',
    reply: '',
    thinkingDone: false
  });
  await nextTick();
  scrollToBottom();

  // 模拟流式思考过程
  const thinkingText = "好的，我收到了您的回答。让我想一想...\n\n1. 正在分析您的回答与岗位要求的匹配度。\n2. 检索我的知识库，寻找相关的追问点。\n3. 构思下一个问题...";
  let currentThinkingText = '';
  const intervalId = setInterval(() => {
    if (currentThinkingText.length >= thinkingText.length) {
      clearInterval(intervalId);
      messages.value[assistantMessageIndex].thinking = thinkingText;
      fetchNextQuestion(assistantMessageIndex);
      return;
    }
    const nextChunk = thinkingText.substring(currentThinkingText.length, currentThinkingText.length + 5);
    currentThinkingText += nextChunk;
    messages.value[assistantMessageIndex].thinking = currentThinkingText + '...';
    scrollToBottom();
  }, 100);
};

const fetchNextQuestion = async (index) => {
  try {
    const history = messages.value.slice(0, index).map(m => {
      if (m.role === 'user') return { role: 'user', content: m.content };
      if (m.role === 'assistant' && m.reply) return { role: 'assistant', content: m.reply };
      return null;
    }).filter(Boolean);

    const payload = {
      messages: history,
      domain: route.query.domain,
      position: route.query.position
    };
    
    const response = await getNextQuestionApi(payload);
    const msg = messages.value[index];
    msg.thinking = response.data.thinking || '思考完毕！';
    msg.reply = response.data.question;
    msg.thinkingDone = true;
  } catch (error) {
    console.error('Failed to get next question:', error);
    const msg = messages.value[index];
    msg.thinking = "出现错误。";
    msg.reply = '抱歉，我遇到了一些麻烦，请稍后再试。';
    msg.thinkingDone = true;
    ElMessage.error('获取 AI 回复失败');
  } finally {
    isThinking.value = false;
    scrollToBottom();
  }
};

const endInterview = () => {
  ElMessageBox.confirm('您确定要结束本次面试吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    // 获取sessionId
    const sessionId = route.params.sessionId

    if (sessionId) {
      // 跳转到报告页面，传递sessionId
      router.push({
        name: 'Report',
        params: {
          sessionId: sessionId
        }
      });
    } else {
      // 如果没有sessionId，跳转到面试选择页面
      ElMessage.warning('未找到面试会话ID，返回面试选择页面')
      router.push('/interview-selection')
    }
  }).catch(() => {
    // User cancelled
  });
};

const handleSendMessage = () => {
  sendMessage();
};

const endCurrentInterview = () => {
  endInterview();
};

// --- Lifecycle Hooks ---
onMounted(() => {
  // 检查是否有sessionId参数
  const sessionId = route.params.sessionId

  if (sessionId) {
    // 如果有sessionId，说明是从面试选择页面跳转过来的
    // 可以开始面试或继续现有面试
    if (route.query.domain && route.query.position) {
      startInterview();
    } else {
      ElMessage.error('缺少面试配置信息');
      router.push('/interview-selection');
    }
  } else if (route.query.domain && route.query.position) {
    // 兼容旧的查询参数方式
    startInterview();
  } else {
    ElMessage.error('无效的面试设置，请返回面试选择页面');
    router.push('/interview-selection');
  }
});
</script>

<style scoped>
.interview-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f0f2f5;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: #ffffff;
  border-bottom: 1px solid #dcdfe6;
  flex-shrink: 0;
}

.title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.chat-container {
  flex-grow: 1;
  overflow-y: hidden;
  padding: 1rem;
  display: flex;
  flex-direction: column;
}

.message-container {
  overflow-y: auto;
  flex-grow: 1;
  padding: 0 1rem;
}

.message {
  display: flex;
  margin-bottom: 1rem;
  max-width: 80%;
  align-self: flex-start;
}

.message.user {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.assistant-message-wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.thinking-process {
  border: 1px solid #e0e0e0;
  border-radius: 10px;
  margin-bottom: 0.5rem;
  background-color: #f9f9f9;
}

.thinking-process summary {
  padding: 0.5rem 1rem;
  font-weight: bold;
  cursor: pointer;
  color: #606266;
}

.thinking-content {
  background-color: transparent !important;
  color: #606266;
}

.final-reply {
  background-color: #e9eef3;
  align-self: flex-start;
  border-radius: 18px;
  border-top-left-radius: 5px;
}

.message-content {
  padding: 0.8rem 1rem;
  border-radius: 18px;
  word-wrap: break-word;
  white-space: pre-wrap;
  line-height: 1.6;
}

.message.user .message-content {
  background-color: #409eff;
  color: #fff;
  border-bottom-right-radius: 4px;
}

.message.assistant .message-content {
  background-color: #ffffff;
  color: #333;
  border-bottom-left-radius: 4px;
  text-align: left;
}

.input-area {
  padding: 1rem 2rem;
  background-color: #ffffff;
  border-top: 1px solid #dcdfe6;
  flex-shrink: 0;
}

.dot-flashing {
  position: relative;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: #909399;
  color: #909399;
  animation: dot-flashing 1s infinite linear alternate;
  animation-delay: 0.5s;
  display: inline-block;
  margin: 0 8px;
}

.dot-flashing::before, .dot-flashing::after {
  content: "";
  display: inline-block;
  position: absolute;
  top: 0;
}

.dot-flashing::before {
  left: -15px;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: #909399;
  color: #909399;
  animation: dot-flashing 1s infinite alternate;
  animation-delay: 0s;
}

.dot-flashing::after {
  left: 15px;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: #909399;
  color: #909399;
  animation: dot-flashing 1s infinite alternate;
  animation-delay: 1s;
}

@keyframes dot-flashing {
  0% {
    background-color: #909399;
  }
  50%, 100% {
    background-color: #dcdfe6;
  }
}

/* Styles for rendered markdown */
.message-content :deep(h1),
.message-content :deep(h2),
.message-content :deep(h3) {
  margin-top: 1.2em;
  margin-bottom: 0.6em;
  font-weight: 600;
}

.message-content :deep(p) {
  margin-bottom: 1em;
}

.message-content :deep(pre) {
  background-color: #f4f5f7;
  padding: 1em;
  border-radius: 8px;
  overflow-x: auto;
  font-family: 'Courier New', Courier, monospace;
}

.message-content :deep(code) {
  background-color: #f4f5f7;
  padding: 0.2em 0.4em;
  border-radius: 4px;
  font-family: 'Courier New', Courier, monospace;
}

.message-content :deep(pre) > :deep(code) {
  background-color: transparent;
  padding: 0;
}

.message-content :deep(table) {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1em;
  display: table;
}

.message-content :deep(th),
.message-content :deep(td) {
  border: 1px solid #dfe2e5;
  padding: 0.6em 1em;
}

.message-content :deep(th) {
  font-weight: 600;
  background-color: #f4f5f7;
}

.message-content :deep(ul),
.message-content :deep(ol) {
  padding-left: 2em;
  margin-bottom: 1em;
}

.message-content :deep(blockquote) {
  border-left: 4px solid #dfe2e5;
  padding-left: 1em;
  color: #6a737d;
  margin-left: 0;
  margin-right: 0;
}
</style>
