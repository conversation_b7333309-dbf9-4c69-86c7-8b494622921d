<template>
  <el-row justify="center" align="middle" style="height: 100%;">
    <el-col :span="12">
      <el-card>
        <template #header>
          <h1>欢迎使用多模态智能模拟面试评测系统</h1>
        </template>
        <p>
          本系统旨在通过模拟真实的面试场景，利用人工智能和多模态分析技术，对您的面试表现进行智能评测，并生成详细的反馈报告，帮助您提升面试技巧，增强就业竞争力。
        </p>
        <el-button type="primary" size="large" @click="startInterview">
          开始模拟面试
        </el-button>
      </el-card>
    </el-col>
  </el-row>
</template>

<script setup>
import { useRouter } from 'vue-router';

const router = useRouter();

const startInterview = () => {
  router.push('/interview-selection');
};
</script> 