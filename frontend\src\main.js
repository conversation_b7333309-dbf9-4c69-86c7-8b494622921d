import { createApp } from 'vue'

// 创建一个内联的测试组件
const TestApp = {
  template: `
    <div style="
      background: red !important;
      color: white !important;
      padding: 50px !important;
      font-size: 30px !important;
      min-height: 100vh !important;
      font-family: Arial !important;
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      bottom: 0 !important;
      z-index: 9999 !important;
    ">
      <h1 style="color: white !important; font-size: 40px !important;">🎉 Vue应用正常工作！</h1>
      <p style="color: white !important; font-size: 20px !important;">如果您能看到这个红色背景和白色文字，说明Vue应用已经成功渲染</p>
      <button @click="testClick" style="
        background: blue !important;
        color: white !important;
        padding: 20px !important;
        font-size: 18px !important;
        border: none !important;
        cursor: pointer !important;
        margin: 20px 0 !important;
      ">点击测试交互</button>
      <p v-if="clicked" style="color: yellow !important; font-size: 24px !important;">✅ 点击事件正常工作！</p>
    </div>
  `,
  data() {
    return {
      clicked: false
    }
  },
  methods: {
    testClick() {
      this.clicked = true
      console.log('🖱️ 按钮点击成功！')
    }
  },
  mounted() {
    console.log('🎯 测试组件已挂载')
    // 强制显示
    document.body.style.margin = '0'
    document.body.style.padding = '0'
    document.body.style.overflow = 'hidden'
  }
}

const app = createApp(TestApp)
app.mount('#app')