["tests/test_report_generation.py::TestImprovementSuggestionService::test_empty_capability_scores", "tests/test_report_generation.py::TestImprovementSuggestionService::test_generate_improvement_suggestions", "tests/test_report_generation.py::TestImprovementSuggestionService::test_generate_priority_recommendations", "tests/test_report_generation.py::TestImprovementSuggestionService::test_get_specific_actions", "tests/test_report_generation.py::TestImprovementSuggestionService::test_priority_assignment", "tests/test_report_generation.py::TestReportVisualizationService::test_generate_capability_breakdown_chart", "tests/test_report_generation.py::TestReportVisualizationService::test_generate_performance_trend_chart", "tests/test_report_generation.py::TestReportVisualizationService::test_generate_performance_trend_chart_empty", "tests/test_report_generation.py::TestReportVisualizationService::test_generate_radar_chart", "tests/test_report_generation.py::TestReportVisualizationService::test_generate_radar_chart_empty_data", "tests/test_report_generation.py::TestServiceIntegration::test_global_service_instances", "tests/test_report_generation.py::TestServiceIntegration::test_service_integration"]