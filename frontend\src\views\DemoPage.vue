<template>
  <div class="demo-page">
    <!-- 演示页面头部 -->
    <div class="demo-header">
      <div class="header-content">
        <h1 class="demo-title">
          <el-icon><VideoPlay /></el-icon>
          系统演示
        </h1>
        <p class="demo-subtitle">体验多模态智能面试评测系统的完整功能</p>

        <!-- 演示统计信息 -->
        <div class="demo-stats" v-if="demoStats.totalVideos">
          <div class="stat-item">
            <span class="stat-number">{{ demoStats.totalVideos }}</span>
            <span class="stat-label">演示视频</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ demoStats.totalFeatures }}</span>
            <span class="stat-label">核心功能</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ demoStats.totalSteps }}</span>
            <span class="stat-label">体验步骤</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ Math.round(demoStats.estimatedTotalTime / 60) || 30 }}分钟</span>
            <span class="stat-label">预计时长</span>
          </div>
        </div>

        <!-- 导航标签 -->
        <el-tabs v-model="activeTab" class="demo-tabs" @tab-click="handleTabClick">
          <el-tab-pane label="功能演示" name="features">
            <el-icon><Star /></el-icon>
          </el-tab-pane>
          <el-tab-pane label="视频教程" name="video">
            <el-icon><VideoCamera /></el-icon>
          </el-tab-pane>
          <el-tab-pane label="交互体验" name="interactive">
            <el-icon><Mouse /></el-icon>
          </el-tab-pane>
          <el-tab-pane label="技术架构" name="architecture">
            <el-icon><Setting /></el-icon>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 演示内容区域 -->
    <div class="demo-content">
      <!-- 功能演示 -->
      <div v-show="activeTab === 'features'" class="features-demo">
        <div class="features-grid">
          <div 
            v-for="(feature, index) in features" 
            :key="index"
            class="feature-card"
            @click="showFeatureDemo(feature)"
          >
            <div class="feature-icon">
              <el-icon :size="40">
                <component :is="feature.icon" />
              </el-icon>
            </div>
            <h3>{{ feature.title }}</h3>
            <p>{{ feature.description }}</p>
            <div class="feature-actions">
              <el-button type="primary" size="small" @click.stop="playFeatureDemo(feature)">
                <el-icon><VideoPlay /></el-icon>
                观看演示
              </el-button>
              <el-button size="small" @click.stop="tryFeature(feature)">
                <el-icon><Operation /></el-icon>
                立即体验
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 视频教程 -->
      <div v-show="activeTab === 'video'" class="video-demo">
        <!-- 主演示视频区域 -->
        <div class="main-video-section">
          <div class="video-player-container">
            <div class="video-player">
              <div class="video-placeholder">
                <el-icon :size="80"><VideoCamera /></el-icon>
                <h3>{{ mainVideoDetails.title }}</h3>
                <p>{{ mainVideoDetails.description }}</p>

                <!-- 视频信息卡片 -->
                <div class="video-info-card">
                  <div class="video-stats">
                    <span class="stat-item">
                      <el-icon><Clock /></el-icon>
                      {{ mainVideoDetails.duration }}
                    </span>
                    <span class="stat-item">
                      <el-icon><View /></el-icon>
                      {{ mainVideoDetails.views }}次观看
                    </span>
                    <span class="stat-item">
                      <el-icon><Star /></el-icon>
                      {{ mainVideoDetails.rating }}分
                    </span>
                    <span class="stat-item">
                      <el-icon><Calendar /></el-icon>
                      {{ mainVideoDetails.publishDate }}
                    </span>
                  </div>

                  <!-- 技术特性标签 -->
                  <div class="tech-features" v-if="mainVideoDetails.features">
                    <el-tag
                      v-for="feature in mainVideoDetails.features.filter(f => f.highlight)"
                      :key="feature.name"
                      type="primary"
                      size="small"
                      class="feature-tag"
                    >
                      {{ feature.name }}
                    </el-tag>
                  </div>
                </div>

                <div class="video-actions">
                  <el-button type="primary" size="large" @click="playMainVideo">
                    <el-icon><VideoPlay /></el-icon>
                    播放完整演示
                  </el-button>
                  <el-button size="large" @click="showChapterNavigation = !showChapterNavigation">
                    <el-icon><List /></el-icon>
                    章节导航
                  </el-button>
                </div>
              </div>
            </div>

            <!-- 章节导航面板 -->
            <div v-if="showChapterNavigation" class="chapter-navigation">
              <h4>
                <el-icon><List /></el-icon>
                视频章节 ({{ videoChapters.length }}个)
              </h4>
              <div class="chapters-list">
                <div
                  v-for="(chapter, index) in videoChapters"
                  :key="index"
                  class="chapter-item"
                  @click="jumpToChapter(index)"
                >
                  <div class="chapter-thumbnail">
                    <img v-if="chapter.thumbnail" :src="chapter.thumbnail" :alt="chapter.title" />
                    <el-icon v-else><VideoPlay /></el-icon>
                  </div>
                  <div class="chapter-info">
                    <div class="chapter-header">
                      <span class="chapter-time">{{ chapter.time }}</span>
                      <span class="chapter-duration">{{ chapter.duration }}</span>
                    </div>
                    <h5>{{ chapter.title }}</h5>
                    <p>{{ chapter.description }}</p>
                    <div class="chapter-keypoints" v-if="chapter.keyPoints">
                      <el-tag
                        v-for="point in chapter.keyPoints"
                        :key="point"
                        size="mini"
                        type="info"
                      >
                        {{ point }}
                      </el-tag>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 技术规格展示 -->
          <div class="tech-specs-panel" v-if="mainVideoDetails.technicalSpecs">
            <h4>
              <el-icon><Setting /></el-icon>
              技术规格
            </h4>
            <div class="specs-grid">
              <div class="spec-item" v-for="(value, key) in mainVideoDetails.technicalSpecs" :key="key">
                <span class="spec-label">{{ getSpecLabel(key) }}</span>
                <span class="spec-value">{{ value }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 演示场景选择 -->
        <div class="demo-scenarios-section">
          <h3>
            <el-icon><Guide /></el-icon>
            演示场景
          </h3>
          <div class="scenarios-grid">
            <div
              v-for="(scenario, key) in demoScenarios"
              :key="key"
              class="scenario-card"
              @click="startDemoScenario(key)"
            >
              <div class="scenario-header">
                <h4>{{ scenario.title }}</h4>
                <el-tag :type="getDifficultyType(scenario.difficulty)">
                  {{ scenario.difficulty }}
                </el-tag>
              </div>
              <p>{{ scenario.description }}</p>
              <div class="scenario-meta">
                <span class="scenario-time">
                  <el-icon><Clock /></el-icon>
                  {{ scenario.estimatedTime }}
                </span>
                <span class="scenario-steps">
                  <el-icon><List /></el-icon>
                  {{ scenario.steps.length }}个步骤
                </span>
              </div>
              <el-button type="primary" size="small" class="scenario-btn">
                开始演示
              </el-button>
            </div>
          </div>
        </div>

        <!-- 分步教程 -->
        <div class="video-tutorials">
          <h3>
            <el-icon><VideoCamera /></el-icon>
            分步教程
          </h3>
          <div class="tutorials-grid">
            <div
              v-for="(video, index) in videoTutorials"
              :key="index"
              class="video-item"
              @click="playVideo(video)"
            >
              <div class="video-thumbnail">
                <el-icon><VideoPlay /></el-icon>
                <div class="video-overlay">
                  <span class="video-duration">{{ video.duration }}</span>
                </div>
              </div>
              <div class="video-info">
                <h4>{{ video.title }}</h4>
                <p>{{ video.description }}</p>
                <div class="video-meta">
                  <span class="video-views">
                    <el-icon><View /></el-icon>
                    {{ video.views }}次观看
                  </span>
                  <span class="video-rating">
                    <el-icon><Star /></el-icon>
                    {{ video.rating }}
                  </span>
                  <el-tag :type="getDifficultyType(video.difficulty)" size="small">
                    {{ video.difficulty }}
                  </el-tag>
                </div>
                <div class="video-tags" v-if="video.tags">
                  <el-tag
                    v-for="tag in video.tags"
                    :key="tag"
                    size="mini"
                    type="info"
                  >
                    {{ tag }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 交互体验 -->
      <div v-show="activeTab === 'interactive'" class="interactive-demo">
        <div class="demo-simulator">
          <h3>模拟面试体验</h3>
          <p>在这里您可以体验完整的面试流程，无需注册即可开始</p>
          
          <div class="simulator-steps">
            <div 
              v-for="(step, index) in simulatorSteps" 
              :key="index"
              class="step-card"
              :class="{ active: currentStep === index, completed: currentStep > index }"
              @click="goToStep(index)"
            >
              <div class="step-number">{{ index + 1 }}</div>
              <div class="step-content">
                <h4>{{ step.title }}</h4>
                <p>{{ step.description }}</p>

                <!-- 步骤详细信息 -->
                <div class="step-details" v-if="step.estimatedTime || step.difficulty">
                  <div class="step-meta">
                    <span v-if="step.estimatedTime" class="step-time">
                      <el-icon><Clock /></el-icon>
                      {{ step.estimatedTime }}
                    </span>
                    <span v-if="step.difficulty" class="step-difficulty">
                      <el-tag :type="getDifficultyType(step.difficulty)" size="small">
                        {{ step.difficulty }}
                      </el-tag>
                    </span>
                  </div>
                </div>

                <!-- 交互元素提示 -->
                <div v-if="step.interactiveElements && step.interactiveElements.length > 0" class="interactive-hint">
                  <el-icon><Operation /></el-icon>
                  <span>{{ step.interactiveElements.length }}个交互元素</span>
                </div>
              </div>
              <div class="step-status">
                <el-icon v-if="currentStep > index"><Check /></el-icon>
                <el-icon v-else-if="currentStep === index"><ArrowRight /></el-icon>
              </div>
            </div>
          </div>
          
          <div class="simulator-actions">
            <el-button 
              v-if="currentStep < simulatorSteps.length - 1"
              type="primary" 
              @click="nextStep"
            >
              下一步
              <el-icon><ArrowRight /></el-icon>
            </el-button>
            <el-button 
              v-else
              type="success" 
              @click="startRealInterview"
            >
              开始真实面试
              <el-icon><VideoPlay /></el-icon>
            </el-button>
            <el-button @click="resetSimulator">重新开始</el-button>
          </div>
        </div>
      </div>

      <!-- 技术架构 -->
      <div v-show="activeTab === 'architecture'" class="architecture-demo">
        <div class="architecture-overview">
          <h3>{{ architectureData.title || '技术架构图' }}</h3>
          <p v-if="architectureData.description" class="arch-description">
            {{ architectureData.description }}
          </p>

          <div class="architecture-diagram">
            <div
              v-for="(layer, index) in architectureData.layers"
              :key="index"
              class="arch-layer"
              :class="layer.name.toLowerCase().replace(/[^a-z]/g, '')"
            >
              <h4>{{ layer.name }}</h4>
              <p class="layer-description">{{ layer.description }}</p>
              <div class="tech-stack">
                <span
                  v-for="tech in layer.technologies"
                  :key="tech"
                  class="tech-item"
                >
                  {{ tech }}
                </span>
              </div>
            </div>
          </div>

          <div class="architecture-features">
            <div
              v-for="(feature, index) in architectureData.features"
              :key="index"
              class="feature-highlight"
            >
              <el-icon><component :is="getFeatureIcon(feature.name)" /></el-icon>
              <h4>{{ feature.name }}</h4>
              <p>{{ feature.description }}</p>
              <div v-if="feature.metrics" class="feature-metrics">
                <span
                  v-for="(value, key) in feature.metrics"
                  :key="key"
                  class="metric-item"
                >
                  {{ key }}: {{ value }}
                </span>
              </div>
            </div>
          </div>

          <!-- 技术规格 -->
          <div v-if="architectureData.specifications" class="tech-specifications">
            <h4>技术规格</h4>
            <div class="spec-grid">
              <div
                v-for="(spec, index) in architectureData.specifications"
                :key="index"
                class="spec-item"
              >
                <strong>{{ spec.category }}</strong>
                <ul>
                  <li v-for="detail in spec.details" :key="detail">{{ detail }}</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 返回按钮 -->
    <div class="demo-footer">
      <el-button @click="goBack">
        <el-icon><ArrowLeft /></el-icon>
        返回首页
      </el-button>
      <el-button type="primary" @click="startInterview">
        <el-icon><VideoPlay /></el-icon>
        开始面试
      </el-button>
    </div>

    <!-- 功能演示对话框 -->
    <el-dialog
      v-model="showFeatureDialog"
      :title="selectedFeature?.title"
      width="90%"
      center
      :show-close="true"
    >
      <FeatureShowcase
        v-if="selectedFeature"
        :feature="selectedFeature"
        @start-demo="handleStartDemo"
        @try-feature="handleTryFeature"
        @view-details="handleViewDetails"
      />
    </el-dialog>

    <!-- 增强的视频播放对话框 -->
    <el-dialog
      v-model="showVideoDialog"
      :title="selectedVideo?.title"
      width="95%"
      center
      class="video-dialog"
    >
      <div class="video-player-dialog">
        <!-- 主视频播放区域 -->
        <div class="video-main-area">
          <div class="video-placeholder-large">
            <el-icon :size="100"><VideoCamera /></el-icon>
            <h3>{{ selectedVideo?.title }}</h3>
            <p>{{ selectedVideo?.description }}</p>

            <!-- 视频详细信息 -->
            <div class="video-details" v-if="selectedVideo">
              <div class="video-stats-row">
                <span class="stat-badge">
                  <el-icon><Clock /></el-icon>
                  {{ selectedVideo.duration }}
                </span>
                <span class="stat-badge">
                  <el-icon><View /></el-icon>
                  {{ selectedVideo.views }}次观看
                </span>
                <span class="stat-badge">
                  <el-icon><Star /></el-icon>
                  {{ selectedVideo.rating }}分
                </span>
                <el-tag v-if="selectedVideo.difficulty" :type="getDifficultyType(selectedVideo.difficulty)">
                  {{ selectedVideo.difficulty }}
                </el-tag>
              </div>

              <!-- 技术特性 -->
              <div class="video-features" v-if="selectedVideo.features">
                <h4>核心特性</h4>
                <div class="features-list">
                  <div
                    v-for="feature in selectedVideo.features"
                    :key="feature.name"
                    class="feature-item"
                    :class="{ highlight: feature.highlight }"
                  >
                    <strong>{{ feature.name }}</strong>
                    <span>{{ feature.description }}</span>
                  </div>
                </div>
              </div>

              <!-- 技术规格 -->
              <div class="video-tech-specs" v-if="selectedVideo.technicalSpecs">
                <h4>技术规格</h4>
                <div class="tech-specs-compact">
                  <div v-for="(value, key) in selectedVideo.technicalSpecs" :key="key" class="spec-row">
                    <span class="spec-key">{{ getSpecLabel(key) }}</span>
                    <span class="spec-val">{{ value }}</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="video-controls">
              <el-button type="primary" size="large" @click="startVideoPlayback">
                <el-icon><VideoPlay /></el-icon>
                开始播放
              </el-button>
              <el-button size="large" v-if="selectedVideo?.chapters" @click="showChapterNavigation = !showChapterNavigation">
                <el-icon><List /></el-icon>
                章节导航
              </el-button>
            </div>
          </div>
        </div>

        <!-- 章节导航侧边栏 -->
        <div v-if="showChapterNavigation && selectedVideo?.chapters" class="video-chapters-sidebar">
          <h4>
            <el-icon><List /></el-icon>
            视频章节
          </h4>
          <div class="chapters-compact-list">
            <div
              v-for="(chapter, index) in selectedVideo.chapters"
              :key="index"
              class="chapter-compact-item"
              @click="jumpToChapter(index)"
            >
              <div class="chapter-time-badge">{{ chapter.time }}</div>
              <div class="chapter-content">
                <h5>{{ chapter.title }}</h5>
                <p>{{ chapter.description }}</p>
                <div class="chapter-tags" v-if="chapter.keyPoints">
                  <el-tag
                    v-for="point in chapter.keyPoints.slice(0, 2)"
                    :key="point"
                    size="mini"
                    type="info"
                  >
                    {{ point }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  VideoPlay, VideoCamera, Star, Mouse, Setting, Operation,
  Check, ArrowRight, ArrowLeft, Lightning, Lock, Expand,
  ChatDotRound, TrendCharts, Document, Clock, View, List,
  Calendar, Guide
} from '@element-plus/icons-vue'
import DemoService from '../services/demoService.js'
import FeatureShowcase from '../components/Demo/FeatureShowcase.vue'

const router = useRouter()

// 响应式数据
const activeTab = ref('features')
const currentStep = ref(0)
const showFeatureDialog = ref(false)
const showVideoDialog = ref(false)
const selectedFeature = ref(null)
const selectedVideo = ref(null)
const demoStats = ref({})

// 新增的演示功能数据
const showChapterNavigation = ref(false)
const mainVideoDetails = ref({})
const videoChapters = ref([])
const demoScenarios = ref({})
const currentScenario = ref(null)
const scenarioProgress = ref(0)

// 从服务获取数据
const features = ref([])
const videoTutorials = ref([])
const simulatorSteps = ref([])
const architectureData = ref({})

// 初始化数据
onMounted(() => {
  loadDemoData()
})

const loadDemoData = () => {
  // 从服务加载演示数据
  const featuresData = DemoService.getFeatures()
  features.value = featuresData.map(feature => ({
    icon: feature.icon,
    title: feature.title,
    description: feature.description,
    demoDescription: feature.highlights.join('、'),
    id: feature.id,
    highlights: feature.highlights,
    demoSteps: feature.demoSteps,
    category: feature.category,
    difficulty: feature.difficulty,
    estimatedTime: feature.estimatedTime,
    technicalSpecs: feature.technicalSpecs
  }))

  const videosData = DemoService.getVideos()
  videoTutorials.value = videosData || []

  const stepsData = DemoService.getInteractiveSteps()
  simulatorSteps.value = stepsData.map(step => ({
    title: step.title,
    description: step.description,
    id: step.id,
    tips: step.tips,
    mockData: step.mockData,
    estimatedTime: step.estimatedTime,
    difficulty: step.difficulty,
    interactiveElements: step.interactiveElements
  }))

  architectureData.value = DemoService.getArchitecture()
  demoStats.value = DemoService.getDemoStats()

  // 加载新的演示功能数据
  mainVideoDetails.value = DemoService.getMainVideoDetails()
  videoChapters.value = DemoService.getVideoChapters('main-demo')
  demoScenarios.value = DemoService.getDemoScenarios()
}

// 方法
const handleTabClick = (tab) => {
  console.log('切换到标签:', tab.props.name)
}

const showFeatureDemo = (feature) => {
  selectedFeature.value = feature
  showFeatureDialog.value = true
}

const playFeatureDemo = (feature) => {
  const result = DemoService.startFeatureDemo(feature.id)
  if (result.success) {
    ElMessage.success(result.message)
    showFeatureDemo(feature)
  } else {
    ElMessage.error(result.message)
  }
}

const tryFeature = (feature) => {
  ElMessage.info(`即将体验 ${feature.title} 功能`)
  // 根据不同功能跳转到相应页面
  switch (feature.id) {
    case 'multimodal-input':
      router.push('/interview-selection')
      break
    case 'comprehensive-report':
      // 如果有测试报告，跳转到报告页面
      router.push('/test-report-to-learning')
      break
    case 'ai-interaction':
      router.push('/interview-selection')
      break
    default:
      router.push('/interview-selection')
  }
}

const playMainVideo = () => {
  const result = DemoService.playVideo('main-demo')
  if (result.success) {
    ElMessage.success(result.message)
    selectedVideo.value = result.video
    showVideoDialog.value = true
  } else {
    ElMessage.error(result.message)
  }
}

const playVideo = (video) => {
  const result = DemoService.playVideo(video.id)
  if (result.success) {
    ElMessage.success(result.message)
    selectedVideo.value = result.video
    showVideoDialog.value = true
  } else {
    ElMessage.error(result.message)
  }
}

// 跳转到视频章节
const jumpToChapter = (chapterIndex) => {
  const result = DemoService.jumpToChapter('main-demo', chapterIndex)
  if (result.success) {
    ElMessage.success(result.message)
    // 这里可以添加实际的视频跳转逻辑
    console.log(`跳转到章节: ${result.chapter.title} (${result.timestamp})`)
  } else {
    ElMessage.error(result.message)
  }
}

// 开始演示场景
const startDemoScenario = (scenarioId) => {
  const result = DemoService.startDemoScenario(scenarioId)
  if (result.success) {
    ElMessage.success(result.message)
    currentScenario.value = result.scenario
    scenarioProgress.value = 0
    // 这里可以添加场景演示的具体逻辑
    console.log(`开始演示场景: ${result.scenario.title}`)
  } else {
    ElMessage.error(result.message)
  }
}

// 获取技术规格标签
const getSpecLabel = (key) => {
  const labels = {
    aiModel: 'AI模型',
    responseTime: '响应时间',
    accuracy: '准确率',
    supportedLanguages: '支持语言',
    concurrentUsers: '并发用户',
    dataPrivacy: '数据安全',
    deployment: '部署方式'
  }
  return labels[key] || key
}

// 获取难度类型样式
const getDifficultyType = (difficulty) => {
  const types = {
    '入门': 'success',
    '初级': 'info',
    '中级': 'warning',
    '高级': 'danger'
  }
  return types[difficulty] || 'info'
}

const nextStep = () => {
  if (currentStep.value < simulatorSteps.length - 1) {
    currentStep.value++
    ElMessage.success(`进入步骤 ${currentStep.value + 1}`)
  }
}

const goToStep = (index) => {
  currentStep.value = index
  ElMessage.info(`跳转到步骤 ${index + 1}`)
}

const resetSimulator = () => {
  currentStep.value = 0
  ElMessage.info('模拟器已重置')
}

const getFeatureIcon = (featureName) => {
  const iconMap = {
    '高性能': 'Lightning',
    '安全可靠': 'Lock',
    '可扩展': 'Expand',
    '智能化': 'Star',
    '实时性': 'VideoPlay',
    '稳定性': 'Check'
  }
  return iconMap[featureName] || 'Setting'
}

const startRealInterview = () => {
  ElMessageBox.confirm(
    '您即将开始真实的面试流程，是否确认？',
    '开始面试',
    {
      confirmButtonText: '确认开始',
      cancelButtonText: '取消',
      type: 'info'
    }
  ).then(() => {
    router.push('/interview-selection')
  })
}

const goBack = () => {
  router.push('/')
}

const startInterview = () => {
  router.push('/interview-selection')
}

const playFeatureVideo = () => {
  ElMessage.success(`正在播放 ${selectedFeature.value?.title} 演示视频`)
  showFeatureDialog.value = false
}

const startVideoPlayback = () => {
  ElMessage.success(`正在播放: ${selectedVideo.value?.title}`)
  showVideoDialog.value = false
}

// FeatureShowcase 组件事件处理
const handleStartDemo = (feature) => {
  ElMessage.success(`开始演示: ${feature.title}`)
  showFeatureDialog.value = false
  // 这里可以启动具体的演示流程
}

const handleTryFeature = (feature) => {
  showFeatureDialog.value = false
  tryFeature(feature)
}

const handleViewDetails = (feature) => {
  ElMessage.info(`查看 ${feature.title} 详细信息`)
  // 这里可以显示更详细的功能说明
}
</script>

<style scoped>
.demo-page {
  min-height: 100vh;
  background: var(--bg-primary);
}

.demo-header {
  background: var(--gradient-primary);
  color: white;
  padding: var(--spacing-xl) 0;
  text-align: center;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.demo-title {
  font-size: 2.5rem;
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
}

.demo-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  margin-bottom: var(--spacing-lg);
}

.demo-stats {
  display: flex;
  justify-content: center;
  gap: var(--spacing-xl);
  margin: var(--spacing-lg) 0;
  padding: var(--spacing-lg);
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-lg);
  backdrop-filter: blur(10px);
}

.stat-item {
  text-align: center;
  color: white;
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.8;
}

.demo-tabs {
  margin-top: var(--spacing-lg);
}

.demo-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-xl);
}

/* 功能演示样式 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

.feature-card {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  text-align: center;
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
  cursor: pointer;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.feature-icon {
  color: var(--color-primary);
  margin-bottom: var(--spacing-lg);
}

.feature-actions {
  margin-top: var(--spacing-lg);
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
}

/* 视频演示样式 */
.video-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-xl);
  margin-top: var(--spacing-lg);
}

.video-player {
  background: white;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.video-placeholder {
  padding: var(--spacing-xxl);
  text-align: center;
  background: var(--bg-secondary);
}

.video-list {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
}

.video-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.video-item:hover {
  background: var(--bg-secondary);
}

.video-info {
  flex: 1;
  margin-left: var(--spacing-md);
}

.video-info h4 {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: 1rem;
  color: var(--text-primary);
}

.video-info p {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.video-tags {
  margin: var(--spacing-sm) 0;
  display: flex;
  gap: var(--spacing-xs);
  flex-wrap: wrap;
}

.video-meta {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
  flex-wrap: wrap;
  margin-top: var(--spacing-sm);
}

.video-duration,
.video-views,
.video-rating {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.video-rating {
  color: var(--color-warning);
}

.video-thumbnail {
  width: 60px;
  height: 40px;
  background: var(--color-primary);
  border-radius: var(--border-radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-right: var(--spacing-md);
}

/* 交互体验样式 */
.demo-simulator {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
  margin-top: var(--spacing-lg);
}

.simulator-steps {
  margin: var(--spacing-xl) 0;
}

.step-card {
  display: flex;
  align-items: center;
  padding: var(--spacing-lg);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-md);
  margin-bottom: var(--spacing-md);
  cursor: pointer;
  transition: all 0.3s ease;
}

.step-card.active {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
}

.step-card.completed {
  border-color: var(--color-success);
  background: var(--color-success-light);
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--color-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: var(--spacing-lg);
}

.step-content {
  flex: 1;
}

.step-content h4 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
}

.step-content p {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-secondary);
}

.step-details {
  margin-top: var(--spacing-sm);
}

.step-meta {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
  flex-wrap: wrap;
}

.step-time {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.interactive-hint {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-sm);
  font-size: 0.9rem;
  color: var(--color-primary);
}

.simulator-actions {
  text-align: center;
  margin-top: var(--spacing-xl);
}

/* 技术架构样式 */
.architecture-overview {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
  margin-top: var(--spacing-lg);
}

.architecture-diagram {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-lg);
  margin: var(--spacing-xl) 0;
}

.arch-layer {
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-md);
  text-align: center;
}

.arch-layer.frontend {
  background: linear-gradient(135deg, #4CAF50, #8BC34A);
  color: white;
}

.arch-layer.backend {
  background: linear-gradient(135deg, #2196F3, #03A9F4);
  color: white;
}

.arch-layer.ai {
  background: linear-gradient(135deg, #FF9800, #FFC107);
  color: white;
}

.arch-layer.data {
  background: linear-gradient(135deg, #9C27B0, #E91E63);
  color: white;
}

.tech-stack {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  justify-content: center;
  margin-top: var(--spacing-md);
}

.tech-item {
  background: rgba(255, 255, 255, 0.2);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: 0.9rem;
}

.architecture-features {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

.feature-highlight {
  text-align: center;
  padding: var(--spacing-lg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
}

.feature-highlight h4 {
  margin: var(--spacing-sm) 0;
  color: var(--text-primary);
}

.feature-highlight p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}

.feature-metrics {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-sm);
}

.metric-item {
  font-size: 0.8rem;
  color: var(--color-primary);
  background: var(--color-primary-light);
  padding: 2px 6px;
  border-radius: var(--border-radius-sm);
}

.arch-description {
  text-align: center;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
  font-size: 1.1rem;
}

.layer-description {
  font-size: 0.9rem;
  margin: var(--spacing-sm) 0;
  opacity: 0.9;
}

.tech-specifications {
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-xl);
  border-top: 1px solid var(--border-color);
}

.tech-specifications h4 {
  text-align: center;
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
}

.spec-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.spec-item {
  background: var(--bg-secondary);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-md);
}

.spec-item strong {
  color: var(--text-primary);
  display: block;
  margin-bottom: var(--spacing-sm);
}

.spec-item ul {
  margin: 0;
  padding-left: var(--spacing-lg);
}

.spec-item li {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.demo-footer {
  text-align: center;
  padding: var(--spacing-xl);
  background: white;
  border-top: 1px solid var(--border-color);
}

/* 对话框样式 */
.feature-demo-content,
.video-player-dialog {
  text-align: center;
  padding: var(--spacing-xl);
}

.demo-video-placeholder,
.video-placeholder-large {
  background: var(--bg-secondary);
  padding: var(--spacing-xxl);
  border-radius: var(--border-radius-lg);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .video-section {
    grid-template-columns: 1fr;
  }
  
  .architecture-diagram {
    grid-template-columns: 1fr;
  }
  
  .architecture-features {
    grid-template-columns: 1fr;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
}

/* 新增的演示功能样式 */

/* 主视频区域 */
.main-video-section {
  margin-bottom: var(--spacing-xl);
}

.video-player-container {
  display: flex;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
}

.video-info-card {
  background: var(--bg-secondary);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  margin: var(--spacing-md) 0;
}

.video-stats {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.tech-features {
  display: flex;
  gap: var(--spacing-xs);
  flex-wrap: wrap;
}

.feature-tag {
  font-weight: 500;
}

.video-actions {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-md);
  justify-content: center;
}

/* 章节导航 */
.chapter-navigation {
  flex: 1;
  background: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  max-height: 600px;
  overflow-y: auto;
}

.chapter-navigation h4 {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

.chapters-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.chapter-item {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: all 0.3s ease;
}

.chapter-item:hover {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
  transform: translateY(-2px);
}

.chapter-thumbnail {
  width: 80px;
  height: 60px;
  background: var(--bg-tertiary);
  border-radius: var(--border-radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.chapter-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.chapter-info {
  flex: 1;
}

.chapter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xs);
}

.chapter-time {
  font-weight: 600;
  color: var(--color-primary);
}

.chapter-duration {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.chapter-info h5 {
  margin: 0 0 var(--spacing-xs) 0;
  color: var(--text-primary);
}

.chapter-info p {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.chapter-keypoints {
  display: flex;
  gap: var(--spacing-xs);
  flex-wrap: wrap;
}

/* 技术规格面板 */
.tech-specs-panel {
  background: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

.tech-specs-panel h4 {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

.specs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.spec-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm);
  background: var(--bg-primary);
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--border-color);
}

.spec-label {
  font-weight: 500;
  color: var(--text-secondary);
}

.spec-value {
  font-weight: 600;
  color: var(--color-primary);
}

/* 演示场景 */
.demo-scenarios-section {
  margin-bottom: var(--spacing-xl);
}

.demo-scenarios-section h3 {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
}

.scenarios-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.scenario-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.scenario-card:hover {
  border-color: var(--color-primary);
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.scenario-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-sm);
}

.scenario-header h4 {
  margin: 0;
  color: var(--text-primary);
}

.scenario-card p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
  line-height: 1.6;
}

.scenario-meta {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  font-size: 0.9rem;
}

.scenario-time,
.scenario-steps {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--text-secondary);
}

.scenario-btn {
  width: 100%;
}

/* 增强的教程网格 */
.tutorials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--spacing-lg);
}

.video-item {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.video-item:hover {
  border-color: var(--color-primary);
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.video-thumbnail {
  position: relative;
  height: 180px;
  background: var(--bg-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-primary);
}

.video-overlay {
  position: absolute;
  bottom: var(--spacing-sm);
  right: var(--spacing-sm);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: var(--border-radius-sm);
  font-size: 0.8rem;
}

.video-info {
  padding: var(--spacing-md);
}

.video-info h4 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
}

.video-info p {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
  line-height: 1.5;
}

.video-meta {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
  margin-bottom: var(--spacing-sm);
  font-size: 0.8rem;
}

.video-views,
.video-rating {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--text-secondary);
}

.video-tags {
  display: flex;
  gap: var(--spacing-xs);
  flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .video-player-container {
    flex-direction: column;
  }

  .video-stats {
    justify-content: center;
  }

  .scenarios-grid,
  .tutorials-grid {
    grid-template-columns: 1fr;
  }

  .specs-grid {
    grid-template-columns: 1fr;
  }

  .video-actions {
    flex-direction: column;
    align-items: center;
  }
}

/* 增强的视频对话框样式 */
.video-dialog .el-dialog__body {
  padding: 0;
}

.video-player-dialog {
  display: flex;
  min-height: 600px;
}

.video-main-area {
  flex: 1;
  padding: var(--spacing-xl);
}

.video-placeholder-large {
  text-align: center;
  padding: var(--spacing-xl);
  background: var(--bg-secondary);
  border-radius: var(--border-radius-md);
}

.video-details {
  margin: var(--spacing-lg) 0;
  text-align: left;
}

.video-stats-row {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  margin-bottom: var(--spacing-md);
  flex-wrap: wrap;
}

.stat-badge {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  background: var(--bg-primary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.video-features,
.video-tech-specs {
  margin: var(--spacing-lg) 0;
  padding: var(--spacing-md);
  background: var(--bg-primary);
  border-radius: var(--border-radius-md);
}

.video-features h4,
.video-tech-specs h4 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--text-primary);
  font-size: 1.1rem;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.feature-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--border-color);
}

.feature-item.highlight {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
}

.feature-item strong {
  color: var(--text-primary);
}

.feature-item span {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.tech-specs-compact {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-sm);
}

.spec-row {
  display: flex;
  justify-content: space-between;
  padding: var(--spacing-xs);
  background: var(--bg-secondary);
  border-radius: var(--border-radius-sm);
}

.spec-key {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.spec-val {
  color: var(--color-primary);
  font-weight: 500;
  font-size: 0.9rem;
}

.video-controls {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  margin-top: var(--spacing-lg);
}

/* 章节侧边栏 */
.video-chapters-sidebar {
  width: 350px;
  background: var(--bg-tertiary);
  padding: var(--spacing-lg);
  border-left: 1px solid var(--border-color);
  overflow-y: auto;
}

.video-chapters-sidebar h4 {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin: 0 0 var(--spacing-md) 0;
  color: var(--text-primary);
}

.chapters-compact-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.chapter-compact-item {
  display: flex;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  transition: all 0.3s ease;
}

.chapter-compact-item:hover {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
}

.chapter-time-badge {
  background: var(--color-primary);
  color: white;
  padding: 2px 6px;
  border-radius: var(--border-radius-sm);
  font-size: 0.8rem;
  font-weight: 500;
  white-space: nowrap;
  align-self: flex-start;
}

.chapter-content {
  flex: 1;
}

.chapter-content h5 {
  margin: 0 0 var(--spacing-xs) 0;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.chapter-content p {
  margin: 0 0 var(--spacing-xs) 0;
  color: var(--text-secondary);
  font-size: 0.8rem;
  line-height: 1.4;
}

.chapter-tags {
  display: flex;
  gap: var(--spacing-xs);
  flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .video-player-dialog {
    flex-direction: column;
  }

  .video-chapters-sidebar {
    width: 100%;
    border-left: none;
    border-top: 1px solid var(--border-color);
  }

  .video-stats-row {
    justify-content: center;
  }

  .tech-specs-compact {
    grid-template-columns: 1fr;
  }

  .video-controls {
    flex-direction: column;
    align-items: center;
  }
}
</style>
