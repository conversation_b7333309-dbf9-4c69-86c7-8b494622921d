<template>
  <div class="home-page">
    <!-- 测试区域 -->
    <div class="test-section">
      <h1>🏠 首页组件加载成功</h1>
      <p>这是简化版的首页，用于测试组件是否正常加载</p>
      <el-button type="primary" size="large" @click="startInterview">
        <el-icon><VideoCamera /></el-icon>
        开始面试测试
      </el-button>
      <el-button size="large" @click="viewDemo">
        <el-icon><VideoPlay /></el-icon>
        观看演示
      </el-button>
    </div>
    
    <!-- 基本信息区域 -->
    <div class="info-section">
      <h2>多模态智能面试评测系统</h2>
      <p>基于讯飞星火大模型的AI面试系统</p>
      
      <div class="feature-grid">
        <div class="feature-card">
          <h3>🎯 多模态分析</h3>
          <p>语音、视频、文本全方位评估</p>
        </div>
        <div class="feature-card">
          <h3>🤖 AI智能评测</h3>
          <p>基于讯飞星火大模型</p>
        </div>
        <div class="feature-card">
          <h3>📊 可视化报告</h3>
          <p>详细的能力分析报告</p>
        </div>
        <div class="feature-card">
          <h3>📚 个性化学习</h3>
          <p>定制化学习路径推荐</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { VideoCamera, VideoPlay } from '@element-plus/icons-vue'

const router = useRouter()

const startInterview = () => {
  console.log('开始面试按钮被点击')
  router.push('/interview-selection')
}

const viewDemo = () => {
  console.log('观看演示按钮被点击')
  alert('演示功能开发中...')
}
</script>

<style scoped>
.home-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  padding: var(--spacing-xl);
  text-align: center;
  background: var(--gradient-primary);
  color: white;
  margin-bottom: var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  animation: fade-in 0.6s ease-out;
}

.test-section h1 {
  font-size: 2rem;
  margin-bottom: 20px;
}

.test-section p {
  font-size: 1.2rem;
  margin-bottom: 30px;
  opacity: 0.9;
}

.info-section {
  text-align: center;
  padding: 40px 20px;
}

.info-section h2 {
  font-size: 2.5rem;
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.info-section > p {
  font-size: 1.3rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xxl);
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-xl);
  margin-top: var(--spacing-xl);
}

.feature-card {
  background: var(--bg-primary);
  padding: var(--spacing-xl) var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-base);
  border: 1px solid var(--border-light);
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
  transform: scaleX(0);
  transition: transform var(--transition-base);
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

.feature-card:hover::before {
  transform: scaleX(1);
}

.feature-card h3 {
  font-size: 1.3rem;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  font-weight: 600;
}

.feature-card p {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .home-page {
    padding: 10px;
  }
  
  .test-section {
    padding: 30px 20px;
  }
  
  .test-section h1 {
    font-size: 1.5rem;
  }
  
  .info-section h2 {
    font-size: 2rem;
  }
  
  .feature-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}
</style>
