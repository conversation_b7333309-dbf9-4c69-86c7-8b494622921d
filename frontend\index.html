<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <!-- <link rel="icon" type="image/svg+xml" href="/vite.svg" /> -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>智能面试评测</title>
  </head>
  <body>
    <div id="app">
      <!-- 初始内容，Vue挂载后会被替换 -->
      <div style="background: orange; color: black; padding: 20px; font-size: 20px; text-align: center;">
        🔄 Vue应用加载中... 如果此消息持续显示，说明Vue未能正确挂载
      </div>
    </div>

    <script>
      console.log('📄 HTML页面加载完成')
      console.log('🎯 #app元素:', document.getElementById('app'))

      // 监听DOM变化
      const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
          console.log('🔄 DOM发生变化:', mutation.type, mutation.target)
        })
      })

      const appElement = document.getElementById('app')
      if (appElement) {
        observer.observe(appElement, { childList: true, subtree: true })
        console.log('👀 开始监听#app元素的变化')
      }
    </script>

    <script type="module" src="/src/main.js"></script>
  </body>
</html> 