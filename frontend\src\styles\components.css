/* 多模态面试评估系统 - 组件样式库 */

/* ==================== 卡片组件 ==================== */
.card {
  background: var(--bg-primary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-base);
  padding: var(--spacing-lg);
  transition: var(--transition-base);
  border: 1px solid var(--border-light);
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card-header {
  padding-bottom: var(--spacing-base);
  border-bottom: 1px solid var(--border-light);
  margin-bottom: var(--spacing-lg);
}

.card-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
}

.card-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin: var(--spacing-xs) 0 0 0;
}

.card-body {
  padding: 0;
}

.card-footer {
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-light);
  margin-top: var(--spacing-lg);
}

/* ==================== 按钮组件增强 ==================== */
.btn-gradient {
  background: var(--gradient-primary);
  border: none;
  color: white;
  font-weight: var(--font-weight-medium);
  transition: var(--transition-base);
  position: relative;
  overflow: hidden;
}

.btn-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: var(--transition-slow);
}

.btn-gradient:hover::before {
  left: 100%;
}

.btn-gradient:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-success-gradient {
  background: var(--gradient-success);
}

.btn-large {
  padding: var(--spacing-base) var(--spacing-xl);
  font-size: var(--font-size-lg);
  border-radius: var(--border-radius-lg);
}

.btn-icon {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
}

/* ==================== 输入框组件增强 ==================== */
.input-group {
  position: relative;
  margin-bottom: var(--spacing-lg);
}

.input-enhanced {
  width: 100%;
  padding: var(--spacing-base);
  border: 2px solid var(--border-base);
  border-radius: var(--border-radius-base);
  font-size: var(--font-size-base);
  transition: var(--transition-base);
  background: var(--bg-primary);
}

.input-enhanced:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1);
  outline: none;
}

.input-label {
  position: absolute;
  top: -8px;
  left: var(--spacing-base);
  background: var(--bg-primary);
  padding: 0 var(--spacing-sm);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

/* ==================== 进度指示器 ==================== */
.progress-container {
  background: var(--bg-tertiary);
  border-radius: var(--border-radius-full);
  height: 8px;
  overflow: hidden;
  position: relative;
}

.progress-bar {
  height: 100%;
  background: var(--gradient-primary);
  border-radius: var(--border-radius-full);
  transition: width var(--transition-base);
  position: relative;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.progress-circle {
  width: 120px;
  height: 120px;
  border-radius: var(--border-radius-full);
  background: conic-gradient(var(--primary-color) 0deg, var(--bg-tertiary) 0deg);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.progress-circle::before {
  content: '';
  width: 80px;
  height: 80px;
  border-radius: var(--border-radius-full);
  background: var(--bg-primary);
  position: absolute;
}

.progress-text {
  position: relative;
  z-index: 1;
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-lg);
  color: var(--primary-color);
}

/* ==================== 标签组件 ==================== */
.tag-modern {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-base);
  border-radius: var(--border-radius-full);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  border: none;
  transition: var(--transition-base);
}

.tag-primary {
  background: rgba(24, 144, 255, 0.1);
  color: var(--primary-color);
}

.tag-success {
  background: rgba(82, 196, 26, 0.1);
  color: var(--success-color);
}

.tag-warning {
  background: rgba(250, 173, 20, 0.1);
  color: var(--warning-color);
}

.tag-error {
  background: rgba(255, 77, 79, 0.1);
  color: var(--error-color);
}

/* ==================== 加载动画 ==================== */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--border-light);
  border-top: 4px solid var(--primary-color);
  border-radius: var(--border-radius-full);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-dots {
  display: flex;
  gap: var(--spacing-xs);
}

.loading-dot {
  width: 8px;
  height: 8px;
  border-radius: var(--border-radius-full);
  background: var(--primary-color);
  animation: bounce 1.4s ease-in-out infinite both;
}

.loading-dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* ==================== 通知组件 ==================== */
.notification {
  padding: var(--spacing-base) var(--spacing-lg);
  border-radius: var(--border-radius-base);
  margin-bottom: var(--spacing-base);
  display: flex;
  align-items: center;
  gap: var(--spacing-base);
  box-shadow: var(--shadow-base);
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.notification-success {
  background: rgba(82, 196, 26, 0.1);
  border-left: 4px solid var(--success-color);
  color: var(--success-dark);
}

.notification-warning {
  background: rgba(250, 173, 20, 0.1);
  border-left: 4px solid var(--warning-color);
  color: var(--warning-dark);
}

.notification-error {
  background: rgba(255, 77, 79, 0.1);
  border-left: 4px solid var(--error-color);
  color: var(--error-dark);
}

.notification-info {
  background: rgba(24, 144, 255, 0.1);
  border-left: 4px solid var(--primary-color);
  color: var(--primary-dark);
}

/* ==================== 模态框增强 ==================== */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: var(--z-modal-backdrop);
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal-content {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--bg-primary);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-2xl);
  z-index: var(--z-modal);
  animation: scaleIn 0.3s ease-out;
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
}

@keyframes scaleIn {
  from {
    transform: translate(-50%, -50%) scale(0.9);
    opacity: 0;
  }
  to {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 768px) {
  .card {
    padding: var(--spacing-base);
    margin: var(--spacing-sm);
  }
  
  .btn-large {
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--font-size-base);
  }
  
  .modal-content {
    margin: var(--spacing-base);
    max-width: calc(100vw - 32px);
  }
}
