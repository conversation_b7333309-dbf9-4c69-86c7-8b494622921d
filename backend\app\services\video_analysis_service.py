"""
视频分析服务模块
实现面部表情识别、眼神交流检测、肢体语言分析等功能
"""
import cv2
import numpy as np
from typing import Dict, Any, List, Tuple
import base64
import io
from PIL import Image
import json
from datetime import datetime

class FaceAnalysisService:
    """面部分析服务"""
    
    def __init__(self):
        # 初始化OpenCV人脸检测器
        self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        self.eye_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_eye.xml')
        
    def detect_faces(self, frame: np.ndarray) -> List[Tuple[int, int, int, int]]:
        """
        检测人脸
        :param frame: 视频帧
        :return: 人脸位置列表 [(x, y, w, h), ...]
        """
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        faces = self.face_cascade.detectMultiScale(gray, 1.1, 4)
        return faces.tolist()
    
    def detect_eyes(self, frame: np.ndarray, face_region: Tuple[int, int, int, int]) -> List[Tuple[int, int, int, int]]:
        """
        检测眼睛
        :param frame: 视频帧
        :param face_region: 人脸区域 (x, y, w, h)
        :return: 眼睛位置列表
        """
        x, y, w, h = face_region
        roi_gray = cv2.cvtColor(frame[y:y+h, x:x+w], cv2.COLOR_BGR2GRAY)
        eyes = self.eye_cascade.detectMultiScale(roi_gray)
        
        # 调整眼睛坐标到原图坐标系
        adjusted_eyes = []
        for (ex, ey, ew, eh) in eyes:
            adjusted_eyes.append((x + ex, y + ey, ew, eh))
        
        return adjusted_eyes
    
    def analyze_facial_expression(self, frame: np.ndarray, face_region: Tuple[int, int, int, int]) -> Dict[str, Any]:
        """
        分析面部表情
        :param frame: 视频帧
        :param face_region: 人脸区域
        :return: 表情分析结果
        """
        # 这里使用简化的表情分析逻辑
        # 在实际应用中，可以集成更专业的表情识别模型
        
        x, y, w, h = face_region
        face_roi = frame[y:y+h, x:x+w]
        
        # 计算面部区域的基本特征
        gray_face = cv2.cvtColor(face_roi, cv2.COLOR_BGR2GRAY)
        
        # 简化的表情分析（基于图像特征）
        brightness = np.mean(gray_face)
        contrast = np.std(gray_face)
        
        # 模拟表情识别结果
        expressions = {
            "neutral": 0.4,
            "happy": 0.3,
            "confident": 0.2,
            "nervous": 0.1
        }
        
        dominant_expression = max(expressions, key=expressions.get)
        
        return {
            "expressions": expressions,
            "dominant_expression": dominant_expression,
            "confidence": expressions[dominant_expression],
            "face_brightness": float(brightness),
            "face_contrast": float(contrast)
        }

class EyeContactAnalysisService:
    """眼神交流分析服务"""
    
    def __init__(self):
        self.face_service = FaceAnalysisService()
        
    def analyze_eye_contact(self, frame: np.ndarray) -> Dict[str, Any]:
        """
        分析眼神交流
        :param frame: 视频帧
        :return: 眼神交流分析结果
        """
        faces = self.face_service.detect_faces(frame)
        
        if not faces:
            return {
                "eye_contact_detected": False,
                "eye_contact_score": 0.0,
                "gaze_direction": "unknown"
            }
        
        # 分析第一个检测到的人脸
        face = faces[0]
        eyes = self.face_service.detect_eyes(frame, face)
        
        if len(eyes) < 2:
            return {
                "eye_contact_detected": False,
                "eye_contact_score": 0.0,
                "gaze_direction": "eyes_not_detected"
            }
        
        # 简化的眼神交流分析
        # 在实际应用中，需要更复杂的算法来判断视线方向
        
        frame_height, frame_width = frame.shape[:2]
        face_x, face_y, face_w, face_h = face
        
        # 计算人脸中心相对于画面中心的位置
        face_center_x = face_x + face_w // 2
        face_center_y = face_y + face_h // 2
        
        frame_center_x = frame_width // 2
        frame_center_y = frame_height // 2
        
        # 计算偏移量
        offset_x = abs(face_center_x - frame_center_x) / frame_width
        offset_y = abs(face_center_y - frame_center_y) / frame_height
        
        # 眼神交流得分（越接近中心得分越高）
        eye_contact_score = max(0, 1 - (offset_x + offset_y))
        
        # 判断视线方向
        gaze_direction = "center"
        if offset_x > 0.2:
            gaze_direction = "left" if face_center_x < frame_center_x else "right"
        elif offset_y > 0.2:
            gaze_direction = "up" if face_center_y < frame_center_y else "down"
        
        return {
            "eye_contact_detected": True,
            "eye_contact_score": float(eye_contact_score),
            "gaze_direction": gaze_direction,
            "face_position": {
                "x": face_center_x,
                "y": face_center_y,
                "offset_x": float(offset_x),
                "offset_y": float(offset_y)
            }
        }

class PostureAnalysisService:
    """姿态分析服务"""
    
    def analyze_posture(self, frame: np.ndarray) -> Dict[str, Any]:
        """
        分析身体姿态
        :param frame: 视频帧
        :return: 姿态分析结果
        """
        # 简化的姿态分析
        # 在实际应用中，可以使用OpenPose或MediaPipe等库
        
        height, width = frame.shape[:2]
        
        # 检测人体轮廓
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # 使用边缘检测来估计姿态
        edges = cv2.Canny(gray, 50, 150)
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            return {
                "posture_score": 0.5,
                "posture_type": "unknown",
                "body_detected": False
            }
        
        # 找到最大的轮廓（假设是人体）
        largest_contour = max(contours, key=cv2.contourArea)
        
        # 计算轮廓的边界框
        x, y, w, h = cv2.boundingRect(largest_contour)
        
        # 分析姿态特征
        aspect_ratio = h / w if w > 0 else 0
        area_ratio = (w * h) / (width * height)
        
        # 简化的姿态评分
        posture_score = 0.7  # 基础分数
        
        # 根据长宽比调整分数（直立姿态得分更高）
        if 1.5 <= aspect_ratio <= 3.0:
            posture_score += 0.2
        
        # 根据占画面比例调整分数
        if 0.1 <= area_ratio <= 0.6:
            posture_score += 0.1
        
        posture_score = min(1.0, posture_score)
        
        # 判断姿态类型
        posture_type = "upright" if aspect_ratio > 1.8 else "leaning"
        
        return {
            "posture_score": float(posture_score),
            "posture_type": posture_type,
            "body_detected": True,
            "aspect_ratio": float(aspect_ratio),
            "area_ratio": float(area_ratio),
            "bounding_box": {"x": int(x), "y": int(y), "w": int(w), "h": int(h)}
        }

class VideoAnalysisService:
    """视频分析服务整合"""
    
    def __init__(self):
        self.face_service = FaceAnalysisService()
        self.eye_contact_service = EyeContactAnalysisService()
        self.posture_service = PostureAnalysisService()
    
    def analyze_frame(self, frame: np.ndarray) -> Dict[str, Any]:
        """
        分析单帧视频
        :param frame: 视频帧
        :return: 分析结果
        """
        try:
            # 人脸检测和表情分析
            faces = self.face_service.detect_faces(frame)
            facial_analysis = {}
            
            if faces:
                facial_analysis = self.face_service.analyze_facial_expression(frame, faces[0])
            
            # 眼神交流分析
            eye_contact_analysis = self.eye_contact_service.analyze_eye_contact(frame)
            
            # 姿态分析
            posture_analysis = self.posture_service.analyze_posture(frame)
            
            return {
                "timestamp": datetime.now().isoformat(),
                "faces_detected": len(faces),
                "facial_analysis": facial_analysis,
                "eye_contact_analysis": eye_contact_analysis,
                "posture_analysis": posture_analysis
            }
            
        except Exception as e:
            return {"error": f"视频分析失败: {str(e)}"}
    
    def analyze_video_sequence(self, frames: List[np.ndarray]) -> Dict[str, Any]:
        """
        分析视频序列
        :param frames: 视频帧列表
        :return: 序列分析结果
        """
        frame_analyses = []
        
        for i, frame in enumerate(frames):
            analysis = self.analyze_frame(frame)
            analysis["frame_index"] = i
            frame_analyses.append(analysis)
        
        # 计算整体统计
        eye_contact_scores = [
            analysis.get("eye_contact_analysis", {}).get("eye_contact_score", 0)
            for analysis in frame_analyses
        ]
        
        posture_scores = [
            analysis.get("posture_analysis", {}).get("posture_score", 0)
            for analysis in frame_analyses
        ]
        
        return {
            "total_frames": len(frames),
            "frame_analyses": frame_analyses,
            "summary": {
                "average_eye_contact_score": float(np.mean(eye_contact_scores)) if eye_contact_scores else 0,
                "average_posture_score": float(np.mean(posture_scores)) if posture_scores else 0,
                "eye_contact_consistency": float(1 - np.std(eye_contact_scores)) if eye_contact_scores else 0,
                "posture_consistency": float(1 - np.std(posture_scores)) if posture_scores else 0
            }
        }

# 全局服务实例
video_analysis_service = VideoAnalysisService()
