/**
 * 演示服务 - 管理系统演示相关的数据和功能
 */

// 模拟数据生成器
export const generateMockData = {
  // 生成模拟语音分析数据
  speechAnalysis: () => ({
    confidence: Math.random() * 0.3 + 0.7, // 0.7-1.0
    fluency: Math.random() * 0.2 + 0.8,    // 0.8-1.0
    clarity: Math.random() * 0.25 + 0.75,  // 0.75-1.0
    pace: Math.random() * 0.3 + 0.7,       // 0.7-1.0
    volume: Math.random() * 0.2 + 0.8,     // 0.8-1.0
    keywords: ['机器学习', '深度学习', '神经网络', '算法优化', '数据处理'],
    emotions: {
      confidence: Math.random() * 0.4 + 0.6,
      nervousness: Math.random() * 0.3,
      enthusiasm: Math.random() * 0.3 + 0.7
    }
  }),

  // 生成模拟视频分析数据
  videoAnalysis: () => ({
    eyeContact: Math.random() * 0.3 + 0.7,
    posture: Math.random() * 0.2 + 0.8,
    gestures: Math.random() * 0.25 + 0.75,
    facialExpression: Math.random() * 0.3 + 0.7,
    attention: Math.random() * 0.2 + 0.8,
    movements: {
      headNods: Math.floor(Math.random() * 15) + 5,
      handGestures: Math.floor(Math.random() * 20) + 10,
      bodyShifts: Math.floor(Math.random() * 8) + 2
    },
    emotions: {
      engagement: Math.random() * 0.3 + 0.7,
      stress: Math.random() * 0.4,
      focus: Math.random() * 0.2 + 0.8
    }
  }),

  // 生成模拟文本分析数据
  textAnalysis: () => ({
    technicalAccuracy: Math.random() * 0.3 + 0.7,
    logicalStructure: Math.random() * 0.25 + 0.75,
    vocabularyRichness: Math.random() * 0.2 + 0.8,
    responseCompleteness: Math.random() * 0.3 + 0.7,
    keywordMatching: Math.random() * 0.35 + 0.65,
    sentimentAnalysis: {
      positive: Math.random() * 0.4 + 0.6,
      neutral: Math.random() * 0.3 + 0.2,
      negative: Math.random() * 0.2
    },
    complexityScore: Math.random() * 0.3 + 0.7
  })
};

// 演示视频数据
export const demoVideos = {
  main: {
    id: 'main-demo',
    title: '多模态智能面试评测系统完整演示',
    description: '展示系统的完整功能流程，从面试选择到报告生成',
    duration: '12:30',
    thumbnail: '/demo/thumbnails/main-demo.jpg',
    url: '/demo/videos/main-demo.mp4',
    category: '系统概览',
    difficulty: '入门',
    views: 2847,
    rating: 4.9,
    tags: ['系统演示', '完整流程', '功能概览', 'iFlytek AI', '多模态交互'],
    publishDate: '2024-06-15',
    author: 'iFlytek技术团队',
    language: '中文',
    quality: '1080p',
    subtitles: true,
    downloadable: false,
    chapters: [
      {
        time: '0:00',
        title: '系统介绍与架构概览',
        description: '了解系统整体架构、核心技术栈和iFlytek AI能力',
        duration: '1:30',
        keyPoints: ['系统架构', 'iFlytek星火大模型', '多模态技术', '技术优势'],
        thumbnail: '/demo/chapters/intro.jpg'
      },
      {
        time: '1:30',
        title: '智能面试选择与配置',
        description: '选择技术领域、岗位类型和面试难度配置',
        duration: '1:45',
        keyPoints: ['领域选择', '岗位匹配', '难度设置', '个性化配置'],
        thumbnail: '/demo/chapters/selection.jpg'
      },
      {
        time: '3:15',
        title: '多模态交互体验',
        description: '体验语音、视频、文本三种交互方式的无缝切换',
        duration: '2:05',
        keyPoints: ['语音识别', '视频分析', '文本理解', '实时反馈'],
        thumbnail: '/demo/chapters/interaction.jpg'
      },
      {
        time: '5:20',
        title: 'iFlytek AI实时评测',
        description: '观看讯飞星火大模型进行实时智能评测的完整过程',
        duration: '2:30',
        keyPoints: ['实时分析', '多维评测', '智能打分', '专业建议'],
        thumbnail: '/demo/chapters/evaluation.jpg'
      },
      {
        time: '7:50',
        title: '智能报告生成与分析',
        description: '查看详细的评测报告、数据可视化和改进建议',
        duration: '2:20',
        keyPoints: ['报告生成', '数据可视化', '能力分析', '学习路径'],
        thumbnail: '/demo/chapters/report.jpg'
      },
      {
        time: '10:10',
        title: '个性化学习路径推荐',
        description: '基于评测结果生成个性化的技能提升方案',
        duration: '1:30',
        keyPoints: ['学习规划', '技能提升', '资源推荐', '进度跟踪'],
        thumbnail: '/demo/chapters/learning.jpg'
      },
      {
        time: '11:40',
        title: '系统管理与数据洞察',
        description: '展示系统管理功能和数据分析能力',
        duration: '0:50',
        keyPoints: ['数据管理', '统计分析', '性能监控', '用户洞察'],
        thumbnail: '/demo/chapters/management.jpg'
      }
    ],
    features: [
      {
        name: 'iFlytek星火大模型',
        description: '基于讯飞星火认知大模型的智能评测',
        icon: 'brain',
        highlight: true
      },
      {
        name: '多模态交互',
        description: '支持语音、视频、文本三种交互方式',
        icon: 'interaction',
        highlight: true
      },
      {
        name: '实时评测',
        description: '毫秒级响应的实时智能评测',
        icon: 'speed',
        highlight: false
      },
      {
        name: '个性化推荐',
        description: '基于AI的个性化学习路径推荐',
        icon: 'personalization',
        highlight: false
      }
    ],
    technicalSpecs: {
      aiModel: 'iFlytek星火认知大模型V3.5',
      responseTime: '< 500ms',
      accuracy: '95.8%',
      supportedLanguages: ['中文', '英文'],
      concurrentUsers: '1000+',
      dataPrivacy: '企业级安全保护',
      deployment: '云端部署 + 本地化支持'
    }
  },
  tutorials: [
    {
      id: 'getting-started',
      title: '快速开始指南',
      description: '5分钟了解如何使用系统进行面试',
      duration: '4:32',
      thumbnail: '/demo/thumbnails/getting-started.jpg',
      url: '/demo/videos/getting-started.mp4',
      category: '基础教程',
      difficulty: '入门',
      views: 892,
      rating: 4.7,
      tags: ['新手指南', '基础操作', '快速上手']
    },
    {
      id: 'multimodal-interview',
      title: '多模态面试详解',
      description: '深入了解文本、语音、视频三种面试模式',
      duration: '6:18',
      thumbnail: '/demo/thumbnails/multimodal.jpg',
      url: '/demo/videos/multimodal.mp4',
      category: '功能详解',
      difficulty: '中级',
      views: 654,
      rating: 4.9,
      tags: ['多模态', '语音识别', '视频分析', '文本处理']
    },
    {
      id: 'ai-evaluation',
      title: 'AI评测机制解析',
      description: '了解讯飞星火大模型如何进行智能评测',
      duration: '5:45',
      thumbnail: '/demo/thumbnails/ai-evaluation.jpg',
      url: '/demo/videos/ai-evaluation.mp4',
      category: '技术原理',
      difficulty: '高级',
      views: 423,
      rating: 4.8,
      tags: ['AI评测', '星火大模型', '算法原理', '评分机制']
    },
    {
      id: 'speech-analysis-deep',
      title: '语音分析技术深度解析',
      description: '详细介绍语音识别、情感分析、流畅度评测等技术',
      duration: '7:22',
      thumbnail: '/demo/thumbnails/speech-analysis.jpg',
      url: '/demo/videos/speech-analysis.mp4',
      category: '技术原理',
      difficulty: '高级',
      views: 356,
      rating: 4.6,
      tags: ['语音识别', '情感分析', '流畅度', '音频处理']
    },
    {
      id: 'video-analysis-tech',
      title: '视频分析技术详解',
      description: '人脸识别、姿态检测、表情分析等视频AI技术',
      duration: '6:45',
      thumbnail: '/demo/thumbnails/video-analysis.jpg',
      url: '/demo/videos/video-analysis.mp4',
      category: '技术原理',
      difficulty: '高级',
      views: 298,
      rating: 4.7,
      tags: ['人脸识别', '姿态检测', '表情分析', '计算机视觉']
    },
    {
      id: 'report-generation',
      title: '智能报告生成系统',
      description: '了解如何生成个性化的面试评测报告',
      duration: '4:18',
      thumbnail: '/demo/thumbnails/report-gen.jpg',
      url: '/demo/videos/report-gen.mp4',
      category: '功能详解',
      difficulty: '中级',
      views: 567,
      rating: 4.5,
      tags: ['报告生成', '数据可视化', '个性化建议']
    },
    {
      id: 'report-analysis',
      title: '评估报告解读',
      description: '如何理解和使用详细的评估报告',
      duration: '4:12',
      thumbnail: '/demo/thumbnails/report.jpg',
      url: '/demo/videos/report.mp4',
      category: '结果分析',
      difficulty: '入门',
      views: 734,
      rating: 4.4,
      tags: ['报告解读', '数据分析', '结果理解']
    },
    {
      id: 'learning-path',
      title: '个性化学习路径',
      description: '基于评估结果获得定制化学习建议',
      duration: '3:56',
      thumbnail: '/demo/thumbnails/learning.jpg',
      url: '/demo/videos/learning.mp4',
      category: '学习提升',
      difficulty: '中级',
      views: 445,
      rating: 4.6,
      tags: ['学习路径', '个性化', '技能提升', '职业发展']
    },
    {
      id: 'advanced-features',
      title: '高级功能特性',
      description: '探索系统的高级功能和定制选项',
      duration: '8:15',
      thumbnail: '/demo/thumbnails/advanced.jpg',
      url: '/demo/videos/advanced.mp4',
      category: '高级功能',
      difficulty: '高级',
      views: 267,
      rating: 4.9,
      tags: ['高级功能', '定制化', '企业版', 'API接口']
    }
  ]
}

// 演示场景数据
export const demoScenarios = {
  systemDemo: {
    id: 'system-demo',
    title: '系统完整演示',
    description: '体验从面试开始到报告生成的完整流程',
    estimatedTime: '15分钟',
    difficulty: '入门',
    steps: [
      {
        id: 'step-1',
        title: '系统登录与初始化',
        description: '登录系统并完成初始设置',
        duration: '1分钟',
        actions: ['打开系统', '用户登录', '选择角色'],
        expectedResult: '成功进入系统主界面',
        tips: '首次使用建议选择"演示模式"'
      },
      {
        id: 'step-2',
        title: '面试配置与选择',
        description: '选择技术领域、岗位类型和面试难度',
        duration: '2分钟',
        actions: ['选择技术领域', '设置岗位类型', '配置面试参数'],
        expectedResult: '完成面试配置，进入面试准备页面',
        tips: '建议先从"人工智能-初级"开始体验'
      },
      {
        id: 'step-3',
        title: '多模态交互体验',
        description: '体验语音、视频、文本三种交互方式',
        duration: '5分钟',
        actions: ['语音回答问题', '视频表情分析', '文本补充说明'],
        expectedResult: '系统实时分析并给出反馈',
        tips: '保持自然状态，系统会自动分析您的表现'
      },
      {
        id: 'step-4',
        title: 'AI实时评测观察',
        description: '观察iFlytek AI进行实时评测的过程',
        duration: '3分钟',
        actions: ['观察评测指标', '查看实时分数', '了解评测维度'],
        expectedResult: '实时显示各项评测指标和分数',
        tips: '注意观察右侧的实时评测面板'
      },
      {
        id: 'step-5',
        title: '报告生成与分析',
        description: '查看详细的评测报告和数据分析',
        duration: '3分钟',
        actions: ['生成评测报告', '查看数据可视化', '分析能力雷达图'],
        expectedResult: '获得完整的评测报告和改进建议',
        tips: '重点关注薄弱环节的改进建议'
      },
      {
        id: 'step-6',
        title: '学习路径推荐',
        description: '获取个性化的学习路径和资源推荐',
        duration: '1分钟',
        actions: ['查看学习路径', '选择学习资源', '制定学习计划'],
        expectedResult: '获得个性化的学习提升方案',
        tips: '可以收藏感兴趣的学习资源'
      }
    ],
    prerequisites: [
      '准备好摄像头和麦克风',
      '确保网络连接稳定',
      '选择安静的环境进行演示'
    ],
    learningObjectives: [
      '了解系统的完整功能流程',
      '掌握多模态交互的使用方法',
      '理解AI评测的工作原理',
      '学会解读评测报告和改进建议'
    ]
  }
};

// 功能演示数据
export const featuresDemos = [
  {
    id: 'multimodal-input',
    title: '多模态输入系统',
    icon: 'VideoCamera',
    description: '支持文本、语音、视频三种输入方式，提供全方位的面试体验',
    category: '核心功能',
    difficulty: '入门',
    estimatedTime: '5分钟',
    highlights: [
      '实时语音识别与转写',
      '高清视频录制与分析',
      '智能文本语义理解',
      '多模态数据融合评估',
      '自适应质量优化',
      '跨平台兼容支持'
    ],
    technicalSpecs: {
      audioFormat: 'WAV/MP3, 16kHz采样率',
      videoFormat: '1080p H.264编码',
      textEncoding: 'UTF-8多语言支持',
      latency: '< 100ms实时处理'
    },
    demoSteps: [
      {
        step: 1,
        title: '选择输入模式',
        description: '根据题目类型和个人偏好选择最适合的回答方式',
        screenshot: '/demo/screenshots/input-selection.jpg',
        duration: '30秒',
        interactiveElements: ['模式切换按钮', '预览窗口', '设备检测'],
        tips: '建议根据问题类型选择：技术问题用文本，表达能力用语音，综合面试用视频'
      },
      {
        step: 2,
        title: '设备检测与配置',
        description: '自动检测并配置麦克风、摄像头等设备',
        screenshot: '/demo/screenshots/device-setup.jpg',
        duration: '45秒',
        interactiveElements: ['设备列表', '权限授权', '质量测试'],
        tips: '确保设备正常工作，环境光线充足，背景相对安静'
      },
      {
        step: 3,
        title: '开始录制',
        description: '点击录制按钮开始语音或视频回答',
        screenshot: '/demo/screenshots/recording.jpg',
        duration: '2-5分钟',
        interactiveElements: ['录制控制', '时间显示', '质量指示器'],
        tips: '保持自然状态，清晰表达，注意时间控制'
      },
      {
        step: 4,
        title: '实时反馈',
        description: '系统提供实时的录制状态和质量反馈',
        screenshot: '/demo/screenshots/feedback.jpg',
        duration: '持续',
        interactiveElements: ['音量指示', '清晰度检测', '表情识别'],
        tips: '根据实时反馈调整音量、距离和表情'
      },
      {
        step: 5,
        title: '预览与确认',
        description: '录制完成后预览内容并确认提交',
        screenshot: '/demo/screenshots/preview.jpg',
        duration: '1分钟',
        interactiveElements: ['播放控制', '重录选项', '提交按钮'],
        tips: '仔细检查录制质量，不满意可以重新录制'
      }
    ],
    commonIssues: [
      { issue: '麦克风无声音', solution: '检查设备权限和音量设置' },
      { issue: '视频画面模糊', solution: '调整摄像头焦距和环境光线' },
      { issue: '录制卡顿', solution: '关闭其他应用程序释放系统资源' }
    ]
  },
  {
    id: 'ai-interaction',
    title: 'AI智能对话系统',
    icon: 'ChatDotRound',
    description: '基于讯飞星火大模型的智能面试官，提供自然流畅的对话体验',
    category: '核心功能',
    difficulty: '中级',
    estimatedTime: '8分钟',
    highlights: [
      '基于星火大模型的智能问题生成',
      '深度上下文理解与记忆',
      '智能追问与深度挖掘',
      '个性化难度动态调整',
      '多轮对话连贯性保持',
      '专业领域知识库支持'
    ],
    technicalSpecs: {
      model: '讯飞星火认知大模型V3.5',
      responseTime: '< 2秒平均响应',
      contextLength: '支持8K上下文长度',
      accuracy: '> 95%领域问题准确率'
    },
    demoSteps: [
      {
        step: 1,
        title: '智能问题生成',
        description: 'AI根据岗位要求、技能需求和候选人背景生成针对性问题',
        screenshot: '/demo/screenshots/question-gen.jpg',
        duration: '30秒',
        interactiveElements: ['问题类型选择', '难度调节', '领域筛选'],
        tips: '系统会根据简历信息和岗位匹配度生成最相关的问题'
      },
      {
        step: 2,
        title: '自然对话交互',
        description: '与AI面试官进行自然的问答对话',
        screenshot: '/demo/screenshots/conversation.jpg',
        duration: '3-5分钟',
        interactiveElements: ['语音输入', '文本输入', '表情识别'],
        tips: '像与真人面试官对话一样自然表达，AI会理解语境和情感'
      },
      {
        step: 3,
        title: '智能追问机制',
        description: '基于回答内容进行深入追问和细节探索',
        screenshot: '/demo/screenshots/follow-up.jpg',
        duration: '2-3分钟',
        interactiveElements: ['追问提示', '深度分析', '关键词高亮'],
        tips: 'AI会根据你的回答深入挖掘，展示真实的技术深度'
      },
      {
        step: 4,
        title: '动态难度调节',
        description: '根据表现实时调整问题难度和深度',
        screenshot: '/demo/screenshots/difficulty.jpg',
        duration: '持续',
        interactiveElements: ['难度指示器', '表现评估', '调节建议'],
        tips: '表现好会增加难度，遇到困难会适当降低，确保最佳面试体验'
      },
      {
        step: 5,
        title: '上下文记忆',
        description: '维持整个面试过程的上下文连贯性',
        screenshot: '/demo/screenshots/context.jpg',
        duration: '全程',
        interactiveElements: ['对话历史', '关键信息', '关联分析'],
        tips: 'AI会记住之前的对话内容，避免重复提问，保持逻辑连贯'
      }
    ],
    aiCapabilities: [
      { capability: '自然语言理解', description: '准确理解候选人的回答意图和内容' },
      { capability: '知识图谱推理', description: '基于专业知识库进行深度推理' },
      { capability: '情感智能识别', description: '识别候选人的情绪状态和压力水平' },
      { capability: '个性化适应', description: '根据个人特点调整交互方式' }
    ]
  },
  {
    id: 'real-time-analysis',
    title: '实时多模态分析引擎',
    icon: 'TrendCharts',
    description: '面试过程中的实时多维度能力分析与智能评估',
    category: '核心功能',
    difficulty: '高级',
    estimatedTime: '10分钟',
    highlights: [
      '实时语音情感与流畅度分析',
      '视频行为与表情识别',
      '文本语义质量评估',
      '多模态数据融合评分',
      '动态趋势预测分析',
      '异常行为智能检测'
    ],
    technicalSpecs: {
      processingLatency: '< 50ms实时处理',
      analysisAccuracy: '> 92%综合准确率',
      dataPoints: '每秒采集100+数据点',
      algorithms: 'CNN+RNN+Transformer混合架构'
    },
    demoSteps: [
      {
        step: 1,
        title: '多维度实时监测',
        description: '同时分析语音、视频、文本多个维度的实时数据',
        screenshot: '/demo/screenshots/monitoring.jpg',
        duration: '持续监测',
        interactiveElements: ['实时波形图', '情感指标', '行为热图'],
        tips: '系统会实时显示各项指标变化，帮助了解当前表现状态',
        metrics: ['语音清晰度', '情感稳定性', '眼神接触', '姿态自然度', '回答完整性']
      },
      {
        step: 2,
        title: '智能实时评分',
        description: '每个回答都会获得基于AI算法的即时评分反馈',
        screenshot: '/demo/screenshots/scoring.jpg',
        duration: '回答后2秒内',
        interactiveElements: ['评分动画', '维度分解', '对比基准'],
        tips: '评分会考虑技术准确性、表达能力、逻辑性等多个维度',
        scoringCriteria: ['技术深度', '表达清晰度', '逻辑结构', '创新思维', '问题解决能力']
      },
      {
        step: 3,
        title: '动态趋势分析',
        description: '展示面试过程中各项能力的表现趋势变化',
        screenshot: '/demo/screenshots/trends.jpg',
        duration: '全程跟踪',
        interactiveElements: ['趋势图表', '峰值标记', '改善建议'],
        tips: '观察趋势变化可以了解面试状态的改善或下降',
        trendIndicators: ['紧张度变化', '自信心提升', '表达流畅度', '技术深度递进']
      },
      {
        step: 4,
        title: '异常检测与提醒',
        description: '智能检测异常行为并提供友好提醒',
        screenshot: '/demo/screenshots/anomaly.jpg',
        duration: '智能触发',
        interactiveElements: ['异常警告', '改善提示', '状态恢复'],
        tips: '系统会检测如过度紧张、注意力分散等情况并给出建议',
        detectionTypes: ['音频异常', '视频异常', '行为异常', '环境干扰']
      },
      {
        step: 5,
        title: '综合能力画像',
        description: '实时构建候选人的多维能力画像',
        screenshot: '/demo/screenshots/profile.jpg',
        duration: '动态更新',
        interactiveElements: ['能力雷达图', '强弱项分析', '匹配度评估'],
        tips: '能力画像会随着面试进行不断完善和精确',
        profileDimensions: ['技术能力', '沟通能力', '逻辑思维', '学习能力', '抗压能力', '创新能力']
      }
    ],
    analysisModules: [
      { module: '语音分析引擎', features: ['情感识别', '流畅度检测', '语速分析', '停顿模式'] },
      { module: '视频分析引擎', features: ['人脸识别', '表情分析', '姿态检测', '注意力追踪'] },
      { module: '文本分析引擎', features: ['语义理解', '逻辑分析', '关键词提取', '完整性评估'] },
      { module: '融合分析引擎', features: ['多模态融合', '权重分配', '综合评分', '趋势预测'] }
    ]
  },
  {
    id: 'comprehensive-report',
    title: '智能评估报告系统',
    icon: 'Document',
    description: '基于AI分析的详细多维度评估报告与个性化改进方案',
    category: '结果分析',
    difficulty: '中级',
    estimatedTime: '6分钟',
    highlights: [
      '六大核心能力维度深度分析',
      '交互式可视化图表展示',
      'AI生成的个性化改进建议',
      '定制化学习路径推荐',
      '行业对标与竞争力分析',
      '历史进步追踪与预测'
    ],
    technicalSpecs: {
      reportGeneration: '< 30秒生成完整报告',
      dataVisualization: '10+种图表类型',
      recommendationEngine: 'AI驱动的个性化推荐',
      exportFormats: 'PDF/Word/Excel多格式导出'
    },
    demoSteps: [
      {
        step: 1,
        title: '多维能力雷达图',
        description: '直观展示技术能力、沟通能力等六大维度的评估结果',
        screenshot: '/demo/screenshots/radar.jpg',
        duration: '1分钟',
        interactiveElements: ['雷达图交互', '维度切换', '分数详情'],
        tips: '雷达图可以快速识别强项和弱项，点击各维度查看详细分析',
        capabilities: ['技术深度', '逻辑思维', '沟通表达', '学习能力', '抗压能力', '创新思维']
      },
      {
        step: 2,
        title: '详细维度分析',
        description: '每个维度的具体分析、评价和证据支撑',
        screenshot: '/demo/screenshots/analysis.jpg',
        duration: '2分钟',
        interactiveElements: ['维度展开', '证据查看', '对比分析'],
        tips: '每个维度都有具体的评分依据和改进空间分析',
        analysisComponents: ['得分解释', '表现亮点', '改进空间', '行业对比']
      },
      {
        step: 3,
        title: '智能改进建议',
        description: '基于AI分析的针对性能力提升建议和学习资源',
        screenshot: '/demo/screenshots/suggestions.jpg',
        duration: '2分钟',
        interactiveElements: ['建议分类', '资源链接', '优先级排序'],
        tips: 'AI会根据你的具体情况生成个性化的改进建议',
        suggestionTypes: ['技能提升', '知识补充', '实践项目', '学习资源']
      },
      {
        step: 4,
        title: '个性化学习路径',
        description: '定制化的学习计划和职业发展建议',
        screenshot: '/demo/screenshots/learning-path.jpg',
        duration: '1分钟',
        interactiveElements: ['路径规划', '时间安排', '里程碑设置'],
        tips: '学习路径会根据你的目标岗位和当前水平制定',
        pathComponents: ['短期目标', '中期规划', '长期发展', '资源推荐']
      },
      {
        step: 5,
        title: '行业竞争力分析',
        description: '与同行业候选人的对比分析和市场定位',
        screenshot: '/demo/screenshots/benchmark.jpg',
        duration: '30秒',
        interactiveElements: ['排名显示', '分布图表', '趋势分析'],
        tips: '了解自己在行业中的相对位置，明确努力方向',
        benchmarkMetrics: ['技能排名', '薪资预期', '岗位匹配度', '发展潜力']
      }
    ],
    reportSections: [
      { section: '执行摘要', content: '面试整体表现概述和核心结论' },
      { section: '能力评估', content: '六大维度的详细评分和分析' },
      { section: '行为分析', content: '面试过程中的行为模式分析' },
      { section: '改进建议', content: 'AI生成的个性化提升方案' },
      { section: '学习路径', content: '定制化的学习计划和资源' },
      { section: '附录资料', content: '相关学习资源和参考材料' }
    ]
  }
]

// 交互式演示步骤
export const interactiveSteps = [
  {
    id: 'domain-selection',
    title: '智能领域匹配',
    description: '基于AI推荐选择最适合的面试领域和岗位类型',
    component: 'DomainSelector',
    estimatedTime: '2分钟',
    difficulty: '入门',
    mockData: {
      domains: [
        {
          id: 'ai',
          name: '人工智能',
          description: '机器学习、深度学习、计算机视觉等',
          popularity: 95,
          avgSalary: '25-50K',
          positions: ['算法工程师', 'AI研究员', '机器学习工程师']
        },
        {
          id: 'bigdata',
          name: '大数据',
          description: '数据分析、数据挖掘、分布式计算等',
          popularity: 88,
          avgSalary: '20-40K',
          positions: ['数据工程师', '数据分析师', '大数据架构师']
        },
        {
          id: 'iot',
          name: '物联网',
          description: '嵌入式开发、传感器网络、边缘计算等',
          popularity: 76,
          avgSalary: '18-35K',
          positions: ['IoT工程师', '嵌入式开发', '硬件工程师']
        }
      ],
      positionTypes: ['技术岗', '运维测试岗', '产品岗', '管理岗']
    },
    interactiveElements: [
      '领域卡片选择',
      '岗位类型筛选',
      '难度级别调节',
      'AI推荐系统'
    ],
    tips: [
      '选择您最熟悉和感兴趣的技术领域',
      '不同领域的问题侧重点和评估标准不同',
      '可以多次尝试不同领域的面试体验',
      'AI会根据您的选择推荐最适合的问题类型'
    ]
  },
  {
    id: 'interview-start',
    title: '智能面试对话',
    description: '与基于讯飞星火大模型的AI面试官进行专业对话',
    component: 'InterviewChat',
    estimatedTime: '5-8分钟',
    difficulty: '中级',
    mockData: {
      questions: [
        {
          id: 1,
          text: '请简述您对机器学习中过拟合问题的理解，以及常用的解决方法。',
          type: '技术基础',
          difficulty: '中级',
          expectedTime: '2-3分钟'
        },
        {
          id: 2,
          text: '在您的项目经验中，如何处理数据不平衡的问题？',
          type: '实践应用',
          difficulty: '中级',
          expectedTime: '3-4分钟'
        }
      ],
      currentQuestion: 0,
      suggestions: [
        '从理论定义开始解释',
        '举出具体的实际例子',
        '详细说明解决方案',
        '分享个人实践经验',
        '讨论方法的优缺点'
      ],
      aiPersonality: {
        name: 'AI面试官小星',
        style: '专业友好',
        expertise: ['技术评估', '能力分析', '职业指导']
      }
    },
    interactiveElements: [
      '实时对话界面',
      '问题难度调节',
      '回答提示系统',
      '时间管理工具'
    ],
    tips: [
      '回答要逻辑清晰、层次分明',
      '可以结合具体项目经验和案例',
      '遇到不确定的问题可以诚实表达',
      'AI会根据你的回答进行智能追问'
    ]
  },
  {
    id: 'multimodal-response',
    title: '多模态智能交互',
    description: '体验文本、语音、视频三种交互方式的智能面试',
    component: 'MultimodalInput',
    estimatedTime: '6-10分钟',
    difficulty: '中级',
    mockData: {
      modes: [
        {
          type: '文本',
          icon: 'Edit',
          description: '键盘输入，适合复杂技术问题',
          advantages: ['思考时间充足', '表达精确', '易于修改'],
          bestFor: ['算法题', '技术方案', '代码解释']
        },
        {
          type: '语音',
          icon: 'Microphone',
          description: '语音回答，展示口语表达能力',
          advantages: ['自然流畅', '情感丰富', '实时互动'],
          bestFor: ['经验分享', '问题讨论', '思路阐述']
        },
        {
          type: '视频',
          icon: 'VideoCamera',
          description: '视频录制，全方位展示个人能力',
          advantages: ['完整展示', '非语言交流', '真实感强'],
          bestFor: ['自我介绍', '项目演示', '综合面试']
        }
      ],
      currentMode: '语音',
      recordingTime: 0,
      qualityMetrics: {
        audio: { volume: 85, clarity: 92, noise: 15 },
        video: { resolution: '1080p', lighting: 88, stability: 95 },
        text: { speed: 45, accuracy: 98 }
      }
    },
    interactiveElements: [
      '模式切换界面',
      '实时质量监测',
      '录制控制面板',
      '预览与重录'
    ],
    tips: [
      '语音回答更能展现自然的表达能力',
      '视频模式可以展示完整的沟通技巧',
      '文本模式适合需要精确表达的技术问题',
      '选择您最舒适和自信的交互方式',
      '可以在不同问题中尝试不同模式'
    ]
  },
  {
    id: 'view-results',
    title: '智能评估结果分析',
    description: '获得基于AI深度分析的详细面试评估报告和多维能力画像',
    component: 'ResultsView',
    estimatedTime: '4-6分钟',
    difficulty: '入门',
    mockData: {
      scores: {
        technical: { score: 85, rank: '优秀', improvement: '+12%' },
        communication: { score: 78, rank: '良好', improvement: '+8%' },
        logic: { score: 82, rank: '优秀', improvement: '+15%' },
        innovation: { score: 75, rank: '良好', improvement: '+5%' },
        teamwork: { score: 80, rank: '良好', improvement: '+10%' },
        learning: { score: 88, rank: '优秀', improvement: '+18%' }
      },
      overall: { score: 81, rank: '优秀', percentile: 78 },
      benchmarks: {
        industry: 75,
        position: 79,
        experience: 82
      },
      strengths: ['技术深度扎实', '学习能力强', '逻辑思维清晰'],
      improvements: ['沟通表达', '创新思维', '团队协作'],
      interviewMetrics: {
        duration: '28分钟',
        questionsAnswered: 12,
        averageResponseTime: '2.3分钟',
        confidenceLevel: 82
      }
    },
    interactiveElements: [
      '能力雷达图',
      '分数详情展开',
      '行业对比图表',
      '历史进步追踪'
    ],
    tips: [
      '重点关注各维度的平衡发展',
      '对比行业基准了解自身定位',
      '认真阅读详细的改进建议',
      '制定基于数据的学习计划'
    ]
  },
  {
    id: 'learning-path',
    title: 'AI个性化学习路径',
    description: '基于评估结果和AI算法生成的定制化学习建议和职业发展规划',
    component: 'LearningRecommendation',
    estimatedTime: '3-5分钟',
    difficulty: '中级',
    mockData: {
      personalizedPlan: {
        currentLevel: '中级工程师',
        targetLevel: '高级工程师',
        estimatedTime: '6-8个月',
        successRate: '85%'
      },
      recommendations: [
        {
          id: 1,
          title: '深度学习基础强化',
          priority: 'high',
          urgency: '立即开始',
          duration: '4周',
          difficulty: '中级',
          modules: [
            '神经网络数学基础',
            '反向传播算法详解',
            '优化方法与技巧',
            '正则化技术'
          ],
          resources: [
            { type: '在线课程', name: '深度学习专项课程', provider: 'Coursera' },
            { type: '实践项目', name: '图像分类项目', difficulty: '中级' },
            { type: '技术书籍', name: '深度学习', author: 'Ian Goodfellow' }
          ],
          expectedOutcome: '掌握深度学习核心概念，能够独立设计神经网络'
        },
        {
          id: 2,
          title: '沟通表达能力提升',
          priority: 'medium',
          urgency: '2周内开始',
          duration: '3周',
          difficulty: '入门',
          modules: [
            '技术演讲技巧',
            '文档写作规范',
            '团队协作沟通',
            '跨部门交流'
          ],
          resources: [
            { type: '在线课程', name: '技术演讲训练', provider: '极客时间' },
            { type: '实践活动', name: '技术分享会', frequency: '每周一次' },
            { type: '工具推荐', name: 'Toastmasters', type: '演讲俱乐部' }
          ],
          expectedOutcome: '提升技术表达能力，增强团队协作效果'
        },
        {
          id: 3,
          title: '项目实战经验积累',
          priority: 'medium',
          urgency: '1个月内开始',
          duration: '6周',
          difficulty: '高级',
          modules: [
            '端到端项目开发',
            '模型部署与运维',
            '性能优化实践',
            '项目管理技能'
          ],
          resources: [
            { type: '实战项目', name: '推荐系统开发', complexity: '企业级' },
            { type: '开源贡献', name: 'TensorFlow/PyTorch', type: '代码贡献' },
            { type: '竞赛参与', name: 'Kaggle竞赛', level: '高级组' }
          ],
          expectedOutcome: '具备完整项目开发能力，提升工程实践水平'
        }
      ],
      milestones: [
        { week: 2, goal: '完成深度学习基础理论学习', status: 'pending' },
        { week: 4, goal: '完成第一个神经网络项目', status: 'pending' },
        { week: 8, goal: '参与技术分享并获得反馈', status: 'pending' },
        { week: 12, goal: '完成端到端项目开发', status: 'pending' }
      ]
    },
    interactiveElements: [
      '学习路径图',
      '进度追踪器',
      '资源收藏夹',
      '目标设定工具'
    ],
    tips: [
      '优先关注高优先级的学习建议',
      '结合个人时间和精力合理安排',
      '定期回顾学习进度并调整计划',
      '积极参与实践项目和技术社区',
      '寻找学习伙伴或导师指导'
    ]
  }
]

// 演示工具函数
export const demoUtils = {
  // 模拟实时数据更新
  simulateRealTimeData: (callback, interval = 1000) => {
    const timer = setInterval(() => {
      const data = {
        speech: generateMockData.speechAnalysis(),
        video: generateMockData.videoAnalysis(),
        text: generateMockData.textAnalysis(),
        timestamp: new Date().toISOString()
      };
      callback(data);
    }, interval);
    return timer;
  },

  // 格式化分数显示
  formatScore: (score) => {
    if (score >= 90) return { level: '优秀', color: '#52c41a' };
    if (score >= 80) return { level: '良好', color: '#1890ff' };
    if (score >= 70) return { level: '中等', color: '#faad14' };
    if (score >= 60) return { level: '及格', color: '#fa8c16' };
    return { level: '待提升', color: '#f5222d' };
  },

  // 计算能力匹配度
  calculateMatch: (userScores, jobRequirements) => {
    const weights = {
      technical: 0.3,
      communication: 0.2,
      logic: 0.2,
      innovation: 0.15,
      teamwork: 0.1,
      learning: 0.05
    };

    let totalMatch = 0;
    Object.keys(weights).forEach(key => {
      const userScore = userScores[key] || 0;
      const requirement = jobRequirements[key] || 70;
      const match = Math.min(userScore / requirement, 1.2); // 允许超出20%
      totalMatch += match * weights[key];
    });

    return Math.round(totalMatch * 100);
  },

  // 生成学习建议
  generateLearningAdvice: (scores) => {
    const advice = [];
    Object.entries(scores).forEach(([skill, score]) => {
      if (score < 70) {
        advice.push({
          skill,
          priority: 'high',
          suggestion: `${skill}能力需要重点提升，建议加强相关训练`
        });
      } else if (score < 85) {
        advice.push({
          skill,
          priority: 'medium',
          suggestion: `${skill}能力良好，可进一步优化`
        });
      }
    });
    return advice;
  }
};

// 演示状态管理
export const demoState = {
  currentStep: 0,
  isPlaying: false,
  playbackSpeed: 1,
  autoAdvance: true,
  showTips: true,

  // 状态更新方法
  updateStep: (step) => {
    demoState.currentStep = step;
  },

  togglePlay: () => {
    demoState.isPlaying = !demoState.isPlaying;
  },

  setSpeed: (speed) => {
    demoState.playbackSpeed = speed;
  }
};

// 技术架构信息
export const architectureInfo = {
  overview: {
    title: '多模态智能面试评测系统架构',
    description: '基于现代微服务架构，集成AI大模型的智能面试平台',
    version: 'v2.0',
    lastUpdated: '2024-12-19'
  },
  layers: [
    {
      id: 'frontend',
      name: '前端展示层',
      technologies: [
        { name: 'Vue.js 3', version: '3.3+', purpose: '响应式UI框架' },
        { name: 'Element Plus', version: '2.4+', purpose: 'UI组件库' },
        { name: 'WebRTC', version: 'Latest', purpose: '实时音视频' },
        { name: 'Canvas API', version: 'HTML5', purpose: '图形渲染' },
        { name: 'Vite', version: '4.5+', purpose: '构建工具' }
      ],
      description: '负责用户界面展示和交互，支持多模态输入和实时反馈',
      color: '#4CAF50',
      responsibilities: [
        '用户界面渲染和交互',
        '多模态数据采集（文本/语音/视频）',
        '实时状态显示和反馈',
        '响应式设计和跨平台适配'
      ],
      keyFeatures: ['组件化架构', '状态管理', '路由控制', '实时通信']
    },
    {
      id: 'backend',
      name: '后端服务层',
      technologies: [
        { name: 'FastAPI', version: '0.104+', purpose: 'Web框架' },
        { name: 'WebSocket', version: 'ASGI', purpose: '实时通信' },
        { name: 'SQLAlchemy', version: '2.0+', purpose: 'ORM框架' },
        { name: 'Pydantic', version: '2.0+', purpose: '数据验证' },
        { name: 'Uvicorn', version: '0.24+', purpose: 'ASGI服务器' }
      ],
      description: '处理业务逻辑、数据验证、API服务和系统集成',
      color: '#2196F3',
      responsibilities: [
        'RESTful API服务提供',
        '业务逻辑处理和验证',
        '数据库操作和管理',
        '第三方服务集成'
      ],
      keyFeatures: ['异步处理', 'API文档', '数据验证', '错误处理']
    },
    {
      id: 'ai',
      name: 'AI智能服务层',
      technologies: [
        { name: '讯飞星火大模型', version: 'V3.5', purpose: '自然语言理解' },
        { name: '多模态分析引擎', version: 'Custom', purpose: '综合分析' },
        { name: '语音识别服务', version: 'iFlytek', purpose: '语音转文本' },
        { name: '视频分析算法', version: 'OpenCV+', purpose: '视频处理' },
        { name: '机器学习模型', version: 'PyTorch', purpose: '智能评估' }
      ],
      description: '提供智能面试问题生成、多维度评估和个性化建议',
      color: '#FF9800',
      responsibilities: [
        '智能问题生成和优化',
        '多模态数据分析处理',
        '能力评估和打分',
        '个性化建议生成'
      ],
      keyFeatures: ['大模型集成', '实时分析', '智能评估', '学习推荐']
    },
    {
      id: 'storage',
      name: '数据存储层',
      technologies: [
        { name: 'SQLite', version: '3.40+', purpose: '关系数据库' },
        { name: '文件存储系统', version: 'Custom', purpose: '媒体文件' },
        { name: 'Redis', version: '7.0+', purpose: '缓存服务' },
        { name: '数据备份系统', version: 'Custom', purpose: '数据安全' }
      ],
      description: '存储用户数据、面试记录、评估结果和系统配置',
      color: '#9C27B0',
      responsibilities: [
        '用户信息和权限管理',
        '面试数据持久化存储',
        '评估结果和报告保存',
        '系统配置和日志记录'
      ],
      keyFeatures: ['数据持久化', '缓存优化', '备份恢复', '安全存储']
    }
  ],
  features: [
    {
      icon: 'Lightning',
      title: '高性能架构',
      description: '异步处理、实时通信、智能缓存',
      category: '性能优化',
      priority: 'high',
      details: [
        'FastAPI异步框架提供高并发支持，单机可处理1000+并发请求',
        'WebSocket实现实时双向通信，延迟低于100ms',
        'Redis缓存提升响应速度，热点数据访问提速80%',
        '智能负载均衡和资源调度，自动扩缩容',
        'CDN加速静态资源，全球访问优化'
      ],
      metrics: {
        concurrency: '1000+ 并发用户',
        latency: '< 100ms 响应时间',
        throughput: '10000+ QPS',
        availability: '99.9% 可用性'
      }
    },
    {
      icon: 'Lock',
      title: '安全可靠',
      description: '数据加密、隐私保护、安全认证',
      category: '安全保障',
      priority: 'critical',
      details: [
        'HTTPS/TLS 1.3加密传输保护数据安全',
        '用户隐私数据本地化处理，符合GDPR规范',
        'JWT令牌认证机制，支持多因子认证',
        '定期安全审计和漏洞修复，零日漏洞响应',
        '数据脱敏和匿名化处理，保护用户隐私',
        '访问控制和权限管理，细粒度权限控制'
      ],
      compliance: ['GDPR', 'ISO 27001', '等保三级', '数据安全法'],
      securityFeatures: ['数据加密', '访问控制', '审计日志', '威胁检测']
    },
    {
      icon: 'Expand',
      title: '可扩展设计',
      description: '模块化架构、插件系统、云原生',
      category: '架构设计',
      priority: 'high',
      details: [
        '微服务架构支持独立扩展，服务解耦降低复杂度',
        '插件化AI模型集成，支持多种大模型切换',
        'Docker容器化部署，一键部署和环境一致性',
        '支持多云环境部署，避免厂商锁定',
        'API网关统一管理，版本控制和流量管理',
        '事件驱动架构，异步处理提升性能'
      ],
      scalabilityMetrics: {
        horizontal: '支持水平扩展',
        vertical: '支持垂直扩展',
        autoScaling: '自动弹性伸缩',
        multiCloud: '多云部署支持'
      }
    },
    {
      icon: 'Monitor',
      title: '智能监控',
      description: '全链路监控、智能告警、性能分析',
      category: '运维监控',
      priority: 'medium',
      details: [
        '全链路性能监控，端到端性能追踪',
        '智能告警系统，异常自动检测和通知',
        '实时日志分析，快速问题定位',
        '用户行为分析，优化用户体验',
        '资源使用监控，成本优化建议'
      ],
      monitoringTools: ['Prometheus', 'Grafana', 'ELK Stack', 'Jaeger']
    },
    {
      icon: 'Connection',
      title: '开放集成',
      description: '标准API、第三方集成、生态兼容',
      category: '集成能力',
      priority: 'medium',
      details: [
        'RESTful API标准接口，易于集成',
        '支持主流AI服务提供商集成',
        'Webhook事件通知，实时数据同步',
        'SDK多语言支持，降低接入成本',
        '开放平台生态，第三方应用扩展'
      ],
      integrations: ['讯飞星火', '百度文心', '阿里通义', '腾讯混元']
    }
  ],

  // 技术栈详情
  techStack: {
    frontend: {
      framework: 'Vue.js 3 + Composition API',
      ui: 'Element Plus + 自定义组件',
      build: 'Vite + TypeScript',
      state: 'Pinia 状态管理',
      routing: 'Vue Router 4',
      testing: 'Vitest + Vue Test Utils'
    },
    backend: {
      framework: 'FastAPI + Python 3.11',
      database: 'SQLAlchemy + SQLite/PostgreSQL',
      cache: 'Redis + 内存缓存',
      queue: 'Celery + Redis',
      auth: 'JWT + OAuth2',
      testing: 'Pytest + Factory Boy'
    },
    ai: {
      llm: '讯飞星火认知大模型 V3.5',
      speech: 'iFlytek 语音识别/合成',
      vision: 'OpenCV + 深度学习模型',
      nlp: 'Transformers + 自然语言处理',
      ml: 'Scikit-learn + PyTorch'
    },
    infrastructure: {
      containerization: 'Docker + Docker Compose',
      orchestration: 'Kubernetes (可选)',
      monitoring: 'Prometheus + Grafana',
      logging: 'ELK Stack',
      ci_cd: 'GitHub Actions'
    }
  }
}

// 演示服务类
export class DemoService {
  // 获取演示视频列表
  static getVideos(category = null) {
    const allVideos = [demoVideos.main, ...demoVideos.tutorials]
    if (category) {
      return allVideos.filter(video => video.category === category)
    }
    return allVideos
  }

  // 获取功能演示数据
  static getFeatures(difficulty = null) {
    if (difficulty) {
      return featuresDemos.filter(feature => feature.difficulty === difficulty)
    }
    return featuresDemos
  }

  // 获取交互式演示步骤
  static getInteractiveSteps() {
    return interactiveSteps
  }

  // 获取技术架构信息
  static getArchitecture() {
    return architectureInfo
  }

  // 获取演示统计数据
  static getDemoStats() {
    return {
      totalVideos: demoVideos.length,
      totalFeatures: featuresDemos.length,
      totalSteps: interactiveSteps.length,
      categories: [...new Set(demoVideos.map(v => v.category))],
      difficulties: [...new Set(featuresDemos.map(f => f.difficulty))],
      estimatedTotalTime: featuresDemos.reduce((total, feature) => {
        const time = parseInt(feature.estimatedTime) || 0;
        return total + time;
      }, 0)
    }
  }

  // 播放演示视频
  static playVideo(videoId) {
    // 从所有视频中查找指定ID的视频
    const allVideos = [demoVideos.main, ...demoVideos.tutorials]
    const video = allVideos.find(v => v.id === videoId)

    if (!video) {
      return {
        success: false,
        message: `视频 ${videoId} 不存在`
      }
    }

    console.log(`播放演示视频: ${video.title}`)
    return {
      success: true,
      message: `正在播放视频: ${video.title}`,
      video: video
    }
  }

  // 开始功能演示
  static startFeatureDemo(featureId) {
    const feature = featuresDemos.find(f => f.id === featureId)
    if (feature) {
      console.log(`开始功能演示: ${feature.title}`)
      return {
        success: true,
        feature: feature,
        message: `正在演示: ${feature.title}`,
        estimatedTime: feature.estimatedTime,
        steps: feature.demoSteps
      }
    }
    return {
      success: false,
      message: '未找到指定的功能演示'
    }
  }

  // 开始交互式演示
  static startInteractiveDemo(stepId) {
    const step = interactiveSteps.find(s => s.id === stepId)
    if (step) {
      console.log(`开始交互式演示: ${step.title}`)
      return {
        success: true,
        step: step,
        message: `正在进行交互式演示: ${step.title}`,
        mockData: step.mockData
      }
    }
    return {
      success: false,
      message: '未找到指定的交互式演示'
    }
  }

  // 生成模拟数据
  static generateMockAnalysis(type = 'all') {
    switch (type) {
      case 'speech':
        return generateMockData.speechAnalysis()
      case 'video':
        return generateMockData.videoAnalysis()
      case 'text':
        return generateMockData.textAnalysis()
      default:
        return {
          speech: generateMockData.speechAnalysis(),
          video: generateMockData.videoAnalysis(),
          text: generateMockData.textAnalysis(),
          timestamp: new Date().toISOString()
        }
    }
  }

  // 搜索演示内容
  static searchDemo(keyword) {
    const results = {
      videos: [],
      features: [],
      steps: []
    }

    if (keyword) {
      const lowerKeyword = keyword.toLowerCase()

      results.videos = demoVideos.filter(video =>
        video.title.toLowerCase().includes(lowerKeyword) ||
        video.description.toLowerCase().includes(lowerKeyword)
      )

      results.features = featuresDemos.filter(feature =>
        feature.title.toLowerCase().includes(lowerKeyword) ||
        feature.description.toLowerCase().includes(lowerKeyword)
      )

      results.steps = interactiveSteps.filter(step =>
        step.title.toLowerCase().includes(lowerKeyword) ||
        step.description.toLowerCase().includes(lowerKeyword)
      )
    }

    return results
  }

  // 获取推荐演示内容
  static getRecommendations(userLevel = 'beginner') {
    const levelMap = {
      'beginner': '入门',
      'intermediate': '中级',
      'advanced': '高级'
    }

    const targetLevel = levelMap[userLevel] || '入门'

    return {
      videos: demoVideos.filter(v => v.difficulty === targetLevel).slice(0, 3),
      features: featuresDemos.filter(f => f.difficulty === targetLevel).slice(0, 3),
      steps: interactiveSteps.filter(s => s.difficulty === targetLevel).slice(0, 3)
    }
  }

  // 获取演示进度
  static getDemoProgress(completedItems = []) {
    const totalItems = demoVideos.length + featuresDemos.length + interactiveSteps.length
    const completedCount = completedItems.length
    const progress = totalItems > 0 ? Math.round((completedCount / totalItems) * 100) : 0

    return {
      total: totalItems,
      completed: completedCount,
      remaining: totalItems - completedCount,
      progress: progress,
      nextRecommendation: this.getNextRecommendation(completedItems)
    }
  }

  // 获取下一个推荐项目
  static getNextRecommendation(completedItems = []) {
    const allItems = [
      ...demoVideos.map(v => ({ ...v, type: 'video' })),
      ...featuresDemos.map(f => ({ ...f, type: 'feature' })),
      ...interactiveSteps.map(s => ({ ...s, type: 'step' }))
    ]

    const remaining = allItems.filter(item => !completedItems.includes(item.id))
    return remaining.length > 0 ? remaining[0] : null
  }

  // 获取演示场景
  static getDemoScenarios() {
    return demoScenarios
  }

  // 获取特定演示场景
  static getDemoScenario(scenarioId) {
    return demoScenarios[scenarioId] || null
  }

  // 获取主演示视频的详细信息
  static getMainVideoDetails() {
    return demoVideos.main
  }

  // 获取视频章节信息
  static getVideoChapters(videoId = 'main-demo') {
    const video = videoId === 'main-demo' ? demoVideos.main :
                  demoVideos.tutorials.find(v => v.id === videoId)
    return video ? video.chapters || [] : []
  }

  // 跳转到视频章节
  static jumpToChapter(videoId, chapterIndex) {
    const chapters = this.getVideoChapters(videoId)
    if (chapterIndex >= 0 && chapterIndex < chapters.length) {
      const chapter = chapters[chapterIndex]
      console.log(`跳转到章节: ${chapter.title} (${chapter.time})`)
      return {
        success: true,
        message: `已跳转到: ${chapter.title}`,
        chapter: chapter,
        timestamp: chapter.time
      }
    }
    return {
      success: false,
      message: '章节不存在'
    }
  }

  // 获取演示进度
  static getDemoProgress(scenarioId, currentStep = 0) {
    const scenario = this.getDemoScenario(scenarioId)
    if (!scenario) {
      return { progress: 0, totalSteps: 0 }
    }

    const totalSteps = scenario.steps.length
    const progress = Math.round((currentStep / totalSteps) * 100)

    return {
      progress,
      totalSteps,
      currentStep,
      nextStep: currentStep < totalSteps - 1 ? scenario.steps[currentStep + 1] : null,
      isComplete: currentStep >= totalSteps
    }
  }

  // 开始演示场景
  static startDemoScenario(scenarioId) {
    const scenario = this.getDemoScenario(scenarioId)
    if (scenario) {
      console.log(`开始演示场景: ${scenario.title}`)
      return {
        success: true,
        message: `开始演示: ${scenario.title}`,
        scenario: scenario,
        currentStep: 0
      }
    }
    return {
      success: false,
      message: '演示场景不存在'
    }
  }

  // 获取演示统计信息
  static getDemoStats() {
    const mainVideo = demoVideos.main
    const tutorials = demoVideos.tutorials
    const totalViews = mainVideo.views + tutorials.reduce((sum, video) => sum + video.views, 0)
    const avgRating = (mainVideo.rating + tutorials.reduce((sum, video) => sum + video.rating, 0)) / (tutorials.length + 1)

    return {
      totalVideos: tutorials.length + 1,
      totalViews: totalViews,
      averageRating: Math.round(avgRating * 10) / 10,
      totalDuration: this.calculateTotalDuration(),
      featuresCount: featuresDemos.length,
      scenariosCount: Object.keys(demoScenarios).length,
      lastUpdated: mainVideo.publishDate || '2024-06-15'
    }
  }

  // 计算总时长
  static calculateTotalDuration() {
    const mainDuration = this.parseDuration(demoVideos.main.duration)
    const tutorialsDuration = demoVideos.tutorials.reduce((total, video) => {
      return total + this.parseDuration(video.duration)
    }, 0)
    return this.formatDuration(mainDuration + tutorialsDuration)
  }

  // 解析时长字符串为分钟数
  static parseDuration(durationStr) {
    const parts = durationStr.split(':')
    return parseInt(parts[0]) + (parseInt(parts[1]) / 60)
  }

  // 格式化时长
  static formatDuration(minutes) {
    const hours = Math.floor(minutes / 60)
    const mins = Math.round(minutes % 60)
    return hours > 0 ? `${hours}:${mins.toString().padStart(2, '0')}` : `${mins}分钟`
  }
}

export default DemoService
