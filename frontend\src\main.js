console.log('🚀 main.js 开始执行');

import { createApp } from 'vue'
console.log('✅ Vue 导入成功');

import App from './App_simple.vue'
console.log('✅ App 组件导入成功');

// 暂时禁用路由和Element Plus来隔离问题
// import router from './router'
// import ElementPlus from 'element-plus'
// import 'element-plus/dist/index.css'

console.log('🔧 开始创建Vue应用');
const app = createApp(App)
console.log('✅ Vue应用创建成功');

// app.use(router)
// app.use(ElementPlus)

console.log('🎯 开始挂载应用到 #app');
app.mount('#app')
console.log('🎉 应用挂载完成');