<template>
  <div class="interview-selection">
    <div class="container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>智能面试评估系统</h1>
        <p class="subtitle">选择您的面试领域和岗位，开始个性化面试体验</p>
      </div>

      <!-- 选择表单 -->
      <div class="selection-form">
        <el-card class="form-card">
          <template #header>
            <div class="card-header">
              <span>面试配置</span>
              <el-tag type="info">多模态智能评估</el-tag>
            </div>
          </template>

          <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
            <!-- 技术领域选择 -->
            <el-form-item label="技术领域" prop="domain">
              <el-select 
                v-model="form.domain" 
                placeholder="请选择技术领域"
                @change="onDomainChange"
                style="width: 100%"
              >
                <el-option
                  v-for="domain in domains"
                  :key="domain"
                  :label="domain"
                  :value="domain"
                >
                  <span style="float: left">{{ domain }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">
                    {{ getDomainDescription(domain) }}
                  </span>
                </el-option>
              </el-select>
            </el-form-item>

            <!-- 岗位类型选择 -->
            <el-form-item label="岗位类型" prop="position">
              <el-select 
                v-model="form.position" 
                placeholder="请选择岗位类型"
                :disabled="!form.domain"
                style="width: 100%"
              >
                <el-option
                  v-for="position in availablePositions"
                  :key="position"
                  :label="position"
                  :value="position"
                >
                  <span style="float: left">{{ position }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">
                    {{ getPositionDescription(position) }}
                  </span>
                </el-option>
              </el-select>
            </el-form-item>

            <!-- 面试模式选择 -->
            <el-form-item label="面试模式" prop="mode">
              <el-radio-group v-model="form.mode">
                <el-radio label="standard">标准模式</el-radio>
                <el-radio label="practice">练习模式</el-radio>
                <el-radio label="challenge">挑战模式</el-radio>
              </el-radio-group>
              <div class="mode-description">
                {{ getModeDescription(form.mode) }}
              </div>
            </el-form-item>

            <!-- 多模态选项 -->
            <el-form-item label="评估方式">
              <el-checkbox-group v-model="form.analysisTypes">
                <el-checkbox label="text">文本分析</el-checkbox>
                <el-checkbox label="audio">语音分析</el-checkbox>
                <el-checkbox label="video">视频分析</el-checkbox>
              </el-checkbox-group>
              <div class="analysis-description">
                <p><i class="el-icon-document"></i> 文本分析：内容相关性、逻辑结构、关键词覆盖</p>
                <p><i class="el-icon-microphone"></i> 语音分析：语音清晰度、语速、情感表达</p>
                <p><i class="el-icon-video-camera"></i> 视频分析：眼神交流、面部表情、身体姿态</p>
              </div>
            </el-form-item>

            <!-- 预计时长 -->
            <el-form-item label="预计时长">
              <el-tag type="warning">{{ getEstimatedDuration() }}</el-tag>
              <span class="duration-note">根据选择的领域和岗位自动计算</span>
            </el-form-item>
          </el-form>

          <!-- 操作按钮 -->
          <div class="form-actions">
            <el-button 
              type="primary" 
              size="large"
              @click="startInterview"
              :loading="isStarting"
              :disabled="!isFormValid"
            >
              <i class="el-icon-video-play"></i>
              开始面试
            </el-button>
            
            <el-button 
              size="large"
              @click="previewQuestions"
              :disabled="!form.domain || !form.position"
            >
              <i class="el-icon-view"></i>
              预览题目
            </el-button>
          </div>
        </el-card>
      </div>

      <!-- 功能特色展示 -->
      <div class="features-section">
        <h2>系统特色</h2>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">
              <i class="el-icon-cpu"></i>
            </div>
            <h3>AI智能评估</h3>
            <p>基于讯飞星火大模型，提供专业的面试评估和反馈</p>
          </div>
          
          <div class="feature-card">
            <div class="feature-icon">
              <i class="el-icon-camera"></i>
            </div>
            <h3>多模态分析</h3>
            <p>综合文本、语音、视频多维度数据，全面评估面试表现</p>
          </div>
          
          <div class="feature-card">
            <div class="feature-icon">
              <i class="el-icon-data-analysis"></i>
            </div>
            <h3>可视化报告</h3>
            <p>生成详细的雷达图和分析报告，直观展示能力水平</p>
          </div>
          
          <div class="feature-card">
            <div class="feature-icon">
              <i class="el-icon-trophy"></i>
            </div>
            <h3>个性化建议</h3>
            <p>根据表现提供针对性的改进建议和学习路径</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 题目预览对话框 -->
    <el-dialog
      v-model="showPreview"
      title="题目预览"
      width="60%"
      :before-close="closePreview"
    >
      <div v-if="previewQuestions.length > 0" class="questions-preview">
        <div 
          v-for="(question, index) in previewQuestions" 
          :key="index"
          class="question-item"
        >
          <div class="question-header">
            <span class="question-number">题目 {{ index + 1 }}</span>
            <el-tag :type="getDifficultyColor(question.difficulty)" size="small">
              {{ question.difficulty }}
            </el-tag>
            <el-tag type="info" size="small">{{ question.type }}</el-tag>
          </div>
          <div class="question-content">
            {{ question.question }}
          </div>
          <div class="question-keywords" v-if="question.keywords && question.keywords.length > 0">
            <span class="keywords-label">关键词：</span>
            <el-tag 
              v-for="keyword in question.keywords" 
              :key="keyword"
              size="mini"
              effect="plain"
            >
              {{ keyword }}
            </el-tag>
          </div>
        </div>
      </div>
      <div v-else class="no-questions">
        <el-empty description="暂无题目数据"></el-empty>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closePreview">关闭</el-button>
          <el-button type="primary" @click="startInterviewFromPreview">
            开始面试
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { interviewApi, domains, positions, difficultyLevels } from '@/api/interview'

export default {
  name: 'InterviewSelection',
  setup() {
    const router = useRouter()
    
    // 响应式数据
    const formRef = ref(null)
    const form = ref({
      domain: '',
      position: '',
      mode: 'standard',
      analysisTypes: ['text', 'audio', 'video']
    })
    
    const domains = ref([])
    const availablePositions = ref([])
    const isStarting = ref(false)
    const showPreview = ref(false)
    const previewQuestions = ref([])
    
    // 表单验证规则
    const rules = {
      domain: [
        { required: true, message: '请选择技术领域', trigger: 'change' }
      ],
      position: [
        { required: true, message: '请选择岗位类型', trigger: 'change' }
      ],
      mode: [
        { required: true, message: '请选择面试模式', trigger: 'change' }
      ]
    }
    
    // 计算属性
    const isFormValid = computed(() => {
      return form.value.domain && 
             form.value.position && 
             form.value.mode &&
             form.value.analysisTypes.length > 0
    })
    
    // 方法
    const loadDomains = async () => {
      try {
        const response = await interviewApi.getDomains()
        domains.value = response.domains
      } catch (error) {
        ElMessage.error('加载技术领域失败: ' + error.message)
      }
    }
    
    const onDomainChange = async () => {
      form.value.position = ''
      availablePositions.value = []
      
      if (form.value.domain) {
        try {
          const response = await interviewApi.getPositions(form.value.domain)
          availablePositions.value = response.positions
        } catch (error) {
          ElMessage.error('加载岗位列表失败: ' + error.message)
        }
      }
    }
    
    const getDomainDescription = (domain) => {
      const descriptions = {
        '人工智能': 'AI算法、机器学习、深度学习',
        '大数据': '数据处理、分析、挖掘技术',
        '物联网': '嵌入式、传感器、通信协议'
      }
      return descriptions[domain] || ''
    }
    
    const getPositionDescription = (position) => {
      const descriptions = {
        '技术岗': '开发、架构、算法设计',
        '运维测试岗': '系统运维、测试、部署',
        '产品岗': '产品设计、需求分析、用户体验'
      }
      return descriptions[position] || ''
    }
    
    const getModeDescription = (mode) => {
      const descriptions = {
        'standard': '标准面试流程，包含多种题型，适合正式面试准备',
        'practice': '练习模式，可重复作答，适合技能提升',
        'challenge': '挑战模式，题目难度较高，适合高级候选人'
      }
      return descriptions[mode] || ''
    }
    
    const getEstimatedDuration = () => {
      if (!form.value.domain || !form.value.position) {
        return '请先选择领域和岗位'
      }
      
      // 根据不同配置估算时长
      let baseDuration = 20 // 基础时长（分钟）
      
      if (form.value.mode === 'challenge') {
        baseDuration += 10
      } else if (form.value.mode === 'practice') {
        baseDuration += 5
      }
      
      if (form.value.analysisTypes.includes('video')) {
        baseDuration += 5
      }
      
      return `约 ${baseDuration} 分钟`
    }
    
    const getDifficultyColor = (difficulty) => {
      switch(difficulty) {
        case 'easy': return 'success'
        case 'medium': return 'warning'
        case 'hard': return 'danger'
        default: return 'info'
      }
    }
    
    const previewQuestions = async () => {
      try {
        const response = await interviewApi.getQuestions(form.value.domain, form.value.position)
        previewQuestions.value = response.questions
        showPreview.value = true
      } catch (error) {
        ElMessage.error('加载题目预览失败: ' + error.message)
      }
    }
    
    const closePreview = () => {
      showPreview.value = false
    }
    
    const startInterview = async () => {
      if (!formRef.value) return
      
      const valid = await formRef.value.validate().catch(() => false)
      if (!valid) return
      
      isStarting.value = true
      
      try {
        // 创建面试会话
        const response = await interviewApi.createSession(
          form.value.domain, 
          form.value.position
        )
        
        ElMessage.success('面试会话创建成功')
        
        // 跳转到面试页面
        router.push({
          name: 'Interview',
          params: {
            sessionId: response.session_id
          },
          query: {
            domain: form.value.domain,
            position: form.value.position,
            mode: form.value.mode,
            analysisTypes: form.value.analysisTypes.join(',')
          }
        })
        
      } catch (error) {
        ElMessage.error('创建面试会话失败: ' + error.message)
      } finally {
        isStarting.value = false
      }
    }
    
    const startInterviewFromPreview = () => {
      closePreview()
      startInterview()
    }
    
    // 生命周期
    onMounted(() => {
      loadDomains()
    })
    
    return {
      // 数据
      formRef,
      form,
      rules,
      domains,
      availablePositions,
      isStarting,
      showPreview,
      previewQuestions,
      
      // 计算属性
      isFormValid,
      
      // 方法
      onDomainChange,
      getDomainDescription,
      getPositionDescription,
      getModeDescription,
      getEstimatedDuration,
      getDifficultyColor,
      previewQuestions,
      closePreview,
      startInterview,
      startInterviewFromPreview
    }
  }
}
</script>

<style scoped>
.interview-selection {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40px 20px;
}

.container {
  max-width: 1000px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
  color: white;
}

.page-header h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  font-weight: 300;
}

.subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

.selection-form {
  margin-bottom: 60px;
}

.form-card {
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  border-radius: 16px;
  border: none;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1.2rem;
  font-weight: 500;
}

.mode-description {
  margin-top: 8px;
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

.analysis-description {
  margin-top: 10px;
}

.analysis-description p {
  margin: 5px 0;
  font-size: 13px;
  color: #666;
  display: flex;
  align-items: center;
  gap: 8px;
}

.duration-note {
  margin-left: 10px;
  font-size: 13px;
  color: #999;
}

.form-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 30px;
}

.features-section {
  text-align: center;
  color: white;
}

.features-section h2 {
  font-size: 2rem;
  margin-bottom: 40px;
  font-weight: 300;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.feature-card {
  background: rgba(255,255,255,0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 30px 20px;
  text-align: center;
  border: 1px solid rgba(255,255,255,0.2);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 20px;
  color: #fff;
}

.feature-card h3 {
  font-size: 1.3rem;
  margin-bottom: 15px;
  font-weight: 500;
}

.feature-card p {
  font-size: 0.95rem;
  line-height: 1.6;
  opacity: 0.9;
  margin: 0;
}

.questions-preview {
  max-height: 500px;
  overflow-y: auto;
}

.question-item {
  margin-bottom: 20px;
  padding: 15px;
  border: 1px solid #eee;
  border-radius: 8px;
}

.question-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.question-number {
  font-weight: bold;
  color: #409EFF;
}

.question-content {
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 10px;
}

.question-keywords {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.keywords-label {
  font-size: 12px;
  color: #666;
}

.no-questions {
  text-align: center;
  padding: 40px;
}
</style>
