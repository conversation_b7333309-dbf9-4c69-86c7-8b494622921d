<template>
  <div class="debug-container">
    <h1>学习路径功能调试</h1>
    
    <div class="debug-section">
      <h2>测试生成学习路径</h2>
      
      <el-form :model="form" label-width="120px">
        <el-form-item label="技术领域">
          <el-select v-model="form.domain" placeholder="请选择技术领域">
            <el-option label="人工智能" value="人工智能"></el-option>
            <el-option label="大数据" value="大数据"></el-option>
            <el-option label="物联网" value="物联网"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="目标岗位">
          <el-select v-model="form.position" placeholder="请选择目标岗位">
            <el-option label="技术岗" value="技术岗"></el-option>
            <el-option label="运维测试岗" value="运维测试岗"></el-option>
            <el-option label="产品岗" value="产品岗"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="技能水平">
          <el-select v-model="form.skillLevel" placeholder="请选择技能水平">
            <el-option label="初级" value="初级"></el-option>
            <el-option label="中级" value="中级"></el-option>
            <el-option label="高级" value="高级"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="testGenerate" :loading="loading">
            测试生成路径
          </el-button>
          <el-button @click="clearResult">清空结果</el-button>
        </el-form-item>
      </el-form>
      
      <div v-if="result" class="result-section">
        <h3>生成结果:</h3>
        <div class="result-card">
          <h4>{{ result.title }}</h4>
          <p><strong>描述:</strong> {{ result.description }}</p>
          <p><strong>学习周期:</strong> {{ result.duration_weeks }}周</p>
          <p><strong>难度等级:</strong> {{ result.difficulty_level }}/5</p>
          
          <div v-if="result.modules && result.modules.length > 0" class="modules-section">
            <h5>学习模块 ({{ result.modules.length }}个):</h5>
            <div v-for="(module, index) in result.modules" :key="index" class="module-item">
              <h6>{{ index + 1 }}. {{ module.title }}</h6>
              <p>{{ module.description }}</p>
              <p><strong>学时:</strong> {{ module.duration_hours }}小时</p>
              <p><strong>类型:</strong> {{ module.type }}</p>
            </div>
          </div>
          
          <div v-if="result.career_goals && result.career_goals.length > 0" class="career-section">
            <h5>职业目标:</h5>
            <ul>
              <li v-for="goal in result.career_goals" :key="goal">{{ goal }}</li>
            </ul>
          </div>
        </div>
      </div>
      
      <div v-if="error" class="error-section">
        <h3>错误信息:</h3>
        <pre>{{ error }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import request from '@/utils/request'

const loading = ref(false)
const result = ref(null)
const error = ref(null)

const form = ref({
  domain: '人工智能',
  position: '技术岗',
  skillLevel: '中级'
})

const testGenerate = async () => {
  if (!form.value.domain || !form.value.position) {
    ElMessage.warning('请选择技术领域和目标岗位')
    return
  }
  
  loading.value = true
  result.value = null
  error.value = null
  
  try {
    console.log('发送请求数据:', form.value)
    
    const response = await request.post('/api/v1/learning-paths/personalized', {
      domain: form.value.domain,
      position: form.value.position,
      skill_level: form.value.skillLevel
    })
    
    console.log('收到响应:', response)
    
    if (response.success) {
      result.value = response.data
      ElMessage.success('学习路径生成成功！')
    } else {
      error.value = `API返回失败: ${response.message}`
      ElMessage.error('生成失败')
    }
  } catch (err) {
    console.error('请求失败:', err)
    error.value = err.message || '请求失败'
    ElMessage.error('请求失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}

const clearResult = () => {
  result.value = null
  error.value = null
}
</script>

<style scoped>
.debug-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.debug-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.result-section {
  margin-top: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.result-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.modules-section {
  margin-top: 20px;
}

.module-item {
  margin: 15px 0;
  padding: 15px;
  background: #f5f5f5;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.career-section {
  margin-top: 20px;
}

.career-section ul {
  margin: 10px 0;
  padding-left: 20px;
}

.error-section {
  margin-top: 20px;
  padding: 20px;
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 8px;
  color: #f56c6c;
}

.error-section pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
