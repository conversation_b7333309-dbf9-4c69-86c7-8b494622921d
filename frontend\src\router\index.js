import { createRouter, createWebHistory } from 'vue-router'
import HomePage from '../views/HomePage.vue'
import DemoPage from '../views/DemoPage.vue'
import InterviewSelection from '../views/InterviewSelection.vue'
import InterviewSetup from '../views/InterviewSetup.vue'
import InterviewingPage from '../views/InterviewingPage.vue'
import ReportView from '../views/ReportView.vue'
import LearningPathPage from '../views/LearningPathPage.vue'
import TestLearningPath from '../views/TestLearningPath.vue'
import TestReportToLearningPath from '../views/TestReportToLearningPath.vue'
import DebugLearningPath from '../views/DebugLearningPath.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: HomePage,
  },
  {
    path: '/demo',
    name: 'Demo',
    component: DemoPage,
  },
  {
    path: '/interview-selection',
    name: 'InterviewSelection',
    component: InterviewSelection,
  },
  {
    path: '/setup',
    name: 'InterviewSetup',
    component: InterviewSetup,
  },
  {
    path: '/interview/:sessionId?',
    name: 'Interviewing',
    component: InterviewingPage,
    props: true
  },
  {
    path: '/report/:sessionId',
    name: 'Report',
    component: ReportView,
    props: true
  },
  {
    path: '/learning-path/:sessionId?',
    name: 'LearningPath',
    component: LearningPathPage,
    props: true
  },
  {
    path: '/test-learning-path',
    name: 'TestLearningPath',
    component: TestLearningPath
  },
  {
    path: '/test-report-to-learning',
    name: 'TestReportToLearningPath',
    component: TestReportToLearningPath
  },
  {
    path: '/debug-learning-path',
    name: 'DebugLearningPath',
    component: DebugLearningPath
  },
]

const router = createRouter({
  history: createWebHistory(),
  routes,
})

export default router 