import { createRouter, createWebHistory } from 'vue-router'
import HomePage from '../views/HomePage.vue'
import InterviewSelection from '../views/InterviewSelection.vue'
import InterviewSetup from '../views/InterviewSetup.vue'
import InterviewingPage from '../views/InterviewingPage.vue'
import ReportView from '../views/ReportView.vue'
import LearningPathPage from '../views/LearningPathPage.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: HomePage,
  },
  {
    path: '/interview-selection',
    name: 'InterviewSelection',
    component: InterviewSelection,
  },
  {
    path: '/setup',
    name: 'InterviewSetup',
    component: InterviewSetup,
  },
  {
    path: '/interview/:sessionId?',
    name: 'Interviewing',
    component: InterviewingPage,
    props: true
  },
  {
    path: '/report/:sessionId',
    name: 'Report',
    component: ReportView,
    props: true
  },
  {
    path: '/learning-path/:sessionId?',
    name: 'LearningPath',
    component: LearningPathPage,
    props: true
  },
]

const router = createRouter({
  history: createWebHistory(),
  routes,
})

export default router 