<template>
  <div class="home-container">
    <!-- 英雄区域 -->
    <section class="hero-section">
      <div class="hero-background">
        <div class="hero-pattern"></div>
      </div>

      <div class="hero-content">
        <div class="hero-text fade-in-up">
          <h1 class="hero-title">
            <span class="text-gradient">多模态智能面试评测系统</span>
          </h1>
          <p class="hero-subtitle fade-in-up delay-200">
            基于讯飞星火大模型，融合语音、视频、文本多维度分析
          </p>
          <p class="hero-description fade-in-up delay-300">
            通过AI驱动的智能评测技术，为您提供专业的面试指导和个性化学习路径，
            助力您在人工智能、大数据、物联网等技术领域脱颖而出
          </p>

          <div class="hero-actions fade-in-up delay-400">
            <el-button
              type="primary"
              size="large"
              class="btn-gradient btn-large btn-icon hover-lift"
              @click="startInterview"
            >
              <el-icon><VideoCamera /></el-icon>
              开始智能面试
            </el-button>
            <el-button
              size="large"
              class="btn-large btn-icon hover-lift"
              @click="viewLearningPath"
            >
              <el-icon><Reading /></el-icon>
              查看学习路径
            </el-button>
          </div>
        </div>

        <div class="hero-visual fade-in-up delay-500">
          <div class="visual-card">
            <div class="ai-animation">
              <div class="ai-circle pulse"></div>
              <div class="ai-waves">
                <div class="wave wave-1"></div>
                <div class="wave wave-2"></div>
                <div class="wave wave-3"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 功能特色区域 -->
    <section class="features-section">
      <div class="container">
        <h2 class="section-title text-center fade-in-up">核心功能特色</h2>
        <p class="section-subtitle text-center fade-in-up delay-200">
          全方位智能评测，助您精准提升面试技能
        </p>

        <div class="features-grid">
          <div class="feature-card hover-lift fade-in-up delay-300" v-for="(feature, index) in features" :key="index">
            <div class="feature-icon">
              <component :is="feature.icon" />
            </div>
            <h3 class="feature-title">{{ feature.title }}</h3>
            <p class="feature-description">{{ feature.description }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 技术领域区域 -->
    <section class="domains-section">
      <div class="container">
        <h2 class="section-title text-center fade-in-up">支持技术领域</h2>
        <p class="section-subtitle text-center fade-in-up delay-200">
          覆盖热门技术方向，精准匹配岗位需求
        </p>

        <div class="domains-grid">
          <div class="domain-card hover-scale fade-in-up delay-300" v-for="(domain, index) in domains" :key="index">
            <div class="domain-header">
              <div class="domain-icon">
                <component :is="domain.icon" />
              </div>
              <h3 class="domain-title">{{ domain.title }}</h3>
            </div>
            <div class="domain-content">
              <p class="domain-description">{{ domain.description }}</p>
              <div class="domain-positions">
                <el-tag
                  v-for="position in domain.positions"
                  :key="position"
                  class="tag-modern tag-primary"
                  size="small"
                >
                  {{ position }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 评测指标区域 -->
    <section class="metrics-section">
      <div class="container">
        <h2 class="section-title text-center fade-in-up">六大核心评测指标</h2>
        <p class="section-subtitle text-center fade-in-up delay-200">
          多维度量化分析，全面评估您的综合能力
        </p>

        <div class="metrics-grid">
          <div class="metric-item fade-in-up" v-for="(metric, index) in metrics" :key="index" :class="`delay-${(index + 1) * 100}`">
            <div class="metric-icon">
              <component :is="metric.icon" />
            </div>
            <h4 class="metric-title">{{ metric.title }}</h4>
            <p class="metric-description">{{ metric.description }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA区域 -->
    <section class="cta-section">
      <div class="container">
        <div class="cta-content fade-in-up">
          <h2 class="cta-title">准备好开始您的智能面试之旅了吗？</h2>
          <p class="cta-description">
            立即体验AI驱动的面试评测，获得专业指导和个性化学习建议
          </p>
          <div class="cta-actions">
            <el-button
              type="primary"
              size="large"
              class="btn-gradient btn-large btn-icon hover-lift"
              @click="startInterview"
            >
              <el-icon><VideoCamera /></el-icon>
              立即开始面试
            </el-button>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import {
  VideoCamera,
  Reading,
  Monitor,
  DataAnalysis,
  Connection,
  ChatDotRound,
  TrendCharts,
  Star,
  Trophy,
  Lightning,
  BrainFilled
} from '@element-plus/icons-vue'

const router = useRouter()

const startInterview = () => {
  router.push('/interview-selection')
}

const viewLearningPath = () => {
  router.push('/learning-path')
}

// 功能特色数据
const features = ref([
  {
    icon: VideoCamera,
    title: '多模态分析',
    description: '融合语音、视频、文本三维数据，全方位评估面试表现'
  },
  {
    icon: BrainFilled,
    title: 'AI智能评测',
    description: '基于讯飞星火大模型，提供专业准确的智能评分'
  },
  {
    icon: TrendCharts,
    title: '可视化报告',
    description: '生成详细的能力雷达图和改进建议，直观展示评测结果'
  },
  {
    icon: Reading,
    title: '个性化学习',
    description: '根据评测结果推荐定制化学习路径和提升方案'
  }
])

// 技术领域数据
const domains = ref([
  {
    icon: BrainFilled,
    title: '人工智能',
    description: '涵盖机器学习、深度学习、自然语言处理等前沿技术',
    positions: ['算法工程师', '机器学习工程师', 'AI产品经理']
  },
  {
    icon: DataAnalysis,
    title: '大数据',
    description: '包含数据挖掘、数据分析、大数据架构等核心技能',
    positions: ['数据分析师', '大数据工程师', '数据科学家']
  },
  {
    icon: Connection,
    title: '物联网',
    description: '覆盖物联网架构、嵌入式开发、智能硬件等技术栈',
    positions: ['物联网工程师', '嵌入式工程师', '硬件工程师']
  }
])

// 评测指标数据
const metrics = ref([
  {
    icon: Star,
    title: '专业知识',
    description: '评估技术理论掌握程度和实践应用能力'
  },
  {
    icon: Trophy,
    title: '技能匹配',
    description: '分析技能与目标岗位的匹配度'
  },
  {
    icon: ChatDotRound,
    title: '语言表达',
    description: '评价口语表达清晰度和逻辑性'
  },
  {
    icon: Lightning,
    title: '逻辑思维',
    description: '考察问题分析和解决思路'
  },
  {
    icon: TrendCharts,
    title: '创新能力',
    description: '评估创新思维和解决方案创造力'
  },
  {
    icon: Monitor,
    title: '应变抗压',
    description: '测试面对压力和突发情况的应对能力'
  }
])
</script>

<style scoped>
.home-container {
  min-height: 100vh;
  background: var(--bg-secondary);
}

/* ==================== 英雄区域 ==================== */
.hero-section {
  position: relative;
  min-height: 80vh;
  display: flex;
  align-items: center;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.1;
}

.hero-pattern {
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.2) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.2) 2px, transparent 2px);
  background-size: 60px 60px;
  animation: float 20s ease-in-out infinite;
}

.hero-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4xl);
  align-items: center;
  position: relative;
  z-index: 1;
}

.hero-text {
  color: white;
}

.hero-title {
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-lg);
}

.hero-subtitle {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-base);
  opacity: 0.9;
}

.hero-description {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-2xl);
  opacity: 0.8;
}

.hero-actions {
  display: flex;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.visual-card {
  width: 300px;
  height: 300px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-2xl);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.ai-animation {
  position: relative;
  width: 120px;
  height: 120px;
}

.ai-circle {
  width: 120px;
  height: 120px;
  border-radius: var(--border-radius-full);
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
}

.ai-waves {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.wave {
  position: absolute;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--border-radius-full);
  animation: wave-animation 3s ease-in-out infinite;
}

.wave-1 {
  width: 160px;
  height: 160px;
  margin: -80px 0 0 -80px;
  animation-delay: 0s;
}

.wave-2 {
  width: 200px;
  height: 200px;
  margin: -100px 0 0 -100px;
  animation-delay: 1s;
}

.wave-3 {
  width: 240px;
  height: 240px;
  margin: -120px 0 0 -120px;
  animation-delay: 2s;
}

@keyframes wave-animation {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

/* ==================== 功能特色区域 ==================== */
.features-section {
  padding: var(--spacing-4xl) 0;
  background: var(--bg-primary);
}

.section-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-base);
}

.section-subtitle {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-3xl);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-2xl);
  margin-top: var(--spacing-3xl);
}

.feature-card {
  background: var(--bg-primary);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-2xl);
  text-align: center;
  box-shadow: var(--shadow-base);
  border: 1px solid var(--border-light);
  transition: var(--transition-base);
}

.feature-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto var(--spacing-lg);
  background: var(--gradient-primary);
  border-radius: var(--border-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32px;
}

.feature-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-base);
}

.feature-description {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
}

/* ==================== 技术领域区域 ==================== */
.domains-section {
  padding: var(--spacing-4xl) 0;
  background: var(--bg-secondary);
}

.domains-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-2xl);
  margin-top: var(--spacing-3xl);
}

.domain-card {
  background: var(--bg-primary);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-2xl);
  box-shadow: var(--shadow-base);
  border: 1px solid var(--border-light);
  transition: var(--transition-base);
}

.domain-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-base);
  margin-bottom: var(--spacing-lg);
}

.domain-icon {
  width: 60px;
  height: 60px;
  background: var(--gradient-tech);
  border-radius: var(--border-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.domain-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
}

.domain-description {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-lg);
}

.domain-positions {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

/* ==================== 评测指标区域 ==================== */
.metrics-section {
  padding: var(--spacing-4xl) 0;
  background: var(--bg-primary);
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-xl);
  margin-top: var(--spacing-3xl);
}

.metric-item {
  text-align: center;
  padding: var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  transition: var(--transition-base);
}

.metric-item:hover {
  background: var(--bg-secondary);
  transform: translateY(-4px);
}

.metric-icon {
  width: 60px;
  height: 60px;
  margin: 0 auto var(--spacing-base);
  background: var(--gradient-success);
  border-radius: var(--border-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.metric-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.metric-description {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
}

/* ==================== CTA区域 ==================== */
.cta-section {
  padding: var(--spacing-4xl) 0;
  background: var(--gradient-primary);
  color: white;
  text-align: center;
}

.cta-content {
  max-width: 600px;
  margin: 0 auto;
}

.cta-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-base);
}

.cta-description {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-2xl);
  opacity: 0.9;
}

.cta-actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 768px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
    text-align: center;
  }

  .hero-title {
    font-size: var(--font-size-3xl);
  }

  .hero-subtitle {
    font-size: var(--font-size-lg);
  }

  .hero-description {
    font-size: var(--font-size-base);
  }

  .hero-actions {
    justify-content: center;
  }

  .visual-card {
    width: 250px;
    height: 250px;
  }

  .features-grid,
  .domains-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-base);
  }

  .cta-title {
    font-size: var(--font-size-2xl);
  }

  .cta-actions {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: var(--font-size-2xl);
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .domain-header {
    flex-direction: column;
    text-align: center;
  }
}
</style>