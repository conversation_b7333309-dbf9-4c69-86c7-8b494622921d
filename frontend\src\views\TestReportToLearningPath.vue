<template>
  <div class="test-container">
    <h1>报告页面到学习路径跳转测试</h1>
    
    <div class="test-section">
      <h2>模拟报告页面</h2>
      <p>模拟面试会话ID: {{ sessionId }}</p>
      
      <div class="mock-report">
        <h3>面试评估报告</h3>
        <div class="scores">
          <div class="score-item">
            <span>专业知识:</span>
            <span>{{ mockReport.professional_knowledge }}分</span>
          </div>
          <div class="score-item">
            <span>技能匹配:</span>
            <span>{{ mockReport.skill_matching }}分</span>
          </div>
          <div class="score-item">
            <span>语言表达:</span>
            <span>{{ mockReport.language_expression }}分</span>
          </div>
          <div class="score-item">
            <span>逻辑思维:</span>
            <span>{{ mockReport.logical_thinking }}分</span>
          </div>
          <div class="score-item">
            <span>创新能力:</span>
            <span>{{ mockReport.innovation_ability }}分</span>
          </div>
          <div class="score-item">
            <span>抗压能力:</span>
            <span>{{ mockReport.stress_resistance }}分</span>
          </div>
        </div>
        
        <div class="overall-score">
          <h4>综合得分: {{ mockReport.overall_score }}分</h4>
        </div>
        
        <div class="action-buttons">
          <el-button type="primary" @click="downloadReport">
            <i class="el-icon-download"></i>
            下载报告
          </el-button>
          
          <el-button @click="shareReport">
            <i class="el-icon-share"></i>
            分享报告
          </el-button>
          
          <el-button @click="startNewInterview">
            <i class="el-icon-refresh"></i>
            重新面试
          </el-button>
          
          <el-button type="success" @click="viewLearningPath">
            <i class="el-icon-guide"></i>
            查看学习路径
          </el-button>
        </div>
      </div>
    </div>
    
    <div class="test-section">
      <h2>测试不同会话的学习路径推荐</h2>
      <div class="session-tests">
        <div class="session-test">
          <h4>会话5 - 人工智能技术岗 (平衡型)</h4>
          <el-button @click="testSession(5)">测试会话5学习路径</el-button>
        </div>
        <div class="session-test">
          <h4>会话6 - 大数据技术岗 (有薄弱环节)</h4>
          <el-button @click="testSession(6)">测试会话6学习路径</el-button>
        </div>
      </div>
    </div>
    
    <div v-if="testResult" class="test-result">
      <h3>测试结果:</h3>
      <div class="result-content">
        <p><strong>会话信息:</strong> {{ testResult.session_info?.domain }} - {{ testResult.session_info?.position }}</p>
        <p><strong>技能水平:</strong> {{ testResult.skill_level }}</p>
        <p><strong>薄弱环节:</strong> {{ testResult.weak_areas?.join(', ') || '无' }}</p>
        <p><strong>推荐路径:</strong> {{ testResult.learning_path?.title }}</p>
        <p><strong>学习周期:</strong> {{ testResult.learning_path?.duration_weeks }}周</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import request from '@/utils/request'

const router = useRouter()
const sessionId = ref(6) // 默认使用会话6
const testResult = ref(null)

const mockReport = ref({
  professional_knowledge: 85.0,
  skill_matching: 80.0,
  language_expression: 55.0,
  logical_thinking: 75.0,
  innovation_ability: 45.0,
  stress_resistance: 70.0,
  overall_score: 68.3
})

const viewLearningPath = () => {
  // 跳转到学习路径页面，传递sessionId
  router.push(`/learning-path/${sessionId.value}`)
}

const downloadReport = () => {
  ElMessage.success('下载报告功能模拟')
}

const shareReport = () => {
  ElMessage.success('分享报告功能模拟')
}

const startNewInterview = () => {
  ElMessage.success('重新面试功能模拟')
}

const testSession = async (id) => {
  try {
    const response = await request.get(`/api/v1/learning-paths/recommendations/${id}`)
    if (response.success) {
      testResult.value = response.data
      ElMessage.success(`会话${id}学习路径推荐获取成功`)
    }
  } catch (error) {
    console.error('获取学习路径推荐失败:', error)
    ElMessage.error('获取学习路径推荐失败')
  }
}

onMounted(() => {
  // 自动测试当前会话
  testSession(sessionId.value)
})
</script>

<style scoped>
.test-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.mock-report {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-top: 15px;
}

.scores {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  margin-bottom: 20px;
}

.score-item {
  display: flex;
  justify-content: space-between;
  padding: 8px;
  background: white;
  border-radius: 4px;
}

.overall-score {
  text-align: center;
  margin: 20px 0;
  padding: 15px;
  background: #e3f2fd;
  border-radius: 8px;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 20px;
}

.session-tests {
  display: flex;
  gap: 20px;
}

.session-test {
  flex: 1;
  padding: 15px;
  background: #f5f5f5;
  border-radius: 8px;
  text-align: center;
}

.test-result {
  margin-top: 20px;
  padding: 20px;
  background: #e8f5e8;
  border-radius: 8px;
}

.result-content p {
  margin: 8px 0;
}
</style>
