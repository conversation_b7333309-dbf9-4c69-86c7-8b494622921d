/**
 * 媒体资源管理服务
 * 管理演示页面的图片、动画和视觉资源
 */

class MediaService {
  constructor() {
    this.baseImagePath = '/demo/images'
    this.baseVideoPath = '/demo/videos'
    this.baseAnimationPath = '/demo/animations'
  }

  /**
   * 获取功能演示卡片的动态图标和图片
   */
  getFeatureMedia() {
    return {
      'multimodal-input': {
        icon: '🎤',
        image: `${this.baseImagePath}/multimodal-input.jpg`,
        animation: `${this.baseAnimationPath}/voice-wave.gif`,
        screenshot: `${this.baseImagePath}/screenshots/multimodal-interface.png`,
        description: '多模态输入界面展示'
      },
      'ai-analysis': {
        icon: '🧠',
        image: `${this.baseImagePath}/ai-analysis.jpg`,
        animation: `${this.baseAnimationPath}/brain-thinking.gif`,
        screenshot: `${this.baseImagePath}/screenshots/ai-analysis-dashboard.png`,
        description: 'AI分析处理界面'
      },
      'comprehensive-report': {
        icon: '📊',
        image: `${this.baseImagePath}/report-generation.jpg`,
        animation: `${this.baseAnimationPath}/chart-building.gif`,
        screenshot: `${this.baseImagePath}/screenshots/report-interface.png`,
        description: '综合报告生成界面'
      },
      'real-time-feedback': {
        icon: '⚡',
        image: `${this.baseImagePath}/real-time-feedback.jpg`,
        animation: `${this.baseAnimationPath}/pulse-feedback.gif`,
        screenshot: `${this.baseImagePath}/screenshots/feedback-panel.png`,
        description: '实时反馈界面'
      },
      'learning-path': {
        icon: '🎯',
        image: `${this.baseImagePath}/learning-path.jpg`,
        animation: `${this.baseAnimationPath}/path-progress.gif`,
        screenshot: `${this.baseImagePath}/screenshots/learning-dashboard.png`,
        description: '学习路径规划界面'
      },
      'ai-interaction': {
        icon: '🤖',
        image: `${this.baseImagePath}/ai-interaction.jpg`,
        animation: `${this.baseAnimationPath}/robot-chat.gif`,
        screenshot: `${this.baseImagePath}/screenshots/ai-chat-interface.png`,
        description: 'AI交互对话界面'
      }
    }
  }

  /**
   * 获取视频相关的媒体资源
   */
  getVideoMedia() {
    return {
      thumbnails: {
        main: `${this.baseImagePath}/video-thumbnails/main-demo-thumb.jpg`,
        features: `${this.baseImagePath}/video-thumbnails/features-demo-thumb.jpg`,
        tutorial: `${this.baseImagePath}/video-thumbnails/tutorial-thumb.jpg`,
        architecture: `${this.baseImagePath}/video-thumbnails/architecture-thumb.jpg`
      },
      posters: {
        main: `${this.baseImagePath}/video-posters/main-demo-poster.jpg`,
        loading: `${this.baseImagePath}/video-posters/loading-poster.jpg`
      },
      previews: {
        main: `${this.baseVideoPath}/previews/main-demo-preview.mp4`,
        features: `${this.baseVideoPath}/previews/features-preview.mp4`
      }
    }
  }

  /**
   * 获取技术架构相关的图表和图片
   */
  getArchitectureMedia() {
    return {
      diagrams: {
        overview: `${this.baseImagePath}/architecture/system-overview.svg`,
        frontend: `${this.baseImagePath}/architecture/frontend-arch.svg`,
        backend: `${this.baseImagePath}/architecture/backend-arch.svg`,
        ai: `${this.baseImagePath}/architecture/ai-pipeline.svg`,
        data: `${this.baseImagePath}/architecture/data-flow.svg`
      },
      animations: {
        dataFlow: `${this.baseAnimationPath}/data-flow.gif`,
        aiProcessing: `${this.baseAnimationPath}/ai-processing.gif`,
        systemInteraction: `${this.baseAnimationPath}/system-interaction.gif`
      },
      icons: {
        vue: `${this.baseImagePath}/tech-icons/vue.svg`,
        python: `${this.baseImagePath}/tech-icons/python.svg`,
        fastapi: `${this.baseImagePath}/tech-icons/fastapi.svg`,
        spark: `${this.baseImagePath}/tech-icons/spark.svg`,
        postgresql: `${this.baseImagePath}/tech-icons/postgresql.svg`,
        redis: `${this.baseImagePath}/tech-icons/redis.svg`
      }
    }
  }

  /**
   * 获取背景装饰元素
   */
  getBackgroundElements() {
    return {
      patterns: {
        grid: `${this.baseImagePath}/patterns/grid-pattern.svg`,
        dots: `${this.baseImagePath}/patterns/dots-pattern.svg`,
        waves: `${this.baseImagePath}/patterns/waves-pattern.svg`,
        circuit: `${this.baseImagePath}/patterns/circuit-pattern.svg`
      },
      particles: {
        floating: `${this.baseAnimationPath}/floating-particles.gif`,
        tech: `${this.baseAnimationPath}/tech-particles.gif`,
        data: `${this.baseAnimationPath}/data-particles.gif`
      },
      decorations: {
        glow: `${this.baseImagePath}/decorations/glow-effect.png`,
        gradient: `${this.baseImagePath}/decorations/gradient-overlay.png`,
        light: `${this.baseImagePath}/decorations/light-rays.png`
      }
    }
  }

  /**
   * 获取交互体验相关的媒体
   */
  getInteractiveMedia() {
    return {
      screenshots: {
        interview: `${this.baseImagePath}/screenshots/interview-process.png`,
        analysis: `${this.baseImagePath}/screenshots/analysis-results.png`,
        report: `${this.baseImagePath}/screenshots/final-report.png`,
        dashboard: `${this.baseImagePath}/screenshots/user-dashboard.png`
      },
      animations: {
        typing: `${this.baseAnimationPath}/typing-animation.gif`,
        loading: `${this.baseAnimationPath}/loading-spinner.gif`,
        progress: `${this.baseAnimationPath}/progress-bar.gif`,
        success: `${this.baseAnimationPath}/success-checkmark.gif`
      },
      mockups: {
        mobile: `${this.baseImagePath}/mockups/mobile-interface.png`,
        tablet: `${this.baseImagePath}/mockups/tablet-interface.png`,
        desktop: `${this.baseImagePath}/mockups/desktop-interface.png`
      }
    }
  }

  /**
   * 创建占位符图片URL（用于开发阶段）
   */
  createPlaceholder(width = 400, height = 300, text = 'Demo Image', bgColor = '667eea', textColor = 'ffffff') {
    return `https://via.placeholder.com/${width}x${height}/${bgColor}/${textColor}?text=${encodeURIComponent(text)}`
  }

  /**
   * 创建Unsplash图片URL（用于高质量占位符）
   */
  createUnsplashImage(keywords, width = 400, height = 300) {
    return `https://source.unsplash.com/${width}x${height}/?${keywords}`
  }

  /**
   * 获取所有媒体资源的占位符版本（用于开发）
   */
  getPlaceholderMedia() {
    return {
      features: {
        'multimodal-input': {
          image: this.createUnsplashImage('microphone,technology', 400, 300),
          screenshot: this.createPlaceholder(800, 600, '多模态输入界面', '1890ff'),
          animation: this.createPlaceholder(200, 200, 'Voice Animation', '40a9ff')
        },
        'ai-analysis': {
          image: this.createUnsplashImage('artificial-intelligence,brain', 400, 300),
          screenshot: this.createPlaceholder(800, 600, 'AI分析界面', '52c41a'),
          animation: this.createPlaceholder(200, 200, 'AI Processing', '73d13d')
        },
        'comprehensive-report': {
          image: this.createUnsplashImage('charts,analytics,data', 400, 300),
          screenshot: this.createPlaceholder(800, 600, '报告生成界面', 'faad14'),
          animation: this.createPlaceholder(200, 200, 'Chart Building', 'ffc53d')
        },
        'real-time-feedback': {
          image: this.createUnsplashImage('feedback,real-time,dashboard', 400, 300),
          screenshot: this.createPlaceholder(800, 600, '实时反馈界面', 'ff4d4f'),
          animation: this.createPlaceholder(200, 200, 'Live Feedback', 'ff7875')
        },
        'learning-path': {
          image: this.createUnsplashImage('learning,path,education', 400, 300),
          screenshot: this.createPlaceholder(800, 600, '学习路径界面', '722ed1'),
          animation: this.createPlaceholder(200, 200, 'Path Progress', 'b37feb')
        },
        'ai-interaction': {
          image: this.createUnsplashImage('robot,chat,interaction', 400, 300),
          screenshot: this.createPlaceholder(800, 600, 'AI交互界面', '13c2c2'),
          animation: this.createPlaceholder(200, 200, 'Robot Chat', '36cfc9')
        }
      },
      videos: {
        mainThumbnail: this.createUnsplashImage('video,technology,demo', 800, 450),
        mainPoster: this.createUnsplashImage('presentation,demo,tech', 1200, 675)
      },
      architecture: {
        systemOverview: this.createPlaceholder(800, 600, '系统架构图', '667eea'),
        dataFlow: this.createPlaceholder(600, 400, '数据流程图', '764ba2'),
        aiPipeline: this.createPlaceholder(700, 500, 'AI处理流程', 'f093fb')
      },
      backgrounds: {
        particles: this.createPlaceholder(1920, 1080, 'Particles Background', '000000', '667eea'),
        pattern: this.createPlaceholder(400, 400, 'Pattern', 'f8fafc', 'e2e8f0')
      }
    }
  }

  /**
   * 检查图片是否存在
   */
  async checkImageExists(url) {
    try {
      const response = await fetch(url, { method: 'HEAD' })
      return response.ok
    } catch {
      return false
    }
  }

  /**
   * 获取图片或返回占位符
   */
  async getImageOrPlaceholder(originalUrl, placeholderUrl) {
    const exists = await this.checkImageExists(originalUrl)
    return exists ? originalUrl : placeholderUrl
  }
}

export default new MediaService()
