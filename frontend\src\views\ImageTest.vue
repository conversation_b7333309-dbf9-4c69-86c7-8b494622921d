<template>
  <div class="image-test-page">
    <h1>图片加载测试页面</h1>
    
    <div class="test-section">
      <h2>1. Picsum 图片测试</h2>
      <div class="image-grid">
        <div class="image-item">
          <h3>Picsum 随机图片</h3>
          <LazyImage
            :src="picsumImage"
            :fallback-src="placeholderImage"
            alt="Picsum测试图片"
            :hover-zoom="true"
            class="test-image"
          />
          <p>URL: {{ picsumImage }}</p>
        </div>
        
        <div class="image-item">
          <h3>Placeholder.com 图片</h3>
          <LazyImage
            :src="placeholderImage"
            :fallback-src="localSvgImage"
            alt="Placeholder测试图片"
            :hover-zoom="true"
            class="test-image"
          />
          <p>URL: {{ placeholderImage }}</p>
        </div>
        
        <div class="image-item">
          <h3>本地SVG占位符</h3>
          <LazyImage
            :src="localSvgImage"
            alt="本地SVG测试图片"
            :hover-zoom="true"
            class="test-image"
          />
          <p>本地生成的SVG图片</p>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h2>2. MediaService 图片测试</h2>
      <div class="image-grid">
        <div class="image-item">
          <h3>AI分析功能图片</h3>
          <LazyImage
            :src="mediaImages.aiAnalysis"
            :fallback-src="placeholderImage"
            alt="AI分析功能"
            :hover-zoom="true"
            class="test-image"
          />
        </div>
        
        <div class="image-item">
          <h3>多模态输入图片</h3>
          <LazyImage
            :src="mediaImages.multimodalInput"
            :fallback-src="placeholderImage"
            alt="多模态输入"
            :hover-zoom="true"
            class="test-image"
          />
        </div>
        
        <div class="image-item">
          <h3>综合报告图片</h3>
          <LazyImage
            :src="mediaImages.comprehensiveReport"
            :fallback-src="placeholderImage"
            alt="综合报告"
            :hover-zoom="true"
            class="test-image"
          />
        </div>
      </div>
    </div>

    <div class="test-section">
      <h2>3. 错误处理测试</h2>
      <div class="image-grid">
        <div class="image-item">
          <h3>无效URL测试</h3>
          <LazyImage
            src="https://invalid-url-that-should-fail.com/image.jpg"
            :fallback-src="placeholderImage"
            alt="无效URL测试"
            :hover-zoom="true"
            class="test-image"
          />
          <p>应该显示备用图片</p>
        </div>
        
        <div class="image-item">
          <h3>完全失败测试</h3>
          <LazyImage
            src="https://invalid-url-1.com/image.jpg"
            fallback-src="https://invalid-url-2.com/fallback.jpg"
            alt="完全失败测试"
            :hover-zoom="true"
            class="test-image"
          />
          <p>应该显示本地SVG占位符</p>
        </div>
      </div>
    </div>

    <div class="debug-info">
      <h2>调试信息</h2>
      <p>请打开浏览器开发者工具查看控制台输出</p>
      <el-button @click="testMediaService">测试 MediaService</el-button>
      <el-button @click="clearConsole">清空控制台</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import LazyImage from '../components/Demo/LazyImage.vue'
import MediaService from '../services/mediaService.js'

// 测试图片URLs
const picsumImage = ref('https://picsum.photos/400/300?random=1')
const placeholderImage = ref('https://via.placeholder.com/400x300/667eea/ffffff?text=测试图片')
const localSvgImage = ref('')

// MediaService图片
const mediaImages = ref({
  aiAnalysis: '',
  multimodalInput: '',
  comprehensiveReport: ''
})

// 生成本地SVG图片
const generateLocalSvg = () => {
  const svg = `
    <svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#667eea88;stop-opacity:1" />
        </linearGradient>
      </defs>
      <rect width="100%" height="100%" fill="url(#grad)"/>
      <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="16" 
            fill="white" text-anchor="middle" dy=".3em">本地SVG测试图片</text>
    </svg>
  `
  return `data:image/svg+xml;base64,${btoa(unescape(encodeURIComponent(svg)))}`
}

// 测试MediaService
const testMediaService = () => {
  console.log('=== MediaService 测试 ===')
  
  const placeholderMedia = MediaService.getPlaceholderMedia()
  console.log('PlaceholderMedia:', placeholderMedia)
  
  const testImage = MediaService.createUnsplashImage('technology,computer', 400, 300)
  console.log('测试图片URL:', testImage)
  
  const testPlaceholder = MediaService.createPlaceholder(400, 300, '测试占位符', '1890ff')
  console.log('测试占位符URL:', testPlaceholder)
}

// 清空控制台
const clearConsole = () => {
  console.clear()
  console.log('控制台已清空')
}

onMounted(() => {
  console.log('=== 图片加载测试页面初始化 ===')
  
  // 生成本地SVG
  localSvgImage.value = generateLocalSvg()
  
  // 获取MediaService图片
  const placeholderMedia = MediaService.getPlaceholderMedia()
  if (placeholderMedia && placeholderMedia.features) {
    mediaImages.value.aiAnalysis = placeholderMedia.features['ai-analysis']?.image || ''
    mediaImages.value.multimodalInput = placeholderMedia.features['multimodal-input']?.image || ''
    mediaImages.value.comprehensiveReport = placeholderMedia.features['comprehensive-report']?.image || ''
  }
  
  console.log('MediaService 图片URLs:', mediaImages.value)
})
</script>

<style scoped>
.image-test-page {
  padding: var(--spacing-lg);
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  border: 1px solid var(--el-border-color-light);
  border-radius: var(--border-radius-lg);
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-md);
}

.image-item {
  text-align: center;
}

.image-item h3 {
  margin-bottom: var(--spacing-sm);
  color: var(--el-text-color-primary);
}

.test-image {
  width: 100%;
  height: 200px;
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-sm);
}

.image-item p {
  font-size: 0.8rem;
  color: var(--el-text-color-secondary);
  word-break: break-all;
}

.debug-info {
  padding: var(--spacing-lg);
  background: var(--el-color-info-light-9);
  border-radius: var(--border-radius-lg);
  text-align: center;
}

.debug-info .el-button {
  margin: 0 var(--spacing-sm);
}
</style>
