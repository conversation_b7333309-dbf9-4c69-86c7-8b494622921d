# 面试题库数据
QUESTION_BANK = {
    "人工智能": {
        "技术岗": [
            {
                "question": "请解释一下机器学习中监督学习和无监督学习的区别，并举例说明它们的应用场景。",
                "difficulty": "medium",
                "type": "technical",
                "keywords": ["监督学习", "无监督学习", "机器学习", "分类", "聚类"],
                "expected_points": [
                    "监督学习需要标注数据，无监督学习不需要",
                    "监督学习用于分类和回归，无监督学习用于聚类和降维",
                    "举出具体应用例子"
                ]
            },
            {
                "question": "深度学习中的梯度消失问题是什么？有哪些解决方法？",
                "difficulty": "hard",
                "type": "technical", 
                "keywords": ["梯度消失", "深度学习", "反向传播", "激活函数"],
                "expected_points": [
                    "解释梯度消失的原因",
                    "提到ReLU激活函数、残差连接、批归一化等解决方案",
                    "理解深度网络训练的挑战"
                ]
            },
            {
                "question": "如果让你设计一个推荐系统，你会考虑哪些因素？请描述你的设计思路。",
                "difficulty": "medium",
                "type": "scenario",
                "keywords": ["推荐系统", "协同过滤", "内容过滤", "冷启动"],
                "expected_points": [
                    "用户行为数据收集",
                    "推荐算法选择（协同过滤、内容过滤等）",
                    "冷启动问题解决",
                    "系统架构设计"
                ]
            }
        ],
        "运维测试岗": [
            {
                "question": "在AI模型部署过程中，你如何确保模型的性能和稳定性？",
                "difficulty": "medium",
                "type": "technical",
                "keywords": ["模型部署", "性能监控", "A/B测试", "模型版本管理"],
                "expected_points": [
                    "模型性能监控指标",
                    "A/B测试验证",
                    "模型版本管理",
                    "异常检测和回滚机制"
                ]
            },
            {
                "question": "AI系统在生产环境中出现预测准确率下降，你会如何排查和解决？",
                "difficulty": "hard",
                "type": "scenario",
                "keywords": ["模型漂移", "数据质量", "特征工程", "模型重训练"],
                "expected_points": [
                    "检查数据质量和分布变化",
                    "分析模型漂移现象",
                    "考虑重新训练或微调模型",
                    "建立监控和预警机制"
                ]
            }
        ],
        "产品岗": [
            {
                "question": "如何向非技术背景的客户解释AI产品的价值和局限性？",
                "difficulty": "medium",
                "type": "behavioral",
                "keywords": ["产品沟通", "AI解释", "客户教育", "价值传递"],
                "expected_points": [
                    "用通俗易懂的语言解释AI概念",
                    "强调AI带来的业务价值",
                    "诚实说明AI的局限性",
                    "提供具体的应用案例"
                ]
            }
        ]
    },
    "大数据": {
        "技术岗": [
            {
                "question": "请解释Hadoop生态系统的核心组件及其作用。",
                "difficulty": "medium",
                "type": "technical",
                "keywords": ["Hadoop", "HDFS", "MapReduce", "YARN", "Hive", "HBase"],
                "expected_points": [
                    "HDFS分布式文件系统",
                    "MapReduce计算框架",
                    "YARN资源管理",
                    "Hive数据仓库工具",
                    "HBase NoSQL数据库"
                ]
            },
            {
                "question": "在处理大规模数据时，如何选择合适的存储和计算方案？",
                "difficulty": "hard",
                "type": "scenario",
                "keywords": ["数据存储", "计算框架", "性能优化", "成本控制"],
                "expected_points": [
                    "根据数据特征选择存储方案",
                    "考虑计算复杂度和实时性要求",
                    "平衡性能和成本",
                    "可扩展性考虑"
                ]
            }
        ],
        "运维测试岗": [
            {
                "question": "大数据集群的监控指标有哪些？如何设计监控体系？",
                "difficulty": "medium",
                "type": "technical",
                "keywords": ["集群监控", "性能指标", "告警机制", "运维自动化"],
                "expected_points": [
                    "CPU、内存、磁盘、网络等基础指标",
                    "作业执行状态和性能指标",
                    "数据质量监控",
                    "告警和自动化处理机制"
                ]
            }
        ],
        "产品岗": [
            {
                "question": "如何设计一个数据可视化产品来帮助业务人员理解复杂的数据分析结果？",
                "difficulty": "medium",
                "type": "scenario",
                "keywords": ["数据可视化", "用户体验", "业务理解", "交互设计"],
                "expected_points": [
                    "了解业务人员的需求和痛点",
                    "选择合适的图表类型",
                    "设计直观的交互方式",
                    "提供数据钻取和筛选功能"
                ]
            }
        ]
    },
    "物联网": {
        "技术岗": [
            {
                "question": "请描述物联网系统的典型架构，包括感知层、网络层和应用层的功能。",
                "difficulty": "medium",
                "type": "technical",
                "keywords": ["物联网架构", "感知层", "网络层", "应用层", "传感器"],
                "expected_points": [
                    "感知层：传感器数据采集",
                    "网络层：数据传输和通信协议",
                    "应用层：数据处理和业务逻辑",
                    "各层之间的交互关系"
                ]
            },
            {
                "question": "在设计物联网设备时，如何平衡功耗、性能和成本？",
                "difficulty": "hard",
                "type": "scenario",
                "keywords": ["功耗优化", "性能平衡", "成本控制", "硬件设计"],
                "expected_points": [
                    "低功耗设计策略",
                    "性能需求分析",
                    "成本效益评估",
                    "技术选型权衡"
                ]
            }
        ],
        "运维测试岗": [
            {
                "question": "物联网设备大规模部署后，如何进行远程监控和维护？",
                "difficulty": "medium",
                "type": "technical",
                "keywords": ["远程监控", "设备管理", "OTA升级", "故障诊断"],
                "expected_points": [
                    "设备状态监控系统",
                    "远程诊断和故障排除",
                    "OTA固件升级机制",
                    "设备生命周期管理"
                ]
            }
        ],
        "产品岗": [
            {
                "question": "如何设计一个智能家居产品的用户体验，让技术小白也能轻松使用？",
                "difficulty": "medium",
                "type": "behavioral",
                "keywords": ["用户体验", "智能家居", "易用性", "产品设计"],
                "expected_points": [
                    "简化操作流程",
                    "直观的界面设计",
                    "智能化自动配置",
                    "完善的用户引导和帮助"
                ]
            }
        ]
    }
}

def get_questions_by_domain_position(domain: str, position: str) -> list:
    """根据领域和岗位获取问题列表"""
    return QUESTION_BANK.get(domain, {}).get(position, [])

def get_all_domains() -> list:
    """获取所有技术领域"""
    return list(QUESTION_BANK.keys())

def get_positions_by_domain(domain: str) -> list:
    """根据领域获取岗位列表"""
    return list(QUESTION_BANK.get(domain, {}).keys())
