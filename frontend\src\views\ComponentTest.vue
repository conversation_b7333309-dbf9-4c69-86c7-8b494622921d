<template>
  <div class="component-test">
    <h1>视觉组件测试页面</h1>
    
    <!-- 粒子背景测试 -->
    <section class="test-section">
      <h2>粒子背景组件</h2>
      <div class="particle-demo" style="height: 300px; position: relative; background: #001529; border-radius: 8px;">
        <ParticleBackground 
          :particle-count="50"
          particle-color="#1890ff"
          :particle-size="2"
          :speed="1"
          :opacity="0.6"
        />
        <div style="position: relative; z-index: 10; color: white; padding: 40px; text-align: center;">
          <h3>粒子背景效果</h3>
          <p>动态粒子动画背景</p>
        </div>
      </div>
    </section>

    <!-- 懒加载图片测试 -->
    <section class="test-section">
      <h2>懒加载图片组件</h2>
      <div class="image-grid">
        <div class="image-item">
          <h4>基础懒加载</h4>
          <LazyImage
            src="https://source.unsplash.com/400x300/?technology"
            fallback-src="https://via.placeholder.com/400x300/1890ff/ffffff?text=Technology"
            alt="技术图片"
            class="test-image"
          />
        </div>
        
        <div class="image-item">
          <h4>悬停缩放效果</h4>
          <LazyImage
            src="https://source.unsplash.com/400x300/?ai"
            fallback-src="https://via.placeholder.com/400x300/52c41a/ffffff?text=AI"
            alt="AI图片"
            :hover-zoom="true"
            class="test-image"
          />
        </div>
        
        <div class="image-item">
          <h4>带覆盖层</h4>
          <LazyImage
            src="https://source.unsplash.com/400x300/?interface"
            fallback-src="https://via.placeholder.com/400x300/faad14/ffffff?text=Interface"
            alt="界面图片"
            :hover-zoom="true"
            :overlay="true"
            overlay-title="界面设计"
            overlay-description="现代化的用户界面设计"
            class="test-image"
          />
        </div>
        
        <div class="image-item">
          <h4>视差滚动效果</h4>
          <LazyImage
            src="https://source.unsplash.com/400x300/?dashboard"
            fallback-src="https://via.placeholder.com/400x300/722ed1/ffffff?text=Dashboard"
            alt="仪表板图片"
            :parallax="true"
            :parallax-speed="0.5"
            class="test-image"
          />
        </div>
      </div>
    </section>

    <!-- 视差滚动部分测试 -->
    <section class="test-section">
      <h2>视差滚动组件</h2>
      <ParallaxSection :speed="0.5" class="parallax-demo">
        <template #background>
          <div class="parallax-bg"></div>
        </template>
        
        <template #decoration>
          <div class="parallax-decoration"></div>
        </template>
        
        <div class="parallax-content">
          <h3>视差滚动效果</h3>
          <p>背景、内容和装饰层以不同速度移动</p>
          <p>向下滚动页面查看效果</p>
        </div>
      </ParallaxSection>
    </section>

    <!-- 媒体服务测试 -->
    <section class="test-section">
      <h2>媒体服务测试</h2>
      <div class="media-grid">
        <div class="media-item" v-for="(item, index) in mediaItems" :key="index">
          <h4>{{ item.title }}</h4>
          <LazyImage
            :src="item.src"
            :fallback-src="item.fallback"
            :alt="item.alt"
            :hover-zoom="true"
            class="media-image"
          />
          <p>{{ item.description }}</p>
        </div>
      </div>
    </section>

    <!-- 滚动提示 -->
    <div class="scroll-indicator">
      <p>继续滚动查看视差效果</p>
      <div class="scroll-arrow">↓</div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import ParticleBackground from '../components/Demo/ParticleBackground.vue'
import LazyImage from '../components/Demo/LazyImage.vue'
import ParallaxSection from '../components/Demo/ParallaxSection.vue'
import MediaService from '../services/mediaService.js'

const mediaItems = ref([])

onMounted(() => {
  // 测试媒体服务
  mediaItems.value = [
    {
      title: '功能演示',
      src: MediaService.createUnsplashImage('technology,demo', 300, 200),
      fallback: MediaService.createPlaceholder(300, 200, '功能演示', '1890ff'),
      alt: '功能演示图片',
      description: '使用Unsplash API生成的高质量图片'
    },
    {
      title: '系统架构',
      src: MediaService.createUnsplashImage('architecture,system', 300, 200),
      fallback: MediaService.createPlaceholder(300, 200, '系统架构', '52c41a'),
      alt: '系统架构图片',
      description: '系统架构相关的图片展示'
    },
    {
      title: '数据分析',
      src: MediaService.createUnsplashImage('analytics,data', 300, 200),
      fallback: MediaService.createPlaceholder(300, 200, '数据分析', 'faad14'),
      alt: '数据分析图片',
      description: '数据分析和可视化图片'
    },
    {
      title: '用户界面',
      src: MediaService.createUnsplashImage('interface,ui', 300, 200),
      fallback: MediaService.createPlaceholder(300, 200, '用户界面', '722ed1'),
      alt: '用户界面图片',
      description: '用户界面设计展示'
    }
  ]
})
</script>

<style scoped>
.component-test {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.test-section {
  margin: 40px 0;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: white;
}

.test-section h2 {
  margin-bottom: 20px;
  color: #1890ff;
  border-bottom: 2px solid #1890ff;
  padding-bottom: 10px;
}

.particle-demo {
  overflow: hidden;
}

.image-grid,
.media-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.image-item,
.media-item {
  text-align: center;
  padding: 15px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  background: #fafafa;
}

.test-image,
.media-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 6px;
}

.parallax-demo {
  height: 400px;
  position: relative;
  overflow: hidden;
  border-radius: 8px;
}

.parallax-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  opacity: 0.8;
}

.parallax-decoration {
  background: 
    radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
}

.parallax-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: white;
  text-align: center;
  padding: 40px;
}

.parallax-content h3 {
  font-size: 2rem;
  margin-bottom: 20px;
}

.parallax-content p {
  font-size: 1.1rem;
  margin: 10px 0;
  opacity: 0.9;
}

.scroll-indicator {
  text-align: center;
  margin: 60px 0;
  color: #666;
}

.scroll-arrow {
  font-size: 2rem;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@media (max-width: 768px) {
  .image-grid,
  .media-grid {
    grid-template-columns: 1fr;
  }
  
  .parallax-content h3 {
    font-size: 1.5rem;
  }
  
  .parallax-content p {
    font-size: 1rem;
  }
}
</style>
