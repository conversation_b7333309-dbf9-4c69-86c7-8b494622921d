<template>
  <div 
    class="parallax-section"
    :class="{ 'in-view': isInView }"
    ref="sectionRef"
  >
    <!-- 背景层 -->
    <div 
      class="parallax-background"
      :style="backgroundStyle"
      ref="backgroundRef"
    >
      <slot name="background"></slot>
    </div>

    <!-- 内容层 -->
    <div 
      class="parallax-content"
      :style="contentStyle"
      ref="contentRef"
    >
      <slot></slot>
    </div>

    <!-- 装饰层 -->
    <div 
      class="parallax-decoration"
      :style="decorationStyle"
      ref="decorationRef"
    >
      <slot name="decoration"></slot>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'

const props = defineProps({
  speed: {
    type: Number,
    default: 0.5
  },
  backgroundSpeed: {
    type: Number,
    default: 0.3
  },
  contentSpeed: {
    type: Number,
    default: 0.1
  },
  decorationSpeed: {
    type: Number,
    default: 0.7
  },
  offset: {
    type: Number,
    default: 0
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const sectionRef = ref(null)
const backgroundRef = ref(null)
const contentRef = ref(null)
const decorationRef = ref(null)

const scrollY = ref(0)
const elementTop = ref(0)
const elementHeight = ref(0)
const windowHeight = ref(0)
const isInView = ref(false)

const backgroundStyle = computed(() => {
  if (props.disabled) return {}
  
  const translateY = (scrollY.value - elementTop.value) * props.backgroundSpeed
  return {
    transform: `translateY(${translateY + props.offset}px)`
  }
})

const contentStyle = computed(() => {
  if (props.disabled) return {}
  
  const translateY = (scrollY.value - elementTop.value) * props.contentSpeed
  return {
    transform: `translateY(${translateY}px)`
  }
})

const decorationStyle = computed(() => {
  if (props.disabled) return {}
  
  const translateY = (scrollY.value - elementTop.value) * props.decorationSpeed
  return {
    transform: `translateY(${translateY}px)`
  }
})

const updateScrollPosition = () => {
  scrollY.value = window.pageYOffset
  
  // 检查元素是否在视口中
  if (sectionRef.value) {
    const rect = sectionRef.value.getBoundingClientRect()
    isInView.value = rect.bottom >= 0 && rect.top <= windowHeight.value
  }
}

const updateElementPosition = () => {
  if (sectionRef.value) {
    const rect = sectionRef.value.getBoundingClientRect()
    elementTop.value = rect.top + window.pageYOffset
    elementHeight.value = rect.height
  }
  windowHeight.value = window.innerHeight
}

const handleScroll = () => {
  if (props.disabled) return
  updateScrollPosition()
}

const handleResize = () => {
  updateElementPosition()
  updateScrollPosition()
}

onMounted(() => {
  updateElementPosition()
  updateScrollPosition()
  
  window.addEventListener('scroll', handleScroll, { passive: true })
  window.addEventListener('resize', handleResize, { passive: true })
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.parallax-section {
  position: relative;
  overflow: hidden;
  will-change: transform;
}

.parallax-background,
.parallax-content,
.parallax-decoration {
  will-change: transform;
}

.parallax-background {
  position: absolute;
  top: -20%;
  left: 0;
  width: 100%;
  height: 120%;
  z-index: 1;
}

.parallax-content {
  position: relative;
  z-index: 3;
}

.parallax-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  pointer-events: none;
}

/* 视口内动画 */
.parallax-section.in-view .parallax-content {
  animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 移动端禁用视差效果 */
@media (max-width: 768px) {
  .parallax-background,
  .parallax-content,
  .parallax-decoration {
    transform: none !important;
  }
}

/* 减少动画的用户偏好 */
@media (prefers-reduced-motion: reduce) {
  .parallax-background,
  .parallax-content,
  .parallax-decoration {
    transform: none !important;
  }
  
  .parallax-section.in-view .parallax-content {
    animation: none;
  }
}
</style>
