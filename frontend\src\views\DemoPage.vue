<template>
  <div class="demo-page">
    <!-- 演示页面头部 -->
    <div class="demo-header">
      <!-- 粒子背景 -->
      <ParticleBackground
        :particle-count="60"
        particle-color="#1890ff"
        :particle-size="3"
        :speed="0.8"
        :opacity="0.4"
      />

      <div class="header-content">
        <h1 class="demo-title">
          <el-icon><VideoPlay /></el-icon>
          系统演示
        </h1>
        <p class="demo-subtitle">体验多模态智能面试评测系统的完整功能</p>

        <!-- 演示统计信息 -->
        <div class="demo-stats" v-if="demoStats.totalVideos">
          <div class="stat-item">
            <span class="stat-number">{{ demoStats.totalVideos }}</span>
            <span class="stat-label">演示视频</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ demoStats.totalFeatures }}</span>
            <span class="stat-label">核心功能</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ demoStats.totalSteps }}</span>
            <span class="stat-label">体验步骤</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ Math.round(demoStats.estimatedTotalTime / 60) || 30 }}分钟</span>
            <span class="stat-label">预计时长</span>
          </div>
        </div>

        <!-- 导航标签 -->
        <el-tabs v-model="activeTab" class="demo-tabs" @tab-click="handleTabClick">
          <el-tab-pane label="功能演示" name="features">
            <el-icon><Star /></el-icon>
          </el-tab-pane>
          <el-tab-pane label="视频教程" name="video">
            <el-icon><VideoCamera /></el-icon>
          </el-tab-pane>
          <el-tab-pane label="交互体验" name="interactive">
            <el-icon><Mouse /></el-icon>
          </el-tab-pane>
          <el-tab-pane label="技术架构" name="architecture">
            <el-icon><Setting /></el-icon>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 演示内容区域 -->
    <div class="demo-content">
      <!-- 功能演示 -->
      <div v-show="activeTab === 'features'" class="features-demo">
        <div class="features-grid">
          <div
            v-for="(feature, index) in features"
            :key="index"
            class="feature-card animate-fade-in-up"
            :style="{ animationDelay: `${index * 0.1}s` }"
            @click="showFeatureDemo(feature)"
          >
            <!-- 功能图片展示 -->
            <div class="feature-media">
              <LazyImage
                :src="getFeatureImage(feature.key)"
                :fallback-src="getFeaturePlaceholder(feature.key)"
                :alt="feature.title + '功能展示'"
                :hover-zoom="true"
                :overlay="true"
                :overlay-title="feature.title"
                :overlay-description="feature.description"
                class="feature-image"
              />

              <!-- 功能图标覆盖 -->
              <div class="feature-icon-overlay">
                <div class="feature-icon">
                  <el-icon :size="40">
                    <component :is="feature.icon" />
                  </el-icon>
                </div>
              </div>
            </div>

            <!-- 功能信息 -->
            <div class="feature-info">
              <h3>{{ feature.title }}</h3>
              <p>{{ feature.description }}</p>

              <!-- 功能标签 -->
              <div class="feature-tags" v-if="feature.tags">
                <el-tag
                  v-for="tag in feature.tags"
                  :key="tag"
                  size="small"
                  class="feature-tag"
                >
                  {{ tag }}
                </el-tag>
              </div>

              <div class="feature-actions">
                <el-button type="primary" size="small" @click.stop="playFeatureDemo(feature)">
                  <el-icon><VideoPlay /></el-icon>
                  观看演示
                </el-button>
                <el-button size="small" @click.stop="tryFeature(feature)">
                  <el-icon><Operation /></el-icon>
                  立即体验
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 视频教程 -->
      <div v-show="activeTab === 'video'" class="video-demo">
        <!-- 主演示视频区域 -->
        <ParallaxSection class="main-video-section" :speed="0.3">
          <template #background>
            <div class="video-background-pattern"></div>
          </template>

          <div class="video-player-container">
            <div class="video-player">
              <!-- 视频缩略图 -->
              <LazyImage
                :src="getVideoThumbnail('main')"
                :fallback-src="getVideoPlaceholder('main')"
                alt="主演示视频缩略图"
                :hover-zoom="true"
                :show-progress="true"
                class="video-thumbnail"
                @click="openVideoDialog"
              />

              <!-- 播放按钮覆盖层 -->
              <div class="video-play-overlay" @click="openVideoDialog">
                <div class="play-button-large">
                  <el-icon :size="60"><VideoPlay /></el-icon>
                </div>
                <div class="video-duration-badge">{{ mainVideoDetails.duration }}</div>
              </div>

              <div class="video-info-overlay">
                <h3>{{ mainVideoDetails.title }}</h3>
                <p>{{ mainVideoDetails.description }}</p>

                <!-- 视频信息卡片 -->
                <div class="video-info-card">
                  <div class="video-stats">
                    <span class="stat-item">
                      <el-icon><Clock /></el-icon>
                      {{ mainVideoDetails.duration }}
                    </span>
                    <span class="stat-item">
                      <el-icon><View /></el-icon>
                      {{ mainVideoDetails.views }}次观看
                    </span>
                    <span class="stat-item">
                      <el-icon><Star /></el-icon>
                      {{ mainVideoDetails.rating }}分
                    </span>
                    <span class="stat-item">
                      <el-icon><Calendar /></el-icon>
                      {{ mainVideoDetails.publishDate }}
                    </span>
                  </div>

                  <!-- 技术特性标签 -->
                  <div class="tech-features" v-if="mainVideoDetails.features">
                    <el-tag
                      v-for="feature in mainVideoDetails.features.filter(f => f.highlight)"
                      :key="feature.name"
                      type="primary"
                      size="small"
                      class="feature-tag"
                    >
                      {{ feature.name }}
                    </el-tag>
                  </div>
                </div>

                <div class="video-actions">
                  <el-button type="primary" size="large" @click="playMainVideo">
                    <el-icon><VideoPlay /></el-icon>
                    播放完整演示
                  </el-button>
                  <el-button size="large" @click="showChapterNavigation = !showChapterNavigation">
                    <el-icon><List /></el-icon>
                    章节导航
                  </el-button>
                </div>
              </div>
            </div>

            <!-- 章节导航面板 -->
            <div v-if="showChapterNavigation" class="chapter-navigation">
              <h4>
                <el-icon><List /></el-icon>
                视频章节 ({{ videoChapters.length }}个)
              </h4>
              <div class="chapters-list">
                <div
                  v-for="(chapter, index) in videoChapters"
                  :key="index"
                  class="chapter-item animate-fade-in-up"
                  :style="{ animationDelay: `${index * 0.1}s` }"
                  @click="jumpToChapter(index)"
                >
                  <div class="chapter-thumbnail">
                    <LazyImage
                      v-if="chapter.thumbnail"
                      :src="chapter.thumbnail"
                      :fallback-src="getChapterPlaceholder(index)"
                      :alt="chapter.title"
                      :hover-zoom="true"
                      class="chapter-thumb-image"
                    />
                    <div v-else class="chapter-placeholder">
                      <el-icon><VideoPlay /></el-icon>
                    </div>

                    <!-- 播放指示器 -->
                    <div class="chapter-play-indicator">
                      <el-icon><VideoPlay /></el-icon>
                    </div>
                  </div>

                  <div class="chapter-info">
                    <div class="chapter-header">
                      <span class="chapter-time">{{ chapter.time }}</span>
                      <span class="chapter-duration">{{ chapter.duration }}</span>
                    </div>
                    <h5>{{ chapter.title }}</h5>
                    <p>{{ chapter.description }}</p>
                    <div class="chapter-keypoints" v-if="chapter.keyPoints">
                      <el-tag
                        v-for="point in chapter.keyPoints"
                        :key="point"
                        size="mini"
                        type="info"
                        class="chapter-tag"
                      >
                        {{ point }}
                      </el-tag>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 技术规格展示 -->
          <div class="tech-specs-panel" v-if="mainVideoDetails.technicalSpecs">
            <h4>
              <el-icon><Setting /></el-icon>
              技术规格
            </h4>
            <div class="specs-grid">
              <div class="spec-item" v-for="(value, key) in mainVideoDetails.technicalSpecs" :key="key">
                <span class="spec-label">{{ getSpecLabel(key) }}</span>
                <span class="spec-value">{{ value }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 演示场景选择 -->
        <div class="demo-scenarios-section">
          <h3>
            <el-icon><Guide /></el-icon>
            演示场景
          </h3>
          <div class="scenarios-grid">
            <div
              v-for="(scenario, key) in demoScenarios"
              :key="key"
              class="scenario-card"
              @click="startDemoScenario(key)"
            >
              <div class="scenario-header">
                <h4>{{ scenario.title }}</h4>
                <el-tag :type="getDifficultyType(scenario.difficulty)">
                  {{ scenario.difficulty }}
                </el-tag>
              </div>
              <p>{{ scenario.description }}</p>
              <div class="scenario-meta">
                <span class="scenario-time">
                  <el-icon><Clock /></el-icon>
                  {{ scenario.estimatedTime }}
                </span>
                <span class="scenario-steps">
                  <el-icon><List /></el-icon>
                  {{ scenario.steps.length }}个步骤
                </span>
              </div>
              <el-button type="primary" size="small" class="scenario-btn">
                开始演示
              </el-button>
            </div>
          </div>
        </div>

        <!-- 分步教程 -->
        <div class="video-tutorials">
          <h3>
            <el-icon><VideoCamera /></el-icon>
            分步教程
          </h3>
          <div class="tutorials-grid">
            <div
              v-for="(video, index) in videoTutorials"
              :key="index"
              class="video-item animate-fade-in-up"
              :style="{ animationDelay: `${index * 0.15}s` }"
              @click="playVideo(video)"
            >
              <div class="video-thumbnail">
                <LazyImage
                  :src="getVideoThumbnail(video.key || `tutorial-${index}`)"
                  :fallback-src="getTutorialPlaceholder(index)"
                  :alt="video.title + '教程视频'"
                  :hover-zoom="true"
                  class="tutorial-thumbnail"
                />

                <!-- 播放按钮覆盖层 -->
                <div class="video-play-overlay">
                  <div class="play-button">
                    <el-icon><VideoPlay /></el-icon>
                  </div>
                  <div class="video-duration-badge">{{ video.duration }}</div>
                </div>

                <!-- 难度标识 -->
                <div class="difficulty-badge" :class="getDifficultyClass(video.difficulty)">
                  {{ video.difficulty }}
                </div>
              </div>

              <div class="video-info">
                <h4>{{ video.title }}</h4>
                <p>{{ video.description }}</p>
                <div class="video-meta">
                  <span class="video-views">
                    <el-icon><View /></el-icon>
                    {{ video.views }}次观看
                  </span>
                  <span class="video-rating">
                    <el-icon><Star /></el-icon>
                    {{ video.rating }}
                  </span>
                  <div class="video-tags">
                    <el-tag
                      v-for="tag in video.tags"
                      :key="tag"
                      size="small"
                      class="video-tag"
                    >
                      {{ tag }}
                    </el-tag>
                  </div>
                </div>
                <div class="video-tags" v-if="video.tags">
                  <el-tag
                    v-for="tag in video.tags"
                    :key="tag"
                    size="mini"
                    type="info"
                  >
                    {{ tag }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 交互体验 -->
      <div v-show="activeTab === 'interactive'" class="interactive-demo">
        <!-- 产品界面截图展示 -->
        <ParallaxSection class="interface-showcase" :speed="0.2">
          <template #background>
            <div class="showcase-background"></div>
          </template>

          <div class="interface-gallery">
            <h3>
              <el-icon><Picture /></el-icon>
              产品界面展示
            </h3>
            <div class="interface-grid">
              <div
                v-for="(screenshot, index) in interfaceScreenshots"
                :key="index"
                class="interface-item animate-fade-in-up"
                :style="{ animationDelay: `${index * 0.2}s` }"
              >
                <LazyImage
                  :src="screenshot.image"
                  :fallback-src="screenshot.placeholder"
                  :alt="screenshot.title"
                  :hover-zoom="true"
                  :overlay="true"
                  :overlay-title="screenshot.title"
                  :overlay-description="screenshot.description"
                  class="interface-screenshot"
                />
                <div class="interface-info">
                  <h4>{{ screenshot.title }}</h4>
                  <p>{{ screenshot.description }}</p>
                  <div class="interface-tags">
                    <el-tag
                      v-for="tag in screenshot.tags"
                      :key="tag"
                      size="small"
                      class="interface-tag"
                    >
                      {{ tag }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ParallaxSection>

        <div class="demo-simulator">
          <h3>
            <el-icon><Mouse /></el-icon>
            模拟面试体验
          </h3>
          <p>在这里您可以体验完整的面试流程，无需注册即可开始</p>

          <div class="simulator-steps">
            <div
              v-for="(step, index) in simulatorSteps"
              :key="index"
              class="step-card animate-slide-in-left"
              :style="{ animationDelay: `${index * 0.1}s` }"
              :class="{ active: currentStep === index, completed: currentStep > index }"
              @click="goToStep(index)"
            >
              <!-- 步骤截图 -->
              <div class="step-screenshot" v-if="step.screenshot">
                <LazyImage
                  :src="step.screenshot"
                  :fallback-src="getStepPlaceholder(index)"
                  :alt="step.title + '步骤截图'"
                  class="step-image"
                />
              </div>

              <div class="step-number">{{ index + 1 }}</div>
              <div class="step-content">
                <h4>{{ step.title }}</h4>
                <p>{{ step.description }}</p>

                <!-- 步骤详细信息 -->
                <div class="step-details" v-if="step.estimatedTime || step.difficulty">
                  <div class="step-meta">
                    <span v-if="step.estimatedTime" class="step-time">
                      <el-icon><Clock /></el-icon>
                      {{ step.estimatedTime }}
                    </span>
                    <span v-if="step.difficulty" class="step-difficulty">
                      <el-tag :type="getDifficultyType(step.difficulty)" size="small">
                        {{ step.difficulty }}
                      </el-tag>
                    </span>
                  </div>
                </div>

                <!-- 交互元素提示 -->
                <div v-if="step.interactiveElements && step.interactiveElements.length > 0" class="interactive-hint">
                  <el-icon><Operation /></el-icon>
                  <span>{{ step.interactiveElements.length }}个交互元素</span>
                </div>
              </div>
              <div class="step-status">
                <el-icon v-if="currentStep > index"><Check /></el-icon>
                <el-icon v-else-if="currentStep === index"><ArrowRight /></el-icon>
              </div>
            </div>
          </div>
          
          <div class="simulator-actions">
            <el-button 
              v-if="currentStep < simulatorSteps.length - 1"
              type="primary" 
              @click="nextStep"
            >
              下一步
              <el-icon><ArrowRight /></el-icon>
            </el-button>
            <el-button 
              v-else
              type="success" 
              @click="startRealInterview"
            >
              开始真实面试
              <el-icon><VideoPlay /></el-icon>
            </el-button>
            <el-button @click="resetSimulator">重新开始</el-button>
          </div>
        </div>
      </div>

      <!-- 技术架构 -->
      <div v-show="activeTab === 'architecture'" class="architecture-demo">
        <!-- 系统架构图 -->
        <ParallaxSection class="architecture-overview" :speed="0.1">
          <template #background>
            <div class="architecture-background"></div>
          </template>

          <div class="architecture-header">
            <h3>
              <el-icon><Setting /></el-icon>
              {{ architectureData.title || '技术架构图' }}
            </h3>
            <p v-if="architectureData.description" class="arch-description">
              {{ architectureData.description }}
            </p>
          </div>

          <!-- 架构图表展示 -->
          <div class="architecture-visualization">
            <LazyImage
              :src="getArchitectureDiagram('overview')"
              :fallback-src="getArchitecturePlaceholder('overview')"
              alt="系统架构总览图"
              :hover-zoom="true"
              class="architecture-main-diagram"
            />
          </div>

          <div class="architecture-diagram">
            <div
              v-for="(layer, index) in architectureData.layers"
              :key="index"
              class="arch-layer animate-fade-in-up"
              :style="{ animationDelay: `${index * 0.2}s` }"
              :class="layer.name.toLowerCase().replace(/[^a-z]/g, '')"
            >
              <!-- 层级图标 -->
              <div class="layer-icon">
                <LazyImage
                  :src="getTechIcon(layer.name)"
                  :fallback-src="getTechIconPlaceholder(layer.name)"
                  :alt="layer.name + '技术栈图标'"
                  class="tech-icon"
                />
              </div>

              <div class="layer-content">
                <h4>{{ layer.name }}</h4>
                <p class="layer-description">{{ layer.description }}</p>
                <div class="tech-stack">
                  <div
                    v-for="tech in layer.technologies"
                    :key="tech"
                    class="tech-item"
                  >
                    <div class="tech-icon-small">
                      <LazyImage
                        :src="getTechIcon(tech)"
                        :fallback-src="getTechIconPlaceholder(tech)"
                        :alt="tech + '图标'"
                        class="tech-small-icon"
                      />
                    </div>
                    <span class="tech-name">{{ tech }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="architecture-features">
            <div
              v-for="(feature, index) in architectureData.features"
              :key="index"
              class="feature-highlight"
            >
              <el-icon><component :is="getFeatureIcon(feature.name)" /></el-icon>
              <h4>{{ feature.name }}</h4>
              <p>{{ feature.description }}</p>
              <div v-if="feature.metrics" class="feature-metrics">
                <span
                  v-for="(value, key) in feature.metrics"
                  :key="key"
                  class="metric-item"
                >
                  {{ key }}: {{ value }}
                </span>
              </div>
            </div>
          </div>

          <!-- 技术规格 -->
          <div v-if="architectureData.specifications" class="tech-specifications">
            <h4>技术规格</h4>
            <div class="spec-grid">
              <div
                v-for="(spec, index) in architectureData.specifications"
                :key="index"
                class="spec-item"
              >
                <strong>{{ spec.category }}</strong>
                <ul>
                  <li v-for="detail in spec.details" :key="detail">{{ detail }}</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 返回按钮 -->
    <div class="demo-footer">
      <el-button @click="goBack">
        <el-icon><ArrowLeft /></el-icon>
        返回首页
      </el-button>
      <el-button type="primary" @click="startInterview">
        <el-icon><VideoPlay /></el-icon>
        开始面试
      </el-button>
    </div>

    <!-- 功能演示对话框 -->
    <el-dialog
      v-model="showFeatureDialog"
      :title="selectedFeature?.title"
      width="90%"
      center
      :show-close="true"
    >
      <FeatureShowcase
        v-if="selectedFeature"
        :feature="selectedFeature"
        @start-demo="handleStartDemo"
        @try-feature="handleTryFeature"
        @view-details="handleViewDetails"
      />
    </el-dialog>

    <!-- 增强的视频播放对话框 -->
    <el-dialog
      v-model="showVideoDialog"
      :title="selectedVideo?.title"
      width="95%"
      center
      class="video-dialog"
    >
      <div class="video-player-dialog">
        <!-- 主视频播放区域 -->
        <div class="video-main-area">
          <!-- 实际的HTML5视频播放器 -->
          <div class="video-player-container-main">
            <video
              ref="videoPlayer"
              class="main-video-player"
              :src="selectedVideo?.url"
              :poster="selectedVideo?.poster || selectedVideo?.thumbnail"
              controls
              preload="metadata"
              @loadedmetadata="onVideoLoaded"
              @timeupdate="onTimeUpdate"
              @play="onVideoPlay"
              @pause="onVideoPause"
              @ended="onVideoEnded"
            >
              <source :src="selectedVideo?.url" type="video/mp4">
              您的浏览器不支持视频播放。
            </video>

            <!-- 视频加载状态指示器 -->
            <div class="video-loading-overlay" v-if="!videoLoaded">
              <div class="loading-spinner">
                <el-icon class="animate-pulse"><Loading /></el-icon>
              </div>
              <p>视频加载中...</p>
            </div>

            <!-- 视频控制覆盖层 -->
            <div class="video-controls-overlay" v-if="!isVideoPlaying && videoLoaded">
              <div class="video-info-overlay animate-fade-in-up">
                <h3>{{ selectedVideo?.title }}</h3>
                <p>{{ selectedVideo?.description }}</p>
                <el-button type="primary" size="large" class="play-button animate-pulse" @click="startVideoPlayback">
                  <el-icon><VideoPlay /></el-icon>
                  开始播放
                </el-button>
              </div>
            </div>
          </div>

          <!-- 视频详细信息 -->
          <div class="video-details" v-if="selectedVideo">
              <div class="video-stats-row">
                <span class="stat-badge">
                  <el-icon><Clock /></el-icon>
                  {{ selectedVideo.duration }}
                </span>
                <span class="stat-badge">
                  <el-icon><View /></el-icon>
                  {{ selectedVideo.views }}次观看
                </span>
                <span class="stat-badge">
                  <el-icon><Star /></el-icon>
                  {{ selectedVideo.rating }}分
                </span>
                <el-tag v-if="selectedVideo.difficulty" :type="getDifficultyType(selectedVideo.difficulty)">
                  {{ selectedVideo.difficulty }}
                </el-tag>
              </div>

              <!-- 技术特性 -->
              <div class="video-features" v-if="selectedVideo.features">
                <h4>核心特性</h4>
                <div class="features-list">
                  <div
                    v-for="feature in selectedVideo.features"
                    :key="feature.name"
                    class="feature-item"
                    :class="{ highlight: feature.highlight }"
                  >
                    <strong>{{ feature.name }}</strong>
                    <span>{{ feature.description }}</span>
                  </div>
                </div>
              </div>

              <!-- 技术规格 -->
              <div class="video-tech-specs" v-if="selectedVideo.technicalSpecs">
                <h4>技术规格</h4>
                <div class="tech-specs-compact">
                  <div v-for="(value, key) in selectedVideo.technicalSpecs" :key="key" class="spec-row">
                    <span class="spec-key">{{ getSpecLabel(key) }}</span>
                    <span class="spec-val">{{ value }}</span>
                  </div>
                </div>
              </div>
            </div>

          <!-- 视频控制按钮 -->
          <div class="video-controls">
            <el-button size="large" v-if="selectedVideo?.chapters" @click="showChapterNavigation = !showChapterNavigation">
              <el-icon><List /></el-icon>
              {{ showChapterNavigation ? '隐藏章节' : '显示章节' }}
            </el-button>
            <el-button size="large" @click="resetVideoPlayer">
              <el-icon><Setting /></el-icon>
              重置播放器
            </el-button>
          </div>
        </div>

        <!-- 章节导航侧边栏 -->
        <div v-if="showChapterNavigation && selectedVideo?.chapters" class="video-chapters-sidebar">
          <h4>
            <el-icon><List /></el-icon>
            视频章节
          </h4>
          <div class="chapters-compact-list">
            <div
              v-for="(chapter, index) in selectedVideo.chapters"
              :key="index"
              class="chapter-compact-item"
              @click="jumpToChapter(index)"
            >
              <div class="chapter-time-badge">{{ chapter.time }}</div>
              <div class="chapter-content">
                <h5>{{ chapter.title }}</h5>
                <p>{{ chapter.description }}</p>
                <div class="chapter-tags" v-if="chapter.keyPoints">
                  <el-tag
                    v-for="point in chapter.keyPoints.slice(0, 2)"
                    :key="point"
                    size="mini"
                    type="info"
                  >
                    {{ point }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  VideoPlay, VideoCamera, Star, Mouse, Setting, Operation,
  Check, ArrowRight, ArrowLeft, Lightning, Lock, Expand,
  ChatDotRound, TrendCharts, Document, Clock, View, List,
  Calendar, Guide, Loading, Picture
} from '@element-plus/icons-vue'
import DemoService from '../services/demoService.js'
import MediaService from '../services/mediaService.js'
import FeatureShowcase from '../components/Demo/FeatureShowcase.vue'
import ParticleBackground from '../components/Demo/ParticleBackground.vue'
import LazyImage from '../components/Demo/LazyImage.vue'
import ParallaxSection from '../components/Demo/ParallaxSection.vue'

const router = useRouter()

// 响应式数据
const activeTab = ref('features')
const currentStep = ref(0)
const showFeatureDialog = ref(false)
const showVideoDialog = ref(false)
const selectedFeature = ref(null)
const selectedVideo = ref(null)
const demoStats = ref({})

// 新增的演示功能数据
const showChapterNavigation = ref(false)
const mainVideoDetails = ref({})
const videoChapters = ref([])
const demoScenarios = ref({})
const currentScenario = ref(null)
const scenarioProgress = ref(0)

// 视频播放相关数据
const videoPlayer = ref(null)
const isVideoPlaying = ref(false)
const currentVideoTime = ref(0)
const videoDuration = ref(0)
const videoLoaded = ref(false)

// 从服务获取数据
const features = ref([])
const videoTutorials = ref([])
const simulatorSteps = ref([])
const architectureData = ref({})

// 初始化数据
onMounted(() => {
  loadDemoData()
})

const loadDemoData = () => {
  // 从服务加载演示数据
  const featuresData = DemoService.getFeatures()
  features.value = featuresData.map(feature => ({
    icon: feature.icon,
    title: feature.title,
    description: feature.description,
    demoDescription: feature.highlights.join('、'),
    id: feature.id,
    highlights: feature.highlights,
    demoSteps: feature.demoSteps,
    category: feature.category,
    difficulty: feature.difficulty,
    estimatedTime: feature.estimatedTime,
    technicalSpecs: feature.technicalSpecs
  }))

  const videosData = DemoService.getVideos()
  videoTutorials.value = videosData || []

  const stepsData = DemoService.getInteractiveSteps()
  simulatorSteps.value = stepsData.map(step => ({
    title: step.title,
    description: step.description,
    id: step.id,
    tips: step.tips,
    mockData: step.mockData,
    estimatedTime: step.estimatedTime,
    difficulty: step.difficulty,
    interactiveElements: step.interactiveElements
  }))

  architectureData.value = DemoService.getArchitecture()
  demoStats.value = DemoService.getDemoStats()

  // 加载新的演示功能数据
  mainVideoDetails.value = DemoService.getMainVideoDetails()
  videoChapters.value = DemoService.getVideoChapters('main-demo')
  demoScenarios.value = DemoService.getDemoScenarios()

  // 初始化媒体资源
  initializeMediaResources()
}

// 媒体资源相关方法
const initializeMediaResources = () => {
  // 为功能添加标签
  features.value = features.value.map(feature => ({
    ...feature,
    key: feature.id,
    tags: ['AI技术', '智能分析', '实时处理']
  }))

  // 为视频教程添加标签
  videoTutorials.value = videoTutorials.value.map((video, index) => ({
    ...video,
    key: `tutorial-${index}`,
    tags: ['教程', '演示', '操作指南']
  }))

  // 为模拟器步骤添加截图
  simulatorSteps.value = simulatorSteps.value.map((step, index) => ({
    ...step,
    screenshot: getStepScreenshot(index)
  }))
}

// 获取功能图片
const getFeatureImage = (featureKey) => {
  const placeholderMedia = MediaService.getPlaceholderMedia()
  return placeholderMedia.features[featureKey]?.image ||
         MediaService.createUnsplashImage('technology,ai,interface', 400, 300)
}

// 获取功能占位符
const getFeaturePlaceholder = (featureKey) => {
  return MediaService.createPlaceholder(400, 300, '功能演示', '1890ff')
}

// 获取视频缩略图
const getVideoThumbnail = (videoKey) => {
  const placeholderMedia = MediaService.getPlaceholderMedia()
  return placeholderMedia.videos.mainThumbnail ||
         MediaService.createUnsplashImage('video,demo,technology', 800, 450)
}

// 获取视频占位符
const getVideoPlaceholder = (videoKey) => {
  return MediaService.createPlaceholder(800, 450, '视频演示', '667eea')
}

// 获取教程占位符
const getTutorialPlaceholder = (index) => {
  const colors = ['1890ff', '52c41a', 'faad14', 'ff4d4f', '722ed1', '13c2c2']
  const color = colors[index % colors.length]
  return MediaService.createPlaceholder(400, 225, `教程 ${index + 1}`, color)
}

// 获取章节占位符
const getChapterPlaceholder = (index) => {
  return MediaService.createPlaceholder(120, 68, `章节 ${index + 1}`, '40a9ff')
}

// 获取步骤截图
const getStepScreenshot = (index) => {
  return MediaService.createUnsplashImage('interface,dashboard,app', 300, 200)
}

// 获取步骤占位符
const getStepPlaceholder = (index) => {
  return MediaService.createPlaceholder(300, 200, `步骤 ${index + 1}`, 'b37feb')
}

// 获取架构图表
const getArchitectureDiagram = (type) => {
  return MediaService.createUnsplashImage('architecture,diagram,system', 800, 600)
}

// 获取架构占位符
const getArchitecturePlaceholder = (type) => {
  return MediaService.createPlaceholder(800, 600, '架构图表', '667eea')
}

// 获取技术图标
const getTechIcon = (techName) => {
  return MediaService.createPlaceholder(48, 48, techName.charAt(0), '1890ff')
}

// 获取技术图标占位符
const getTechIconPlaceholder = (techName) => {
  return MediaService.createPlaceholder(48, 48, techName.charAt(0), 'f0f0f0', '666666')
}

// 获取难度类型样式
const getDifficultyType = (difficulty) => {
  const types = {
    '简单': 'success',
    '中等': 'warning',
    '困难': 'danger',
    '入门': 'info'
  }
  return types[difficulty] || 'info'
}

// 获取难度样式类
const getDifficultyClass = (difficulty) => {
  const classes = {
    '简单': 'difficulty-easy',
    '中等': 'difficulty-medium',
    '困难': 'difficulty-hard',
    '入门': 'difficulty-beginner'
  }
  return classes[difficulty] || 'difficulty-beginner'
}

// 界面截图数据
const interfaceScreenshots = ref([
  {
    title: '面试主界面',
    description: '清晰直观的面试操作界面',
    image: MediaService.createUnsplashImage('interview,interface,dashboard', 600, 400),
    placeholder: MediaService.createPlaceholder(600, 400, '面试界面', '1890ff'),
    tags: ['主界面', '用户体验', '交互设计']
  },
  {
    title: 'AI分析结果',
    description: '智能分析结果可视化展示',
    image: MediaService.createUnsplashImage('analytics,charts,ai', 600, 400),
    placeholder: MediaService.createPlaceholder(600, 400, 'AI分析', '52c41a'),
    tags: ['数据分析', '可视化', 'AI技术']
  },
  {
    title: '综合报告',
    description: '详细的评估报告和建议',
    image: MediaService.createUnsplashImage('report,document,analysis', 600, 400),
    placeholder: MediaService.createPlaceholder(600, 400, '综合报告', 'faad14'),
    tags: ['报告生成', '评估结果', '数据展示']
  },
  {
    title: '学习路径',
    description: '个性化的学习建议和路径规划',
    image: MediaService.createUnsplashImage('learning,path,education', 600, 400),
    placeholder: MediaService.createPlaceholder(600, 400, '学习路径', '722ed1'),
    tags: ['个性化', '学习规划', '智能推荐']
  }
])

// 方法
const handleTabClick = (tab) => {
  console.log('切换到标签:', tab.props.name)
}

const showFeatureDemo = (feature) => {
  selectedFeature.value = feature
  showFeatureDialog.value = true
}

const playFeatureDemo = (feature) => {
  const result = DemoService.startFeatureDemo(feature.id)
  if (result.success) {
    ElMessage.success(result.message)
    showFeatureDemo(feature)
  } else {
    ElMessage.error(result.message)
  }
}

const tryFeature = (feature) => {
  ElMessage.info(`即将体验 ${feature.title} 功能`)
  // 根据不同功能跳转到相应页面
  switch (feature.id) {
    case 'multimodal-input':
      router.push('/interview-selection')
      break
    case 'comprehensive-report':
      // 如果有测试报告，跳转到报告页面
      router.push('/test-report-to-learning')
      break
    case 'ai-interaction':
      router.push('/interview-selection')
      break
    default:
      router.push('/interview-selection')
  }
}

// 打开视频对话框
const openVideoDialog = () => {
  const mainVideo = {
    title: mainVideoDetails.value.title || '系统演示视频',
    url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
    poster: getVideoThumbnail('main'),
    thumbnail: getVideoThumbnail('main'),
    description: mainVideoDetails.value.description || '完整的系统功能演示'
  }
  selectedVideo.value = mainVideo
  showVideoDialog.value = true
}

const playMainVideo = () => {
  const result = DemoService.playVideo('main-demo')
  if (result.success) {
    ElMessage.success(result.message)
    selectedVideo.value = result.video
    showVideoDialog.value = true
  } else {
    ElMessage.error(result.message)
  }
}

const playVideo = (video) => {
  const result = DemoService.playVideo(video.id)
  if (result.success) {
    ElMessage.success(result.message)
    selectedVideo.value = result.video
    showVideoDialog.value = true
  } else {
    ElMessage.error(result.message)
  }
}

// 跳转到视频章节
const jumpToChapter = (chapterIndex) => {
  const result = DemoService.jumpToChapter('main-demo', chapterIndex)
  if (result.success) {
    ElMessage.success(result.message)

    // 实际的视频跳转逻辑
    if (videoPlayer.value && videoLoaded.value) {
      const timeInSeconds = parseTimeToSeconds(result.timestamp)
      videoPlayer.value.currentTime = timeInSeconds

      // 如果视频没有在播放，则开始播放
      if (!isVideoPlaying.value) {
        videoPlayer.value.play()
      }
    }
  } else {
    ElMessage.error(result.message)
  }
}

// 将时间字符串转换为秒数
const parseTimeToSeconds = (timeString) => {
  const parts = timeString.split(':')
  if (parts.length === 2) {
    return parseInt(parts[0]) * 60 + parseInt(parts[1])
  } else if (parts.length === 3) {
    return parseInt(parts[0]) * 3600 + parseInt(parts[1]) * 60 + parseInt(parts[2])
  }
  return 0
}

// 开始演示场景
const startDemoScenario = (scenarioId) => {
  const result = DemoService.startDemoScenario(scenarioId)
  if (result.success) {
    ElMessage.success(result.message)
    currentScenario.value = result.scenario
    scenarioProgress.value = 0
    // 这里可以添加场景演示的具体逻辑
    console.log(`开始演示场景: ${result.scenario.title}`)
  } else {
    ElMessage.error(result.message)
  }
}

// 获取技术规格标签
const getSpecLabel = (key) => {
  const labels = {
    aiModel: 'AI模型',
    responseTime: '响应时间',
    accuracy: '准确率',
    supportedLanguages: '支持语言',
    concurrentUsers: '并发用户',
    dataPrivacy: '数据安全',
    deployment: '部署方式'
  }
  return labels[key] || key
}

// 获取难度类型样式
const getDifficultyType = (difficulty) => {
  const types = {
    '入门': 'success',
    '初级': 'info',
    '中级': 'warning',
    '高级': 'danger'
  }
  return types[difficulty] || 'info'
}

const nextStep = () => {
  if (currentStep.value < simulatorSteps.length - 1) {
    currentStep.value++
    ElMessage.success(`进入步骤 ${currentStep.value + 1}`)
  }
}

const goToStep = (index) => {
  currentStep.value = index
  ElMessage.info(`跳转到步骤 ${index + 1}`)
}

const resetSimulator = () => {
  currentStep.value = 0
  ElMessage.info('模拟器已重置')
}

const getFeatureIcon = (featureName) => {
  const iconMap = {
    '高性能': 'Lightning',
    '安全可靠': 'Lock',
    '可扩展': 'Expand',
    '智能化': 'Star',
    '实时性': 'VideoPlay',
    '稳定性': 'Check'
  }
  return iconMap[featureName] || 'Setting'
}

const startRealInterview = () => {
  ElMessageBox.confirm(
    '您即将开始真实的面试流程，是否确认？',
    '开始面试',
    {
      confirmButtonText: '确认开始',
      cancelButtonText: '取消',
      type: 'info'
    }
  ).then(() => {
    router.push('/interview-selection')
  })
}

const goBack = () => {
  router.push('/')
}

const startInterview = () => {
  router.push('/interview-selection')
}

const playFeatureVideo = () => {
  ElMessage.success(`正在播放 ${selectedFeature.value?.title} 演示视频`)
  showFeatureDialog.value = false
}

// 视频播放控制方法
const startVideoPlayback = () => {
  if (videoPlayer.value) {
    videoPlayer.value.play()
    ElMessage.success(`开始播放: ${selectedVideo.value?.title}`)
  }
}

const onVideoLoaded = () => {
  if (videoPlayer.value) {
    videoDuration.value = videoPlayer.value.duration
    videoLoaded.value = true
    ElMessage.success('视频加载完成')
  }
}

const onTimeUpdate = () => {
  if (videoPlayer.value) {
    currentVideoTime.value = videoPlayer.value.currentTime
  }
}

const onVideoPlay = () => {
  isVideoPlaying.value = true
}

const onVideoPause = () => {
  isVideoPlaying.value = false
}

const onVideoEnded = () => {
  isVideoPlaying.value = false
  ElMessage.success('视频播放完成')
}

const resetVideoPlayer = () => {
  if (videoPlayer.value) {
    videoPlayer.value.currentTime = 0
    videoPlayer.value.pause()
    isVideoPlaying.value = false
    currentVideoTime.value = 0
    ElMessage.info('播放器已重置')
  }
}

// FeatureShowcase 组件事件处理
const handleStartDemo = (feature) => {
  ElMessage.success(`开始演示: ${feature.title}`)
  showFeatureDialog.value = false
  // 这里可以启动具体的演示流程
}

const handleTryFeature = (feature) => {
  showFeatureDialog.value = false
  tryFeature(feature)
}

const handleViewDetails = (feature) => {
  ElMessage.info(`查看 ${feature.title} 详细信息`)
  // 这里可以显示更详细的功能说明
}
</script>

<style scoped>
/* ==================== CSS变量映射 ==================== */
.demo-page {
  /* 映射设计系统变量 */
  --color-primary: var(--primary-color);
  --color-primary-light: var(--primary-light);
  --color-primary-lighter: var(--primary-lighter);
  --color-primary-dark: var(--primary-dark);
  --color-success: var(--success-color);
  --color-warning: var(--warning-color);
  --color-error: var(--error-color);

  --text-primary: var(--text-primary);
  --text-secondary: var(--text-secondary);
  --text-tertiary: var(--text-tertiary);

  --bg-primary: var(--bg-primary);
  --bg-secondary: var(--bg-secondary);
  --bg-tertiary: var(--bg-tertiary);

  --border-color: var(--border-base);
  --border-light: var(--border-light);

  --gradient-primary: var(--gradient-primary);
  --gradient-tech: var(--gradient-tech);
  --gradient-warm: var(--gradient-warm);

  --spacing-xs: var(--spacing-xs);
  --spacing-sm: var(--spacing-sm);
  --spacing-md: var(--spacing-base);
  --spacing-lg: var(--spacing-lg);
  --spacing-xl: var(--spacing-xl);
  --spacing-xxl: var(--spacing-2xl);

  --border-radius-sm: var(--border-radius-sm);
  --border-radius-md: var(--border-radius-base);
  --border-radius-lg: var(--border-radius-lg);
  --border-radius-xl: var(--border-radius-xl);

  --shadow-sm: var(--shadow-sm);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);

  --transition-base: var(--transition-base);
  --transition-fast: var(--transition-fast);

  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  font-family: var(--font-family-base);
}

/* ==================== 页面头部优化 ==================== */
.demo-header {
  background: var(--gradient-tech);
  color: white;
  padding: var(--spacing-3xl) 0 var(--spacing-2xl);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.demo-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  position: relative;
  z-index: 1;
}

.demo-title {
  font-size: clamp(2rem, 5vw, 3.5rem);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  animation: fadeInUp 0.8s ease-out;
}

.demo-subtitle {
  font-size: clamp(1rem, 3vw, 1.4rem);
  font-weight: var(--font-weight-medium);
  opacity: 0.95;
  margin-bottom: var(--spacing-xl);
  line-height: var(--line-height-relaxed);
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.demo-stats {
  display: flex;
  justify-content: center;
  gap: var(--spacing-xl);
  margin: var(--spacing-xl) 0;
  padding: var(--spacing-xl);
  background: rgba(255, 255, 255, 0.15);
  border-radius: var(--border-radius-xl);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  animation: fadeInUp 0.8s ease-out 0.4s both;
}

.stat-item {
  text-align: center;
  color: white;
  padding: var(--spacing-md);
  border-radius: var(--border-radius-lg);
  transition: transform var(--transition-base);
}

.stat-item:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.1);
}

.stat-number {
  display: block;
  font-size: clamp(1.5rem, 4vw, 2.5rem);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-xs);
  background: linear-gradient(45deg, #fff, #e3f2fd);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  font-size: var(--font-size-sm);
  opacity: 0.9;
  font-weight: var(--font-weight-medium);
}

.demo-tabs {
  margin-top: var(--spacing-xl);
  animation: fadeInUp 0.8s ease-out 0.6s both;
}

.demo-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-xl);
}

/* ==================== 功能演示样式优化 ==================== */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--spacing-xl);
  margin-top: var(--spacing-xl);
  padding: var(--spacing-lg) 0;
}

.feature-card {
  background: linear-gradient(145deg, #ffffff, #f8fafc);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-2xl);
  text-align: center;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all var(--transition-base);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
  transform: scaleX(0);
  transition: transform var(--transition-base);
}

.feature-card:hover::before {
  transform: scaleX(1);
}

.feature-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  background: linear-gradient(145deg, #ffffff, #f1f5f9);
}

.feature-icon {
  color: var(--color-primary);
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, var(--color-primary-light), var(--color-primary));
  border-radius: var(--border-radius-full);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 16px rgba(24, 144, 255, 0.3);
  transition: all var(--transition-base);
}

.feature-card:hover .feature-icon {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 12px 24px rgba(24, 144, 255, 0.4);
}

.feature-icon .el-icon {
  color: white;
  font-size: 2.5rem;
}

.feature-card h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: var(--spacing-lg) 0;
  line-height: var(--line-height-tight);
}

.feature-card p {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-xl);
}

.feature-actions {
  margin-top: var(--spacing-xl);
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  flex-wrap: wrap;
}

.feature-actions .el-button {
  border-radius: var(--border-radius-lg);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-sm) var(--spacing-lg);
  transition: all var(--transition-base);
}

.feature-actions .el-button--primary {
  background: var(--gradient-primary);
  border: none;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.feature-actions .el-button--primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
}

/* ==================== 视频演示样式优化 ==================== */
.video-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-2xl);
  margin-top: var(--spacing-xl);
  align-items: start;
}

.main-video-section {
  background: linear-gradient(145deg, #ffffff, #f8fafc);
  border-radius: var(--border-radius-2xl);
  padding: var(--spacing-2xl);
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.video-player-container {
  position: relative;
  border-radius: var(--border-radius-xl);
  overflow: hidden;
  background: #000;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.video-player {
  background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
  border-radius: var(--border-radius-xl);
  overflow: hidden;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
}

.video-placeholder {
  padding: var(--spacing-4xl);
  text-align: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.video-placeholder::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  opacity: 0.3;
}

.video-placeholder > * {
  position: relative;
  z-index: 1;
}

.video-placeholder .el-icon {
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-xl);
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-full);
  backdrop-filter: blur(10px);
}

.video-placeholder h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-lg);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.video-placeholder p {
  font-size: var(--font-size-lg);
  opacity: 0.9;
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--spacing-xl);
}

.video-list {
  background: linear-gradient(145deg, #ffffff, #f8fafc);
  border-radius: var(--border-radius-xl);
  padding: var(--spacing-xl);
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.video-list h3 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.video-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  cursor: pointer;
  transition: all var(--transition-base);
  margin-bottom: var(--spacing-sm);
  border: 1px solid transparent;
  background: rgba(255, 255, 255, 0.5);
}

.video-item:hover {
  background: linear-gradient(145deg, #f1f5f9, #e2e8f0);
  transform: translateX(4px);
  border-color: var(--color-primary-light);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

.video-item .el-icon {
  color: var(--color-primary);
  font-size: 1.5rem;
  padding: var(--spacing-sm);
  background: var(--color-primary-light);
  border-radius: var(--border-radius-lg);
  margin-right: var(--spacing-md);
}

.video-info {
  flex: 1;
}

.video-info h4 {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  line-height: var(--line-height-tight);
}

.video-info p {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: var(--line-height-base);
}

/* ==================== 视频标签和元数据优化 ==================== */
.video-tags {
  margin: var(--spacing-md) 0;
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.video-tags .el-tag {
  border-radius: var(--border-radius-lg);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-xs) var(--spacing-md);
  border: none;
  background: linear-gradient(135deg, var(--color-primary-light), var(--color-primary));
  color: white;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
  transition: all var(--transition-base);
  font-size: var(--font-size-sm);
}

.video-tags .el-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

.video-meta {
  display: flex;
  gap: var(--spacing-lg);
  align-items: center;
  flex-wrap: wrap;
  margin-top: var(--spacing-md);
  padding: var(--spacing-md);
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--border-radius-lg);
  backdrop-filter: blur(10px);
}

.video-duration,
.video-views,
.video-rating {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  padding: var(--spacing-xs) var(--spacing-sm);
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-sm);
  transition: all var(--transition-base);
}

.video-duration:hover,
.video-views:hover,
.video-rating:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.video-rating {
  color: var(--color-warning);
}

.video-rating .el-icon {
  color: #ffd700;
}

.video-thumbnail {
  width: 80px;
  height: 60px;
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
  border-radius: var(--border-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-right: var(--spacing-lg);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}

.video-thumbnail::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.video-thumbnail:hover::before {
  transform: translateX(100%);
}

.video-thumbnail:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
}

.video-thumbnail .el-icon {
  font-size: 2rem;
}

/* ==================== 交互体验样式优化 ==================== */
.demo-simulator {
  background: linear-gradient(145deg, #ffffff, #f8fafc);
  border-radius: var(--border-radius-2xl);
  padding: var(--spacing-2xl);
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  margin-top: var(--spacing-xl);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.demo-simulator::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
}

.demo-simulator h3 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xl);
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
}

.simulator-steps {
  margin: var(--spacing-xl) 0;
  position: relative;
}

.simulator-steps::before {
  content: '';
  position: absolute;
  left: 20px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, var(--color-primary), var(--color-primary-light));
  z-index: 1;
}

.step-card {
  display: flex;
  align-items: center;
  padding: var(--spacing-xl);
  border: 2px solid transparent;
  border-radius: var(--border-radius-xl);
  margin-bottom: var(--spacing-lg);
  cursor: pointer;
  transition: all var(--transition-base);
  background: linear-gradient(145deg, #ffffff, #f1f5f9);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
}

.step-card:hover {
  transform: translateX(8px);
  box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1);
  border-color: var(--color-primary-light);
}

.step-card.active {
  border-color: var(--color-primary);
  background: linear-gradient(145deg, #e3f2fd, #bbdefb);
  box-shadow: 0 8px 25px rgba(24, 144, 255, 0.2);
}

.step-card.completed {
  border-color: var(--color-success);
  background: linear-gradient(145deg, #e8f5e8, #c8e6c9);
  box-shadow: 0 8px 25px rgba(76, 175, 80, 0.2);
}

.step-number {
  width: 50px;
  height: 50px;
  border-radius: var(--border-radius-full);
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-lg);
  margin-right: var(--spacing-xl);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  transition: all var(--transition-base);
}

.step-card.completed .step-number {
  background: linear-gradient(135deg, var(--color-success), #388e3c);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.step-card:hover .step-number {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
}

.step-content {
  flex: 1;
}

.step-content h4 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
}

.step-content p {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--text-secondary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
}

.step-details {
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-md);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.step-meta {
  display: flex;
  gap: var(--spacing-lg);
  align-items: center;
  flex-wrap: wrap;
}

.step-time {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--bg-tertiary);
  border-radius: var(--border-radius-sm);
}

.interactive-hint {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-md);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-primary);
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--color-primary-light);
  border-radius: var(--border-radius-lg);
  opacity: 0.9;
}

.simulator-actions {
  text-align: center;
  margin-top: var(--spacing-2xl);
  padding-top: var(--spacing-xl);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.simulator-actions .el-button {
  margin: 0 var(--spacing-sm);
  border-radius: var(--border-radius-lg);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-md) var(--spacing-xl);
  transition: all var(--transition-base);
}

.simulator-actions .el-button--primary {
  background: var(--gradient-primary);
  border: none;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.simulator-actions .el-button--primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
}

/* 技术架构样式 */
.architecture-overview {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
  margin-top: var(--spacing-lg);
}

.architecture-diagram {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-lg);
  margin: var(--spacing-xl) 0;
}

.arch-layer {
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-md);
  text-align: center;
}

.arch-layer.frontend {
  background: linear-gradient(135deg, #4CAF50, #8BC34A);
  color: white;
}

.arch-layer.backend {
  background: linear-gradient(135deg, #2196F3, #03A9F4);
  color: white;
}

.arch-layer.ai {
  background: linear-gradient(135deg, #FF9800, #FFC107);
  color: white;
}

.arch-layer.data {
  background: linear-gradient(135deg, #9C27B0, #E91E63);
  color: white;
}

.tech-stack {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  justify-content: center;
  margin-top: var(--spacing-md);
}

.tech-item {
  background: rgba(255, 255, 255, 0.2);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: 0.9rem;
}

.architecture-features {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

.feature-highlight {
  text-align: center;
  padding: var(--spacing-lg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
}

.feature-highlight h4 {
  margin: var(--spacing-sm) 0;
  color: var(--text-primary);
}

.feature-highlight p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}

.feature-metrics {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-sm);
}

.metric-item {
  font-size: 0.8rem;
  color: var(--color-primary);
  background: var(--color-primary-light);
  padding: 2px 6px;
  border-radius: var(--border-radius-sm);
}

.arch-description {
  text-align: center;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
  font-size: 1.1rem;
}

.layer-description {
  font-size: 0.9rem;
  margin: var(--spacing-sm) 0;
  opacity: 0.9;
}

.tech-specifications {
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-xl);
  border-top: 1px solid var(--border-color);
}

.tech-specifications h4 {
  text-align: center;
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
}

.spec-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.spec-item {
  background: var(--bg-secondary);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-md);
}

.spec-item strong {
  color: var(--text-primary);
  display: block;
  margin-bottom: var(--spacing-sm);
}

.spec-item ul {
  margin: 0;
  padding-left: var(--spacing-lg);
}

.spec-item li {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.demo-footer {
  text-align: center;
  padding: var(--spacing-xl);
  background: white;
  border-top: 1px solid var(--border-color);
}

/* 对话框样式 */
.feature-demo-content,
.video-player-dialog {
  text-align: center;
  padding: var(--spacing-xl);
}

.demo-video-placeholder,
.video-placeholder-large {
  background: var(--bg-secondary);
  padding: var(--spacing-xxl);
  border-radius: var(--border-radius-lg);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .video-section {
    grid-template-columns: 1fr;
  }
  
  .architecture-diagram {
    grid-template-columns: 1fr;
  }
  
  .architecture-features {
    grid-template-columns: 1fr;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
}

/* 新增的演示功能样式 */

/* 主视频区域 */
.main-video-section {
  margin-bottom: var(--spacing-xl);
}

.video-player-container {
  display: flex;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
}

.video-info-card {
  background: var(--bg-secondary);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  margin: var(--spacing-md) 0;
}

.video-stats {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.tech-features {
  display: flex;
  gap: var(--spacing-xs);
  flex-wrap: wrap;
}

.feature-tag {
  font-weight: 500;
}

.video-actions {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-md);
  justify-content: center;
}

/* 章节导航 */
.chapter-navigation {
  flex: 1;
  background: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  max-height: 600px;
  overflow-y: auto;
}

.chapter-navigation h4 {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

.chapters-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.chapter-item {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: all 0.3s ease;
}

.chapter-item:hover {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
  transform: translateY(-2px);
}

.chapter-thumbnail {
  width: 80px;
  height: 60px;
  background: var(--bg-tertiary);
  border-radius: var(--border-radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.chapter-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.chapter-info {
  flex: 1;
}

.chapter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xs);
}

.chapter-time {
  font-weight: 600;
  color: var(--color-primary);
}

.chapter-duration {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.chapter-info h5 {
  margin: 0 0 var(--spacing-xs) 0;
  color: var(--text-primary);
}

.chapter-info p {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.chapter-keypoints {
  display: flex;
  gap: var(--spacing-xs);
  flex-wrap: wrap;
}

/* 技术规格面板 */
.tech-specs-panel {
  background: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

.tech-specs-panel h4 {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

.specs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.spec-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm);
  background: var(--bg-primary);
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--border-color);
}

.spec-label {
  font-weight: 500;
  color: var(--text-secondary);
}

.spec-value {
  font-weight: 600;
  color: var(--color-primary);
}

/* 演示场景 */
.demo-scenarios-section {
  margin-bottom: var(--spacing-xl);
}

.demo-scenarios-section h3 {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
}

.scenarios-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.scenario-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.scenario-card:hover {
  border-color: var(--color-primary);
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.scenario-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-sm);
}

.scenario-header h4 {
  margin: 0;
  color: var(--text-primary);
}

.scenario-card p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
  line-height: 1.6;
}

.scenario-meta {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  font-size: 0.9rem;
}

.scenario-time,
.scenario-steps {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--text-secondary);
}

.scenario-btn {
  width: 100%;
}

/* 增强的教程网格 */
.tutorials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--spacing-lg);
}

.video-item {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.video-item:hover {
  border-color: var(--color-primary);
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.video-thumbnail {
  position: relative;
  height: 180px;
  background: var(--bg-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-primary);
}

.video-overlay {
  position: absolute;
  bottom: var(--spacing-sm);
  right: var(--spacing-sm);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: var(--border-radius-sm);
  font-size: 0.8rem;
}

.video-info {
  padding: var(--spacing-md);
}

.video-info h4 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
}

.video-info p {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
  line-height: 1.5;
}

.video-meta {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
  margin-bottom: var(--spacing-sm);
  font-size: 0.8rem;
}

.video-views,
.video-rating {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--text-secondary);
}

.video-tags {
  display: flex;
  gap: var(--spacing-xs);
  flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .video-player-container {
    flex-direction: column;
  }

  .video-stats {
    justify-content: center;
  }

  .scenarios-grid,
  .tutorials-grid {
    grid-template-columns: 1fr;
  }

  .specs-grid {
    grid-template-columns: 1fr;
  }

  .video-actions {
    flex-direction: column;
    align-items: center;
  }
}

/* 增强的视频对话框样式 */
.video-dialog .el-dialog__body {
  padding: 0;
}

.video-player-dialog {
  display: flex;
  min-height: 600px;
}

.video-main-area {
  flex: 1;
  padding: var(--spacing-xl);
}

/* ==================== 视频播放器容器样式优化 ==================== */
.video-player-container-main {
  position: relative;
  width: 100%;
  background: linear-gradient(145deg, #000000, #1a1a1a);
  border-radius: var(--border-radius-2xl);
  overflow: hidden;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.3),
    0 10px 10px -5px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.1);
  transition: all var(--transition-base);
}

.video-player-container-main:hover {
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.4),
    0 15px 15px -5px rgba(0, 0, 0, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
  border-color: rgba(24, 144, 255, 0.3);
}

.main-video-player {
  width: 100%;
  height: auto;
  min-height: 450px;
  max-height: 650px;
  display: block;
  background: #000;
  border-radius: var(--border-radius-xl);
  transition: all var(--transition-base);
}

.main-video-player:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* 视频播放器控制栏美化 */
.main-video-player::-webkit-media-controls-panel {
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  border-radius: 0 0 var(--border-radius-xl) var(--border-radius-xl);
}

.main-video-player::-webkit-media-controls-play-button,
.main-video-player::-webkit-media-controls-pause-button {
  background: var(--gradient-primary);
  border-radius: var(--border-radius-full);
  margin: 0 var(--spacing-sm);
}

.main-video-player::-webkit-media-controls-timeline {
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--border-radius-sm);
  margin: 0 var(--spacing-md);
}

.main-video-player::-webkit-media-controls-current-time-display,
.main-video-player::-webkit-media-controls-time-remaining-display {
  color: white;
  font-weight: var(--font-weight-medium);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* ==================== 视频加载和控制覆盖层 ==================== */
.video-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(24, 144, 255, 0.1));
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 15;
  backdrop-filter: blur(10px);
  color: white;
}

.loading-spinner {
  margin-bottom: var(--spacing-lg);
}

.loading-spinner .el-icon {
  font-size: 3rem;
  color: var(--color-primary);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.video-loading-overlay p {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
}

.video-controls-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(24, 144, 255, 0.1));
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  transition: all var(--transition-base);
  backdrop-filter: blur(10px);
}

.video-controls-overlay:hover {
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(24, 144, 255, 0.15));
}

.video-info-overlay {
  text-align: center;
  color: white;
  padding: var(--spacing-2xl);
  max-width: 600px;
  margin: 0 auto;
}

.video-info-overlay h3 {
  color: white;
  margin-bottom: var(--spacing-lg);
  font-size: clamp(1.5rem, 4vw, 2.5rem);
  font-weight: var(--font-weight-bold);
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  line-height: var(--line-height-tight);
}

.video-info-overlay p {
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: var(--spacing-2xl);
  font-size: clamp(1rem, 2.5vw, 1.2rem);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-relaxed);
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
}

.video-info-overlay .play-button {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-dark));
  border: none;
  padding: var(--spacing-lg) var(--spacing-2xl);
  border-radius: var(--border-radius-2xl);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: white;
  box-shadow:
    0 8px 16px rgba(24, 144, 255, 0.4),
    0 4px 8px rgba(0, 0, 0, 0.2);
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}

.video-info-overlay .play-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.video-info-overlay .play-button:hover::before {
  left: 100%;
}

.video-info-overlay .play-button:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow:
    0 12px 24px rgba(24, 144, 255, 0.5),
    0 6px 12px rgba(0, 0, 0, 0.3);
}

.video-info-overlay .play-button .el-icon {
  font-size: 1.5rem;
  margin-right: var(--spacing-sm);
}

.video-placeholder-large {
  text-align: center;
  padding: var(--spacing-xl);
  background: var(--bg-secondary);
  border-radius: var(--border-radius-md);
}

.video-details {
  margin: var(--spacing-lg) 0;
  text-align: left;
}

.video-stats-row {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  margin-bottom: var(--spacing-md);
  flex-wrap: wrap;
}

.stat-badge {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  background: var(--bg-primary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.video-features,
.video-tech-specs {
  margin: var(--spacing-lg) 0;
  padding: var(--spacing-md);
  background: var(--bg-primary);
  border-radius: var(--border-radius-md);
}

.video-features h4,
.video-tech-specs h4 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--text-primary);
  font-size: 1.1rem;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.feature-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--border-color);
}

.feature-item.highlight {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
}

.feature-item strong {
  color: var(--text-primary);
}

.feature-item span {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.tech-specs-compact {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-sm);
}

.spec-row {
  display: flex;
  justify-content: space-between;
  padding: var(--spacing-xs);
  background: var(--bg-secondary);
  border-radius: var(--border-radius-sm);
}

.spec-key {
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.spec-val {
  color: var(--color-primary);
  font-weight: 500;
  font-size: 0.9rem;
}

.video-controls {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  margin-top: var(--spacing-lg);
}

/* 章节侧边栏 */
.video-chapters-sidebar {
  width: 350px;
  background: var(--bg-tertiary);
  padding: var(--spacing-lg);
  border-left: 1px solid var(--border-color);
  overflow-y: auto;
}

.video-chapters-sidebar h4 {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin: 0 0 var(--spacing-md) 0;
  color: var(--text-primary);
}

.chapters-compact-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.chapter-compact-item {
  display: flex;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  transition: all 0.3s ease;
}

.chapter-compact-item:hover {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
}

.chapter-time-badge {
  background: var(--color-primary);
  color: white;
  padding: 2px 6px;
  border-radius: var(--border-radius-sm);
  font-size: 0.8rem;
  font-weight: 500;
  white-space: nowrap;
  align-self: flex-start;
}

.chapter-content {
  flex: 1;
}

.chapter-content h5 {
  margin: 0 0 var(--spacing-xs) 0;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.chapter-content p {
  margin: 0 0 var(--spacing-xs) 0;
  color: var(--text-secondary);
  font-size: 0.8rem;
  line-height: 1.4;
}

.chapter-tags {
  display: flex;
  gap: var(--spacing-xs);
  flex-wrap: wrap;
}

/* ==================== 动画效果 ==================== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* ==================== 响应式设计优化 ==================== */
@media (max-width: 1024px) {
  .demo-title {
    font-size: clamp(1.8rem, 4vw, 2.5rem);
  }

  .demo-subtitle {
    font-size: clamp(0.9rem, 2.5vw, 1.2rem);
  }

  .features-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
  }

  .video-section {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
  }

  .architecture-diagram {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
}

@media (max-width: 768px) {
  .demo-header {
    padding: var(--spacing-2xl) 0 var(--spacing-xl);
  }

  .demo-stats {
    flex-direction: column;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
  }

  .stat-item {
    padding: var(--spacing-sm);
  }

  .demo-content {
    padding: var(--spacing-lg);
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .feature-card {
    padding: var(--spacing-xl);
  }

  .video-player-dialog {
    flex-direction: column;
  }

  .video-player-container-main {
    margin-bottom: var(--spacing-md);
  }

  .main-video-player {
    min-height: 250px;
    max-height: 400px;
  }

  .video-info-overlay h3 {
    font-size: var(--font-size-xl);
  }

  .video-info-overlay p {
    font-size: var(--font-size-base);
  }

  .video-chapters-sidebar {
    width: 100%;
    border-left: none;
    border-top: 1px solid var(--border-color);
  }

  .video-stats-row {
    justify-content: center;
  }

  .tech-specs-compact {
    grid-template-columns: 1fr;
  }

  .video-controls {
    flex-direction: column;
    align-items: center;
  }

  .step-card {
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
  }

  .step-number {
    width: 40px;
    height: 40px;
    margin-right: var(--spacing-lg);
  }

  .simulator-steps::before {
    left: 20px;
  }
}

@media (max-width: 480px) {
  .demo-title {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .demo-stats {
    gap: var(--spacing-md);
  }

  .feature-actions {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .feature-actions .el-button {
    width: 100%;
  }

  .video-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .step-card {
    flex-direction: column;
    text-align: center;
  }

  .step-number {
    margin-right: 0;
    margin-bottom: var(--spacing-md);
  }

  .simulator-steps::before {
    display: none;
  }
}

/* 新增视觉组件样式 */

/* 功能卡片媒体样式 */
.feature-media {
  position: relative;
  margin-bottom: var(--spacing-md);
  border-radius: var(--border-radius-md);
  overflow: hidden;
}

.feature-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.feature-icon-overlay {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  padding: var(--spacing-sm);
  backdrop-filter: blur(10px);
}

.feature-info {
  padding: var(--spacing-md);
}

.feature-tags {
  margin: var(--spacing-sm) 0;
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.feature-tag {
  font-size: 0.8rem;
}

/* 视频播放覆盖层样式 */
.video-play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
  opacity: 0;
  transition: opacity 0.3s ease;
  cursor: pointer;
}

.video-play-overlay:hover {
  opacity: 1;
}

.play-button-large {
  background: rgba(24, 144, 255, 0.9);
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  transition: transform 0.3s ease;
}

.play-button-large:hover {
  transform: scale(1.1);
}

.play-button {
  background: rgba(24, 144, 255, 0.9);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  transition: transform 0.3s ease;
}

.play-button:hover {
  transform: scale(1.1);
}

.video-duration-badge {
  position: absolute;
  bottom: var(--spacing-sm);
  right: var(--spacing-sm);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: 0.8rem;
}

.video-info-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: var(--spacing-lg) var(--spacing-md) var(--spacing-md);
}

.video-info-overlay h3 {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: 1.2rem;
}

.video-info-overlay p {
  margin: 0;
  font-size: 0.9rem;
  opacity: 0.9;
}

/* 章节导航增强样式 */
.chapter-item {
  transition: all 0.3s ease;
}

.chapter-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.chapter-thumbnail {
  position: relative;
}

.chapter-thumb-image {
  width: 120px;
  height: 68px;
  object-fit: cover;
  border-radius: var(--border-radius-sm);
}

.chapter-placeholder {
  width: 120px;
  height: 68px;
  background: var(--bg-secondary);
  border-radius: var(--border-radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
}

.chapter-play-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(24, 144, 255, 0.9);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.chapter-item:hover .chapter-play-indicator {
  opacity: 1;
}

.chapter-tag {
  font-size: 0.7rem;
  margin: 2px;
}

/* 教程视频增强样式 */
.tutorial-thumbnail {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.difficulty-badge {
  position: absolute;
  top: var(--spacing-sm);
  left: var(--spacing-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: 0.7rem;
  font-weight: bold;
  color: white;
}

.difficulty-easy {
  background: #52c41a;
}

.difficulty-medium {
  background: #faad14;
}

.difficulty-hard {
  background: #ff4d4f;
}

.difficulty-beginner {
  background: #1890ff;
}

.video-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-xs);
}

.video-tag {
  font-size: 0.7rem;
}

/* 界面截图展示样式 */
.interface-showcase {
  margin: var(--spacing-xl) 0;
  padding: var(--spacing-xl) 0;
  position: relative;
}

.showcase-background {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  opacity: 0.1;
}

.interface-gallery h3 {
  text-align: center;
  margin-bottom: var(--spacing-xl);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
}

.interface-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.interface-item {
  background: white;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.interface-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.interface-screenshot {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.interface-info {
  padding: var(--spacing-md);
}

.interface-info h4 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
}

.interface-info p {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.interface-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.interface-tag {
  font-size: 0.7rem;
}

/* 步骤截图样式 */
.step-screenshot {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  width: 80px;
  height: 60px;
  border-radius: var(--border-radius-sm);
  overflow: hidden;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.step-card:hover .step-screenshot {
  opacity: 1;
}

.step-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 架构图表样式 */
.architecture-background {
  background: linear-gradient(45deg, #f0f2f5 0%, #e6f7ff 100%);
  opacity: 0.5;
}

.architecture-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
}

.architecture-header h3 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.architecture-visualization {
  text-align: center;
  margin: var(--spacing-xl) 0;
}

.architecture-main-diagram {
  max-width: 100%;
  height: auto;
  border-radius: var(--border-radius-lg);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.layer-icon {
  width: 60px;
  height: 60px;
  margin-bottom: var(--spacing-md);
  border-radius: 50%;
  overflow: hidden;
  background: var(--bg-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.tech-icon {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.layer-content {
  flex: 1;
}

.tech-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin: var(--spacing-xs) 0;
  padding: var(--spacing-xs);
  border-radius: var(--border-radius-sm);
  transition: background-color 0.3s ease;
}

.tech-item:hover {
  background: var(--bg-primary);
}

.tech-icon-small {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  overflow: hidden;
  background: var(--bg-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.tech-small-icon {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.tech-name {
  font-size: 0.9rem;
  color: var(--text-primary);
}

/* 视差背景图案 */
.video-background-pattern {
  background:
    radial-gradient(circle at 20% 50%, rgba(24, 144, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(82, 196, 26, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(250, 173, 20, 0.1) 0%, transparent 50%);
}

/* 动画增强 */
@keyframes animate-slide-in-left {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slide-in-left {
  animation: animate-slide-in-left 0.6s ease-out forwards;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .interface-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .feature-media {
    margin-bottom: var(--spacing-sm);
  }

  .feature-image {
    height: 150px;
  }

  .step-screenshot {
    display: none;
  }

  .architecture-main-diagram {
    max-width: 100%;
    height: auto;
  }
}
}
</style>
