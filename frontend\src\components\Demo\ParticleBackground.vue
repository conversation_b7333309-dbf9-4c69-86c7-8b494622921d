<template>
  <div class="particle-background" ref="particleContainer">
    <canvas ref="particleCanvas" class="particle-canvas"></canvas>
    <div class="particle-overlay"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

const particleContainer = ref(null)
const particleCanvas = ref(null)
let animationId = null
let particles = []
let ctx = null

const props = defineProps({
  particleCount: {
    type: Number,
    default: 50
  },
  particleColor: {
    type: String,
    default: '#1890ff'
  },
  particleSize: {
    type: Number,
    default: 2
  },
  speed: {
    type: Number,
    default: 0.5
  },
  opacity: {
    type: Number,
    default: 0.6
  }
})

class Particle {
  constructor(canvas) {
    this.canvas = canvas
    this.reset()
    this.y = Math.random() * canvas.height
  }

  reset() {
    this.x = Math.random() * this.canvas.width
    this.y = -10
    this.size = Math.random() * props.particleSize + 1
    this.speedX = (Math.random() - 0.5) * props.speed
    this.speedY = Math.random() * props.speed + 0.5
    this.opacity = Math.random() * props.opacity + 0.1
    this.life = 1
    this.decay = Math.random() * 0.01 + 0.005
  }

  update() {
    this.x += this.speedX
    this.y += this.speedY
    this.life -= this.decay
    
    if (this.life <= 0 || this.y > this.canvas.height + 10) {
      this.reset()
    }
  }

  draw(ctx) {
    ctx.save()
    ctx.globalAlpha = this.opacity * this.life
    ctx.fillStyle = props.particleColor
    ctx.beginPath()
    ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2)
    ctx.fill()
    ctx.restore()
  }
}

const initParticles = () => {
  const canvas = particleCanvas.value
  if (!canvas) return

  ctx = canvas.getContext('2d')
  
  // 设置canvas尺寸
  const resizeCanvas = () => {
    const container = particleContainer.value
    if (!container) return
    
    canvas.width = container.offsetWidth
    canvas.height = container.offsetHeight
  }
  
  resizeCanvas()
  window.addEventListener('resize', resizeCanvas)

  // 创建粒子
  particles = []
  for (let i = 0; i < props.particleCount; i++) {
    particles.push(new Particle(canvas))
  }

  // 动画循环
  const animate = () => {
    ctx.clearRect(0, 0, canvas.width, canvas.height)
    
    particles.forEach(particle => {
      particle.update()
      particle.draw(ctx)
    })
    
    animationId = requestAnimationFrame(animate)
  }
  
  animate()
}

onMounted(() => {
  setTimeout(initParticles, 100) // 延迟初始化确保DOM已渲染
})

onUnmounted(() => {
  if (animationId) {
    cancelAnimationFrame(animationId)
  }
  window.removeEventListener('resize', () => {})
})
</script>

<style scoped>
.particle-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
  z-index: 1;
}

.particle-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.particle-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    ellipse at center,
    transparent 0%,
    rgba(0, 0, 0, 0.1) 100%
  );
}
</style>
