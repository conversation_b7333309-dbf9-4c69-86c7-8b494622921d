<template>
  <div id="app" class="app-container">
    <!-- 测试显示 -->
    <div class="test-banner">
      <h1>🎉 Vue应用正常加载</h1>
      <p>当前路由: {{ $route.path }}</p>
      <p>Element Plus 测试: <el-button type="primary">测试按钮</el-button></p>
    </div>

    <!-- 顶部导航栏 -->
    <header class="app-header">
      <div class="header-content">
        <!-- Logo和标题 -->
        <div class="brand-section">
          <div class="logo-icon">
            <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
              <circle cx="16" cy="16" r="16" fill="url(#gradient1)"/>
              <path d="M12 10h8v2h-8v-2zm0 4h8v2h-8v-2zm0 4h6v2h-6v-2z" fill="white"/>
              <defs>
                <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" style="stop-color:#1890ff"/>
                  <stop offset="100%" style="stop-color:#40a9ff"/>
                </linearGradient>
              </defs>
            </svg>
          </div>
          <div class="brand-text">
            <h1 class="brand-title">多模态智能面试评测系统</h1>
            <p class="brand-subtitle">AI-Powered Interview Assessment Platform</p>
          </div>
        </div>

        <!-- 导航菜单 -->
        <nav class="nav-menu">
          <el-menu
            :default-active="$route.path"
            mode="horizontal"
            :router="true"
            class="header-menu"
            background-color="transparent"
            text-color="#262626"
            active-text-color="#1890ff"
          >
            <el-menu-item index="/" class="menu-item">
              <el-icon><House /></el-icon>
              <span>首页</span>
            </el-menu-item>
            <el-menu-item index="/demo" class="menu-item">
              <el-icon><VideoPlay /></el-icon>
              <span>观看演示</span>
            </el-menu-item>
            <el-menu-item index="/interview-selection" class="menu-item">
              <el-icon><VideoCamera /></el-icon>
              <span>开始面试</span>
            </el-menu-item>
            <el-menu-item index="/learning-path" class="menu-item">
              <el-icon><Reading /></el-icon>
              <span>学习路径</span>
            </el-menu-item>
          </el-menu>
        </nav>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="app-main">
      <router-view v-slot="{ Component }">
        <transition name="page" mode="out-in">
          <component :is="Component" />
        </transition>
      </router-view>
    </main>

    <!-- 页脚 -->
    <footer class="app-footer">
      <div class="footer-content">
        <p>&copy; 2024 多模态智能面试评测系统 | 基于讯飞星火大模型驱动</p>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { House, VideoCamera, Reading, VideoPlay } from '@element-plus/icons-vue'
</script>

<style scoped>
.test-banner {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  color: white;
  text-align: center;
  padding: 20px;
  margin-bottom: 20px;
}

.test-banner h1 {
  margin: 0 0 10px 0;
  font-size: 2rem;
}

.test-banner p {
  margin: 0;
  font-size: 1.2rem;
  opacity: 0.9;
}

.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--bg-secondary);
}

/* 顶部导航栏样式 */
.app-header {
  position: sticky;
  top: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-light);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80px;
}

.brand-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.logo-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.brand-text {
  display: flex;
  flex-direction: column;
}

.brand-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.brand-subtitle {
  font-size: 0.875rem;
  color: var(--text-tertiary);
  margin: 0;
  font-weight: 400;
}

.nav-menu {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

.header-menu {
  border: none !important;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.menu-item:hover {
  background: rgba(24, 144, 255, 0.08) !important;
}

/* 主要内容区域 */
.app-main {
  flex: 1;
  position: relative;
  overflow: hidden;
}

/* 页面过渡动画 */
.page-enter-active,
.page-leave-active {
  transition: all 0.3s ease;
}

.page-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.page-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

/* 页脚样式 */
.app-footer {
  background: var(--bg-tertiary);
  border-top: 1px solid var(--border-light);
  padding: 24px 0;
  margin-top: auto;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  text-align: center;
  color: var(--text-tertiary);
  font-size: 0.875rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    padding: 0 16px;
    height: 64px;
  }

  .brand-title {
    font-size: 1.25rem;
  }

  .brand-subtitle {
    display: none;
  }

  .nav-menu {
    display: none;
  }

  .footer-content {
    padding: 0 16px;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: 0 12px;
  }

  .brand-section {
    gap: 12px;
  }

  .brand-title {
    font-size: 1.125rem;
  }
}

/* ==================== 顶部导航栏 ==================== */
.app-header {
  background: var(--bg-primary);
  box-shadow: var(--shadow-base);
  border-bottom: 1px solid var(--border-light);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  backdrop-filter: blur(10px);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80px;
}

.brand-section {
  display: flex;
  align-items: center;
  gap: var(--spacing-base);
}

.logo-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  animation: float 3s ease-in-out infinite;
}

.brand-text {
  display: flex;
  flex-direction: column;
}

.brand-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.brand-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin: 0;
  font-weight: var(--font-weight-normal);
}

.nav-menu {
  display: flex;
  align-items: center;
}

.header-menu {
  border-bottom: none !important;
}

.menu-item {
  display: flex !important;
  align-items: center !important;
  gap: var(--spacing-xs) !important;
  font-weight: var(--font-weight-medium) !important;
  transition: var(--transition-base) !important;
  border-radius: var(--border-radius-base) !important;
  margin: 0 var(--spacing-xs) !important;
}

.menu-item:hover {
  background: rgba(24, 144, 255, 0.1) !important;
  transform: translateY(-1px);
}

/* ==================== 主要内容区域 ==================== */
.app-main {
  flex: 1;
  min-height: calc(100vh - 140px);
  padding: 0;
}

/* ==================== 页脚 ==================== */
.app-footer {
  background: var(--bg-primary);
  border-top: 1px solid var(--border-light);
  padding: var(--spacing-lg) 0;
  margin-top: auto;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  text-align: center;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 768px) {
  .header-content {
    padding: 0 var(--spacing-base);
    height: 70px;
  }

  .brand-title {
    font-size: var(--font-size-lg);
  }

  .brand-subtitle {
    display: none;
  }

  .menu-item span {
    display: none;
  }

  .app-main {
    min-height: calc(100vh - 120px);
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: 0 var(--spacing-sm);
  }

  .brand-section {
    gap: var(--spacing-sm);
  }

  .logo-icon {
    width: 24px;
    height: 24px;
  }

  .brand-title {
    font-size: var(--font-size-base);
  }
}
</style>