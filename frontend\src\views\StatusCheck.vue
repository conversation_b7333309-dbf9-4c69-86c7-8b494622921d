<template>
  <div class="status-check-page">
    <h1>系统状态检查</h1>
    
    <div class="check-section">
      <h2>1. 图片加载测试</h2>
      <div class="test-grid">
        <div class="test-item">
          <h3>Picsum API 测试</h3>
          <div class="image-container">
            <img 
              :src="testImages.picsum" 
              @load="onImageLoad('picsum')"
              @error="onImageError('picsum')"
              alt="Picsum测试"
              class="test-img"
            />
          </div>
          <div class="status" :class="imageStatus.picsum">
            {{ getStatusText('picsum') }}
          </div>
        </div>
        
        <div class="test-item">
          <h3>Placeholder API 测试</h3>
          <div class="image-container">
            <img 
              :src="testImages.placeholder" 
              @load="onImageLoad('placeholder')"
              @error="onImageError('placeholder')"
              alt="Placeholder测试"
              class="test-img"
            />
          </div>
          <div class="status" :class="imageStatus.placeholder">
            {{ getStatusText('placeholder') }}
          </div>
        </div>
        
        <div class="test-item">
          <h3>本地SVG测试</h3>
          <div class="image-container">
            <img 
              :src="testImages.localSvg" 
              @load="onImageLoad('localSvg')"
              @error="onImageError('localSvg')"
              alt="本地SVG测试"
              class="test-img"
            />
          </div>
          <div class="status" :class="imageStatus.localSvg">
            {{ getStatusText('localSvg') }}
          </div>
        </div>
      </div>
    </div>

    <div class="check-section">
      <h2>2. MediaService 功能测试</h2>
      <div class="service-tests">
        <el-button @click="testMediaService" type="primary">测试 MediaService</el-button>
        <el-button @click="testDemoService" type="success">测试 DemoService</el-button>
        <el-button @click="clearLogs" type="info">清空日志</el-button>
      </div>
      <div class="logs-container">
        <h3>测试日志:</h3>
        <div class="logs" ref="logsContainer">
          <div v-for="(log, index) in logs" :key="index" class="log-item" :class="log.type">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="check-section">
      <h2>3. 演示场景数据测试</h2>
      <div class="scenario-test">
        <el-button @click="loadScenarios" type="warning">加载演示场景</el-button>
        <div v-if="scenarioData" class="scenario-info">
          <p>场景数量: {{ Object.keys(scenarioData).length }}</p>
          <div class="scenario-list">
            <div v-for="(scenario, key) in scenarioData" :key="key" class="scenario-item">
              <h4>{{ scenario.title }}</h4>
              <p>{{ scenario.description }}</p>
              <el-tag :type="getDifficultyType(scenario.difficulty)">{{ scenario.difficulty }}</el-tag>
              <span class="steps-count">{{ scenario.steps?.length || 0 }}个步骤</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="check-section">
      <h2>4. 网络连接测试</h2>
      <div class="network-tests">
        <el-button @click="testNetworkConnections" type="danger">测试网络连接</el-button>
        <div class="network-status">
          <div v-for="(status, url) in networkStatus" :key="url" class="network-item">
            <span class="url">{{ url }}</span>
            <span class="status" :class="status.status">{{ status.message }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import MediaService from '../services/mediaService.js'
import DemoService from '../services/demoService.js'

// 图片测试
const testImages = ref({
  picsum: 'https://picsum.photos/200/150?random=1',
  placeholder: 'https://via.placeholder.com/200x150/667eea/ffffff?text=测试',
  localSvg: ''
})

const imageStatus = ref({
  picsum: 'loading',
  placeholder: 'loading', 
  localSvg: 'loading'
})

// 日志系统
const logs = ref([])
const logsContainer = ref(null)

// 演示场景数据
const scenarioData = ref(null)

// 网络状态
const networkStatus = ref({})

// 生成本地SVG
const generateLocalSvg = () => {
  const svg = `
    <svg width="200" height="150" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="testGrad" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#667eea88;stop-opacity:1" />
        </linearGradient>
      </defs>
      <rect width="100%" height="100%" fill="url(#testGrad)"/>
      <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="14" 
            fill="white" text-anchor="middle" dy=".3em">本地SVG</text>
    </svg>
  `
  return `data:image/svg+xml;base64,${btoa(unescape(encodeURIComponent(svg)))}`
}

// 图片加载事件
const onImageLoad = (type) => {
  imageStatus.value[type] = 'success'
  addLog(`${type} 图片加载成功`, 'success')
}

const onImageError = (type) => {
  imageStatus.value[type] = 'error'
  addLog(`${type} 图片加载失败`, 'error')
}

// 获取状态文本
const getStatusText = (type) => {
  const statusMap = {
    loading: '加载中...',
    success: '加载成功',
    error: '加载失败'
  }
  return statusMap[imageStatus.value[type]] || '未知状态'
}

// 日志功能
const addLog = (message, type = 'info') => {
  const now = new Date()
  logs.value.push({
    time: now.toLocaleTimeString(),
    message,
    type
  })
  
  // 自动滚动到底部
  setTimeout(() => {
    if (logsContainer.value) {
      logsContainer.value.scrollTop = logsContainer.value.scrollHeight
    }
  }, 100)
}

const clearLogs = () => {
  logs.value = []
}

// 测试MediaService
const testMediaService = () => {
  addLog('开始测试 MediaService...', 'info')
  
  try {
    const testImage = MediaService.createUnsplashImage('technology', 400, 300)
    addLog(`createUnsplashImage: ${testImage}`, 'success')
    
    const testPlaceholder = MediaService.createPlaceholder(400, 300, '测试', '1890ff')
    addLog(`createPlaceholder: ${testPlaceholder}`, 'success')
    
    const placeholderMedia = MediaService.getPlaceholderMedia()
    addLog(`getPlaceholderMedia: ${placeholderMedia ? '有数据' : '无数据'}`, placeholderMedia ? 'success' : 'error')
    
    addLog('MediaService 测试完成', 'success')
  } catch (error) {
    addLog(`MediaService 测试失败: ${error.message}`, 'error')
  }
}

// 测试DemoService
const testDemoService = () => {
  addLog('开始测试 DemoService...', 'info')
  
  try {
    const scenarios = DemoService.getDemoScenarios()
    addLog(`getDemoScenarios: ${Object.keys(scenarios).length}个场景`, 'success')
    
    const features = DemoService.getFeatures()
    addLog(`getFeatures: ${features.length}个功能`, 'success')
    
    const videos = DemoService.getVideos()
    addLog(`getVideos: ${videos.length}个视频`, 'success')
    
    addLog('DemoService 测试完成', 'success')
  } catch (error) {
    addLog(`DemoService 测试失败: ${error.message}`, 'error')
  }
}

// 加载演示场景
const loadScenarios = () => {
  try {
    scenarioData.value = DemoService.getDemoScenarios()
    addLog(`加载了 ${Object.keys(scenarioData.value).length} 个演示场景`, 'success')
  } catch (error) {
    addLog(`加载演示场景失败: ${error.message}`, 'error')
  }
}

// 获取难度类型
const getDifficultyType = (difficulty) => {
  const typeMap = {
    '入门': 'success',
    '初级': 'info',
    '中级': 'warning', 
    '高级': 'danger',
    '专家': 'danger'
  }
  return typeMap[difficulty] || 'info'
}

// 测试网络连接
const testNetworkConnections = async () => {
  const testUrls = [
    'https://picsum.photos/100/100?random=1',
    'https://via.placeholder.com/100x100',
    'https://dummyimage.com/100x100'
  ]
  
  addLog('开始测试网络连接...', 'info')
  
  for (const url of testUrls) {
    try {
      const response = await fetch(url, { method: 'HEAD', mode: 'no-cors' })
      networkStatus.value[url] = {
        status: 'success',
        message: '连接成功'
      }
      addLog(`${url}: 连接成功`, 'success')
    } catch (error) {
      networkStatus.value[url] = {
        status: 'error', 
        message: `连接失败: ${error.message}`
      }
      addLog(`${url}: 连接失败`, 'error')
    }
  }
}

onMounted(() => {
  // 生成本地SVG
  testImages.value.localSvg = generateLocalSvg()
  
  addLog('状态检查页面初始化完成', 'info')
})
</script>

<style scoped>
.status-check-page {
  padding: var(--spacing-lg);
  max-width: 1200px;
  margin: 0 auto;
}

.check-section {
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  border: 1px solid var(--el-border-color-light);
  border-radius: var(--border-radius-lg);
}

.test-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

.test-item {
  text-align: center;
  padding: var(--spacing-md);
  border: 1px solid var(--el-border-color-lighter);
  border-radius: var(--border-radius);
}

.image-container {
  margin: var(--spacing-md) 0;
}

.test-img {
  max-width: 100%;
  height: auto;
  border-radius: var(--border-radius);
}

.status {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  font-weight: 600;
}

.status.loading {
  background: var(--el-color-info-light-8);
  color: var(--el-color-info);
}

.status.success {
  background: var(--el-color-success-light-8);
  color: var(--el-color-success);
}

.status.error {
  background: var(--el-color-danger-light-8);
  color: var(--el-color-danger);
}

.service-tests {
  margin-bottom: var(--spacing-md);
}

.service-tests .el-button {
  margin-right: var(--spacing-sm);
}

.logs-container {
  margin-top: var(--spacing-md);
}

.logs {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid var(--el-border-color-light);
  border-radius: var(--border-radius);
  padding: var(--spacing-sm);
  background: var(--el-color-info-light-9);
}

.log-item {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-xs);
  font-size: 0.9rem;
}

.log-time {
  color: var(--el-text-color-secondary);
  min-width: 80px;
}

.log-item.success .log-message {
  color: var(--el-color-success);
}

.log-item.error .log-message {
  color: var(--el-color-danger);
}

.log-item.warning .log-message {
  color: var(--el-color-warning);
}

.scenario-test {
  margin-top: var(--spacing-md);
}

.scenario-info {
  margin-top: var(--spacing-md);
}

.scenario-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-md);
  margin-top: var(--spacing-md);
}

.scenario-item {
  padding: var(--spacing-md);
  border: 1px solid var(--el-border-color-light);
  border-radius: var(--border-radius);
}

.scenario-item h4 {
  margin: 0 0 var(--spacing-xs) 0;
}

.scenario-item p {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--el-text-color-regular);
}

.steps-count {
  margin-left: var(--spacing-sm);
  color: var(--el-text-color-secondary);
  font-size: 0.9rem;
}

.network-tests {
  margin-top: var(--spacing-md);
}

.network-status {
  margin-top: var(--spacing-md);
}

.network-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm);
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.network-item .url {
  font-family: monospace;
  font-size: 0.9rem;
}

.network-item .status.success {
  color: var(--el-color-success);
}

.network-item .status.error {
  color: var(--el-color-danger);
}
</style>
