<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .debug-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            max-width: 600px;
            width: 90%;
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #ffffff, #f0f8ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .status-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .status-title {
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }
        
        .status-value {
            font-size: 1.5rem;
            color: #52c41a;
        }
        
        .error {
            color: #ff4d4f;
        }
        
        .info-section {
            margin-top: 30px;
            text-align: left;
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            border-radius: 10px;
        }
        
        .info-section h3 {
            margin-bottom: 15px;
            color: #40a9ff;
        }
        
        .info-section ul {
            list-style: none;
            padding-left: 0;
        }
        
        .info-section li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        
        .info-section li::before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #52c41a;
            font-weight: bold;
        }
        
        .btn {
            background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            margin: 10px;
            transition: transform 0.2s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔧 前端调试信息</h1>
        
        <div class="status-grid">
            <div class="status-item">
                <div class="status-title">服务器状态</div>
                <div class="status-value">✅ 正常</div>
            </div>
            <div class="status-item">
                <div class="status-title">端口</div>
                <div class="status-value">5175</div>
            </div>
            <div class="status-item">
                <div class="status-title">Vite版本</div>
                <div class="status-value">4.5.14</div>
            </div>
        </div>
        
        <div class="info-section">
            <h3>📋 检查清单</h3>
            <ul>
                <li>前端服务器已启动</li>
                <li>端口5175可访问</li>
                <li>静态文件服务正常</li>
                <li>热重载功能正常</li>
            </ul>
        </div>
        
        <div class="info-section">
            <h3>🔗 快速链接</h3>
            <button class="btn" onclick="window.open('http://localhost:5175/', '_blank')">
                打开主应用
            </button>
            <button class="btn" onclick="window.open('http://localhost:5175/test.html', '_blank')">
                测试页面
            </button>
        </div>
        
        <div class="info-section">
            <h3>💡 故障排除</h3>
            <p>如果主应用无法显示，可能的原因：</p>
            <ul>
                <li>Vue组件加载错误</li>
                <li>CSS变量未定义</li>
                <li>JavaScript模块导入失败</li>
                <li>Element Plus组件库问题</li>
            </ul>
        </div>
    </div>
    
    <script>
        console.log('🔧 调试页面加载完成');
        console.log('📍 当前URL:', window.location.href);
        console.log('🕐 加载时间:', new Date().toLocaleString());
    </script>
</body>
</html>
