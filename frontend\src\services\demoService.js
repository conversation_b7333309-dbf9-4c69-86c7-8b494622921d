/**
 * 演示服务 - 管理系统演示相关的数据和功能
 */

// 演示视频数据
export const demoVideos = {
  main: {
    id: 'main-demo',
    title: '多模态智能面试评测系统完整演示',
    description: '展示系统的完整功能流程，从面试选择到报告生成',
    duration: '8:45',
    thumbnail: '/demo/thumbnails/main-demo.jpg',
    url: '/demo/videos/main-demo.mp4',
    chapters: [
      { time: '0:00', title: '系统介绍' },
      { time: '1:30', title: '面试选择' },
      { time: '3:15', title: '多模态交互' },
      { time: '5:20', title: 'AI评测过程' },
      { time: '7:10', title: '报告生成' }
    ]
  },
  tutorials: [
    {
      id: 'getting-started',
      title: '快速开始指南',
      description: '5分钟了解如何使用系统进行面试',
      duration: '4:32',
      thumbnail: '/demo/thumbnails/getting-started.jpg',
      url: '/demo/videos/getting-started.mp4',
      category: '基础教程'
    },
    {
      id: 'multimodal-interview',
      title: '多模态面试详解',
      description: '深入了解文本、语音、视频三种面试模式',
      duration: '6:18',
      thumbnail: '/demo/thumbnails/multimodal.jpg',
      url: '/demo/videos/multimodal.mp4',
      category: '功能详解'
    },
    {
      id: 'ai-evaluation',
      title: 'AI评测机制解析',
      description: '了解讯飞星火大模型如何进行智能评测',
      duration: '5:45',
      thumbnail: '/demo/thumbnails/ai-evaluation.jpg',
      url: '/demo/videos/ai-evaluation.mp4',
      category: '技术原理'
    },
    {
      id: 'report-analysis',
      title: '评估报告解读',
      description: '如何理解和使用详细的评估报告',
      duration: '4:12',
      thumbnail: '/demo/thumbnails/report.jpg',
      url: '/demo/videos/report.mp4',
      category: '结果分析'
    },
    {
      id: 'learning-path',
      title: '个性化学习路径',
      description: '基于评估结果获得定制化学习建议',
      duration: '3:56',
      thumbnail: '/demo/thumbnails/learning.jpg',
      url: '/demo/videos/learning.mp4',
      category: '学习提升'
    }
  ]
}

// 功能演示数据
export const featuresDemos = [
  {
    id: 'multimodal-input',
    title: '多模态输入',
    icon: 'VideoCamera',
    description: '支持文本、语音、视频三种输入方式',
    highlights: [
      '实时语音识别',
      '视频表情分析',
      '文本语义理解',
      '多模态融合评估'
    ],
    demoSteps: [
      {
        step: 1,
        title: '选择输入模式',
        description: '根据题目类型选择最适合的回答方式',
        screenshot: '/demo/screenshots/input-selection.jpg'
      },
      {
        step: 2,
        title: '开始录制',
        description: '点击录制按钮开始语音或视频回答',
        screenshot: '/demo/screenshots/recording.jpg'
      },
      {
        step: 3,
        title: '实时反馈',
        description: '系统提供实时的录制状态和质量反馈',
        screenshot: '/demo/screenshots/feedback.jpg'
      }
    ]
  },
  {
    id: 'ai-interaction',
    title: 'AI智能对话',
    icon: 'ChatDotRound',
    description: '与AI面试官进行自然流畅的对话',
    highlights: [
      '智能问题生成',
      '上下文理解',
      '追问机制',
      '个性化调整'
    ],
    demoSteps: [
      {
        step: 1,
        title: '问题生成',
        description: 'AI根据岗位要求生成针对性问题',
        screenshot: '/demo/screenshots/question-gen.jpg'
      },
      {
        step: 2,
        title: '智能追问',
        description: '基于回答内容进行深入追问',
        screenshot: '/demo/screenshots/follow-up.jpg'
      },
      {
        step: 3,
        title: '难度调节',
        description: '根据表现动态调整问题难度',
        screenshot: '/demo/screenshots/difficulty.jpg'
      }
    ]
  },
  {
    id: 'real-time-analysis',
    title: '实时分析',
    icon: 'TrendCharts',
    description: '面试过程中的实时能力分析',
    highlights: [
      '语音情感分析',
      '视频行为识别',
      '文本质量评估',
      '综合能力评分'
    ],
    demoSteps: [
      {
        step: 1,
        title: '多维度监测',
        description: '同时分析语音、视频、文本多个维度',
        screenshot: '/demo/screenshots/monitoring.jpg'
      },
      {
        step: 2,
        title: '实时评分',
        description: '每个回答都会获得即时的评分反馈',
        screenshot: '/demo/screenshots/scoring.jpg'
      },
      {
        step: 3,
        title: '趋势分析',
        description: '展示面试过程中的表现趋势变化',
        screenshot: '/demo/screenshots/trends.jpg'
      }
    ]
  },
  {
    id: 'comprehensive-report',
    title: '综合报告',
    icon: 'Document',
    description: '详细的多维度评估报告',
    highlights: [
      '六大能力维度',
      '可视化图表',
      '改进建议',
      '学习路径推荐'
    ],
    demoSteps: [
      {
        step: 1,
        title: '能力雷达图',
        description: '直观展示各项能力的评估结果',
        screenshot: '/demo/screenshots/radar.jpg'
      },
      {
        step: 2,
        title: '详细分析',
        description: '每个维度的具体分析和评价',
        screenshot: '/demo/screenshots/analysis.jpg'
      },
      {
        step: 3,
        title: '改进建议',
        description: '针对性的能力提升建议和学习资源',
        screenshot: '/demo/screenshots/suggestions.jpg'
      }
    ]
  }
]

// 交互式演示步骤
export const interactiveSteps = [
  {
    id: 'domain-selection',
    title: '选择面试领域',
    description: '从人工智能、大数据、物联网中选择您的专业领域',
    component: 'DomainSelector',
    mockData: {
      domains: ['人工智能', '大数据', '物联网'],
      positions: ['技术岗', '运维测试岗', '产品岗']
    },
    tips: [
      '选择您最熟悉的技术领域',
      '不同领域的问题侧重点不同',
      '可以多次尝试不同领域的面试'
    ]
  },
  {
    id: 'interview-start',
    title: '开始面试对话',
    description: '与AI面试官开始智能对话，回答专业问题',
    component: 'InterviewChat',
    mockData: {
      question: '请简述您对机器学习中过拟合问题的理解，以及常用的解决方法。',
      suggestions: [
        '从定义开始解释',
        '举出具体例子',
        '说明解决方案',
        '分享实践经验'
      ]
    },
    tips: [
      '回答要条理清晰',
      '可以结合实际项目经验',
      '不确定时可以诚实表达'
    ]
  },
  {
    id: 'multimodal-response',
    title: '多模态回答',
    description: '尝试使用语音或视频方式回答问题',
    component: 'MultimodalInput',
    mockData: {
      modes: ['文本', '语音', '视频'],
      currentMode: '语音',
      recordingTime: 0
    },
    tips: [
      '语音回答更自然流畅',
      '视频可以展示表达能力',
      '选择您最舒适的方式'
    ]
  },
  {
    id: 'view-results',
    title: '查看评估结果',
    description: '获得详细的面试评估报告和能力分析',
    component: 'ResultsView',
    mockData: {
      scores: {
        technical: 85,
        communication: 78,
        logic: 82,
        innovation: 75,
        teamwork: 80,
        learning: 88
      },
      overall: 81
    },
    tips: [
      '关注各维度的平衡发展',
      '重视改进建议',
      '制定针对性学习计划'
    ]
  },
  {
    id: 'learning-path',
    title: '学习路径推荐',
    description: '根据评估结果获得个性化的学习建议',
    component: 'LearningRecommendation',
    mockData: {
      recommendations: [
        {
          title: '深度学习基础强化',
          priority: 'high',
          duration: '4周',
          modules: ['神经网络原理', '反向传播算法', '优化方法']
        },
        {
          title: '项目实战经验积累',
          priority: 'medium',
          duration: '6周',
          modules: ['端到端项目', '模型部署', '性能优化']
        }
      ]
    },
    tips: [
      '优先关注高优先级建议',
      '结合个人时间安排学习',
      '定期回顾学习进度'
    ]
  }
]

// 技术架构信息
export const architectureInfo = {
  layers: [
    {
      name: '前端展示层',
      technologies: ['Vue.js 3', 'Element Plus', 'WebRTC', 'Canvas API'],
      description: '负责用户界面展示和交互，支持多模态输入',
      color: '#4CAF50'
    },
    {
      name: '后端服务层',
      technologies: ['FastAPI', 'WebSocket', 'SQLAlchemy', 'Pydantic'],
      description: '处理业务逻辑、数据验证和API服务',
      color: '#2196F3'
    },
    {
      name: 'AI服务层',
      technologies: ['讯飞星火', '多模态分析', '自然语言处理', '机器学习'],
      description: '提供智能面试问题生成和多维度评估',
      color: '#FF9800'
    },
    {
      name: '数据存储层',
      technologies: ['SQLite', '文件存储', 'Redis缓存', '数据备份'],
      description: '存储用户数据、面试记录和评估结果',
      color: '#9C27B0'
    }
  ],
  features: [
    {
      icon: 'Lightning',
      title: '高性能架构',
      description: '异步处理、实时通信、智能缓存',
      details: [
        'FastAPI异步框架提供高并发支持',
        'WebSocket实现实时双向通信',
        'Redis缓存提升响应速度',
        '智能负载均衡和资源调度'
      ]
    },
    {
      icon: 'Shield',
      title: '安全可靠',
      description: '数据加密、隐私保护、安全认证',
      details: [
        'HTTPS加密传输保护数据安全',
        '用户隐私数据本地化处理',
        'JWT令牌认证机制',
        '定期安全审计和漏洞修复'
      ]
    },
    {
      icon: 'Expand',
      title: '可扩展设计',
      description: '模块化架构、插件系统、云原生',
      details: [
        '微服务架构支持独立扩展',
        '插件化AI模型集成',
        'Docker容器化部署',
        '支持多云环境部署'
      ]
    }
  ]
}

// 演示服务类
export class DemoService {
  // 获取演示视频列表
  static getVideos() {
    return demoVideos
  }

  // 获取功能演示数据
  static getFeatures() {
    return featuresDemos
  }

  // 获取交互式演示步骤
  static getInteractiveSteps() {
    return interactiveSteps
  }

  // 获取技术架构信息
  static getArchitecture() {
    return architectureInfo
  }

  // 播放演示视频
  static playVideo(videoId) {
    console.log(`播放演示视频: ${videoId}`)
    // 这里可以集成实际的视频播放逻辑
    return {
      success: true,
      message: `正在播放视频: ${videoId}`
    }
  }

  // 开始功能演示
  static startFeatureDemo(featureId) {
    const feature = featuresDemos.find(f => f.id === featureId)
    if (feature) {
      console.log(`开始功能演示: ${feature.title}`)
      return {
        success: true,
        feature: feature,
        message: `正在演示: ${feature.title}`
      }
    }
    return {
      success: false,
      message: '未找到指定的功能演示'
    }
  }

  // 获取演示统计信息
  static getStats() {
    return {
      totalVideos: demoVideos.tutorials.length + 1,
      totalFeatures: featuresDemos.length,
      totalSteps: interactiveSteps.length,
      estimatedTime: '30分钟'
    }
  }
}

export default DemoService
