from sqlalchemy import create_engine, Column, Integer, String, Float, DateTime, Text, JSON, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime
import os

# 数据库配置
DATABASE_URL = "sqlite:///./interview_system.db"
engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

class User(Base):
    """用户表"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True)
    email = Column(String(100), unique=True, index=True)
    created_at = Column(DateTime, default=datetime.utcnow)

class InterviewSession(Base):
    """面试会话表"""
    __tablename__ = "interview_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, index=True)
    domain = Column(String(50))  # 技术领域：AI、大数据、物联网
    position = Column(String(50))  # 岗位类型：技术岗、运维测试岗、产品岗
    status = Column(String(20), default="active")  # active, completed, cancelled
    start_time = Column(DateTime, default=datetime.utcnow)
    end_time = Column(DateTime)
    total_questions = Column(Integer, default=0)
    session_data = Column(JSON)  # 存储会话的详细数据

class InterviewQuestion(Base):
    """面试问题表"""
    __tablename__ = "interview_questions"
    
    id = Column(Integer, primary_key=True, index=True)
    domain = Column(String(50))
    position = Column(String(50))
    question_text = Column(Text)
    difficulty_level = Column(String(20))  # easy, medium, hard
    question_type = Column(String(30))  # technical, behavioral, scenario
    keywords = Column(JSON)  # 关键词列表
    expected_points = Column(JSON)  # 期望回答要点

class InterviewResponse(Base):
    """面试回答表"""
    __tablename__ = "interview_responses"
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(Integer, index=True)
    question_id = Column(Integer, index=True)
    question_text = Column(Text)
    response_text = Column(Text)
    response_time = Column(Float)  # 回答用时（秒）
    audio_file_path = Column(String(255))  # 音频文件路径
    video_file_path = Column(String(255))  # 视频文件路径
    created_at = Column(DateTime, default=datetime.utcnow)

class MultimodalAnalysis(Base):
    """多模态分析结果表"""
    __tablename__ = "multimodal_analysis"
    
    id = Column(Integer, primary_key=True, index=True)
    response_id = Column(Integer, index=True)
    
    # 语音分析结果
    speech_clarity = Column(Float)  # 语音清晰度 0-100
    speech_speed = Column(Float)  # 语速（字/分钟）
    emotion_score = Column(Float)  # 情感得分 0-100
    pause_frequency = Column(Float)  # 停顿频率
    
    # 视频分析结果
    eye_contact_score = Column(Float)  # 眼神交流得分 0-100
    facial_expression_score = Column(Float)  # 面部表情得分 0-100
    posture_score = Column(Float)  # 姿态得分 0-100
    gesture_appropriateness = Column(Float)  # 手势适当性 0-100
    
    # 文本分析结果
    content_relevance = Column(Float)  # 内容相关性 0-100
    logical_structure = Column(Float)  # 逻辑结构 0-100
    keyword_coverage = Column(Float)  # 关键词覆盖率 0-100
    innovation_score = Column(Float)  # 创新性得分 0-100
    
    analysis_timestamp = Column(DateTime, default=datetime.utcnow)

class EvaluationReport(Base):
    """评测报告表"""
    __tablename__ = "evaluation_reports"
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(Integer, index=True)
    
    # 六项核心能力指标
    professional_knowledge = Column(Float)  # 专业知识水平 0-100
    skill_matching = Column(Float)  # 技能匹配度 0-100
    language_expression = Column(Float)  # 语言表达能力 0-100
    logical_thinking = Column(Float)  # 逻辑思维能力 0-100
    innovation_ability = Column(Float)  # 创新能力 0-100
    stress_resistance = Column(Float)  # 应变抗压能力 0-100
    
    overall_score = Column(Float)  # 综合得分 0-100
    strengths = Column(JSON)  # 优势点列表
    weaknesses = Column(JSON)  # 不足点列表
    improvement_suggestions = Column(JSON)  # 改进建议列表
    
    report_data = Column(JSON)  # 完整报告数据（包含图表数据）
    generated_at = Column(DateTime, default=datetime.utcnow)

class ResumeAnalysis(Base):
    """简历分析表"""
    __tablename__ = "resume_analysis"
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(Integer, index=True)
    resume_file_path = Column(String(255))
    
    # 简历解析结果
    education_background = Column(JSON)  # 教育背景
    work_experience = Column(JSON)  # 工作经验
    skills = Column(JSON)  # 技能列表
    projects = Column(JSON)  # 项目经验
    
    # 匹配度分析
    position_match_score = Column(Float)  # 岗位匹配度 0-100
    skill_gap_analysis = Column(JSON)  # 技能差距分析
    
    analyzed_at = Column(DateTime, default=datetime.utcnow)

# 创建所有表
def create_tables():
    Base.metadata.create_all(bind=engine)

# 获取数据库会话
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
