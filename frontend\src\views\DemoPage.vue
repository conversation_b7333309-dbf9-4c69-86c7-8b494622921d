<template>
  <div class="demo-page">
    <!-- 演示页面头部 -->
    <div class="demo-header">
      <div class="header-content">
        <h1 class="demo-title">
          <el-icon><VideoPlay /></el-icon>
          系统演示
        </h1>
        <p class="demo-subtitle">体验多模态智能面试评测系统的完整功能</p>

        <!-- 演示统计信息 -->
        <div class="demo-stats" v-if="demoStats.totalVideos">
          <div class="stat-item">
            <span class="stat-number">{{ demoStats.totalVideos }}</span>
            <span class="stat-label">演示视频</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ demoStats.totalFeatures }}</span>
            <span class="stat-label">核心功能</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ demoStats.totalSteps }}</span>
            <span class="stat-label">体验步骤</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ Math.round(demoStats.estimatedTotalTime / 60) || 30 }}分钟</span>
            <span class="stat-label">预计时长</span>
          </div>
        </div>

        <!-- 导航标签 -->
        <el-tabs v-model="activeTab" class="demo-tabs" @tab-click="handleTabClick">
          <el-tab-pane label="功能演示" name="features">
            <el-icon><Star /></el-icon>
          </el-tab-pane>
          <el-tab-pane label="视频教程" name="video">
            <el-icon><VideoCamera /></el-icon>
          </el-tab-pane>
          <el-tab-pane label="交互体验" name="interactive">
            <el-icon><Mouse /></el-icon>
          </el-tab-pane>
          <el-tab-pane label="技术架构" name="architecture">
            <el-icon><Setting /></el-icon>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 演示内容区域 -->
    <div class="demo-content">
      <!-- 功能演示 -->
      <div v-show="activeTab === 'features'" class="features-demo">
        <div class="features-grid">
          <div 
            v-for="(feature, index) in features" 
            :key="index"
            class="feature-card"
            @click="showFeatureDemo(feature)"
          >
            <div class="feature-icon">
              <el-icon :size="40">
                <component :is="feature.icon" />
              </el-icon>
            </div>
            <h3>{{ feature.title }}</h3>
            <p>{{ feature.description }}</p>
            <div class="feature-actions">
              <el-button type="primary" size="small" @click.stop="playFeatureDemo(feature)">
                <el-icon><VideoPlay /></el-icon>
                观看演示
              </el-button>
              <el-button size="small" @click.stop="tryFeature(feature)">
                <el-icon><Operation /></el-icon>
                立即体验
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 视频教程 -->
      <div v-show="activeTab === 'video'" class="video-demo">
        <div class="video-section">
          <div class="video-player">
            <div class="video-placeholder">
              <el-icon :size="80"><VideoCamera /></el-icon>
              <h3>系统演示视频</h3>
              <p>完整展示多模态面试评测系统的使用流程</p>
              <el-button type="primary" size="large" @click="playMainVideo">
                <el-icon><VideoPlay /></el-icon>
                播放演示视频
              </el-button>
            </div>
          </div>
          
          <div class="video-list">
            <h3>分步教程</h3>
            <div 
              v-for="(video, index) in videoTutorials" 
              :key="index"
              class="video-item"
              @click="playVideo(video)"
            >
              <div class="video-thumbnail">
                <el-icon><VideoPlay /></el-icon>
              </div>
              <div class="video-info">
                <h4>{{ video.title }}</h4>
                <p>{{ video.description }}</p>

                <!-- 视频标签 -->
                <div class="video-tags" v-if="video.category || video.difficulty">
                  <el-tag v-if="video.category" type="primary" size="small">{{ video.category }}</el-tag>
                  <el-tag v-if="video.difficulty" :type="getDifficultyType(video.difficulty)" size="small">
                    {{ video.difficulty }}
                  </el-tag>
                </div>

                <!-- 视频元数据 -->
                <div class="video-meta">
                  <span class="video-duration">
                    <el-icon><Clock /></el-icon>
                    {{ video.duration }}
                  </span>
                  <span v-if="video.views" class="video-views">
                    <el-icon><View /></el-icon>
                    {{ video.views }}次观看
                  </span>
                  <span v-if="video.rating" class="video-rating">
                    <el-icon><Star /></el-icon>
                    {{ video.rating }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 交互体验 -->
      <div v-show="activeTab === 'interactive'" class="interactive-demo">
        <div class="demo-simulator">
          <h3>模拟面试体验</h3>
          <p>在这里您可以体验完整的面试流程，无需注册即可开始</p>
          
          <div class="simulator-steps">
            <div 
              v-for="(step, index) in simulatorSteps" 
              :key="index"
              class="step-card"
              :class="{ active: currentStep === index, completed: currentStep > index }"
              @click="goToStep(index)"
            >
              <div class="step-number">{{ index + 1 }}</div>
              <div class="step-content">
                <h4>{{ step.title }}</h4>
                <p>{{ step.description }}</p>

                <!-- 步骤详细信息 -->
                <div class="step-details" v-if="step.estimatedTime || step.difficulty">
                  <div class="step-meta">
                    <span v-if="step.estimatedTime" class="step-time">
                      <el-icon><Clock /></el-icon>
                      {{ step.estimatedTime }}
                    </span>
                    <span v-if="step.difficulty" class="step-difficulty">
                      <el-tag :type="getDifficultyType(step.difficulty)" size="small">
                        {{ step.difficulty }}
                      </el-tag>
                    </span>
                  </div>
                </div>

                <!-- 交互元素提示 -->
                <div v-if="step.interactiveElements && step.interactiveElements.length > 0" class="interactive-hint">
                  <el-icon><Operation /></el-icon>
                  <span>{{ step.interactiveElements.length }}个交互元素</span>
                </div>
              </div>
              <div class="step-status">
                <el-icon v-if="currentStep > index"><Check /></el-icon>
                <el-icon v-else-if="currentStep === index"><ArrowRight /></el-icon>
              </div>
            </div>
          </div>
          
          <div class="simulator-actions">
            <el-button 
              v-if="currentStep < simulatorSteps.length - 1"
              type="primary" 
              @click="nextStep"
            >
              下一步
              <el-icon><ArrowRight /></el-icon>
            </el-button>
            <el-button 
              v-else
              type="success" 
              @click="startRealInterview"
            >
              开始真实面试
              <el-icon><VideoPlay /></el-icon>
            </el-button>
            <el-button @click="resetSimulator">重新开始</el-button>
          </div>
        </div>
      </div>

      <!-- 技术架构 -->
      <div v-show="activeTab === 'architecture'" class="architecture-demo">
        <div class="architecture-overview">
          <h3>{{ architectureData.title || '技术架构图' }}</h3>
          <p v-if="architectureData.description" class="arch-description">
            {{ architectureData.description }}
          </p>

          <div class="architecture-diagram">
            <div
              v-for="(layer, index) in architectureData.layers"
              :key="index"
              class="arch-layer"
              :class="layer.name.toLowerCase().replace(/[^a-z]/g, '')"
            >
              <h4>{{ layer.name }}</h4>
              <p class="layer-description">{{ layer.description }}</p>
              <div class="tech-stack">
                <span
                  v-for="tech in layer.technologies"
                  :key="tech"
                  class="tech-item"
                >
                  {{ tech }}
                </span>
              </div>
            </div>
          </div>

          <div class="architecture-features">
            <div
              v-for="(feature, index) in architectureData.features"
              :key="index"
              class="feature-highlight"
            >
              <el-icon><component :is="getFeatureIcon(feature.name)" /></el-icon>
              <h4>{{ feature.name }}</h4>
              <p>{{ feature.description }}</p>
              <div v-if="feature.metrics" class="feature-metrics">
                <span
                  v-for="(value, key) in feature.metrics"
                  :key="key"
                  class="metric-item"
                >
                  {{ key }}: {{ value }}
                </span>
              </div>
            </div>
          </div>

          <!-- 技术规格 -->
          <div v-if="architectureData.specifications" class="tech-specifications">
            <h4>技术规格</h4>
            <div class="spec-grid">
              <div
                v-for="(spec, index) in architectureData.specifications"
                :key="index"
                class="spec-item"
              >
                <strong>{{ spec.category }}</strong>
                <ul>
                  <li v-for="detail in spec.details" :key="detail">{{ detail }}</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 返回按钮 -->
    <div class="demo-footer">
      <el-button @click="goBack">
        <el-icon><ArrowLeft /></el-icon>
        返回首页
      </el-button>
      <el-button type="primary" @click="startInterview">
        <el-icon><VideoPlay /></el-icon>
        开始面试
      </el-button>
    </div>

    <!-- 功能演示对话框 -->
    <el-dialog
      v-model="showFeatureDialog"
      :title="selectedFeature?.title"
      width="90%"
      center
      :show-close="true"
    >
      <FeatureShowcase
        v-if="selectedFeature"
        :feature="selectedFeature"
        @start-demo="handleStartDemo"
        @try-feature="handleTryFeature"
        @view-details="handleViewDetails"
      />
    </el-dialog>

    <!-- 视频播放对话框 -->
    <el-dialog
      v-model="showVideoDialog"
      :title="selectedVideo?.title"
      width="90%"
      center
    >
      <div class="video-player-dialog">
        <div class="video-placeholder-large">
          <el-icon :size="100"><VideoCamera /></el-icon>
          <h3>{{ selectedVideo?.title }}</h3>
          <p>{{ selectedVideo?.description }}</p>
          <el-button type="primary" size="large" @click="startVideoPlayback">
            <el-icon><VideoPlay /></el-icon>
            开始播放
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  VideoPlay, VideoCamera, Star, Mouse, Setting, Operation,
  Check, ArrowRight, ArrowLeft, Lightning, Lock, Expand,
  ChatDotRound, TrendCharts, Document, Clock, View
} from '@element-plus/icons-vue'
import DemoService from '../services/demoService.js'
import FeatureShowcase from '../components/Demo/FeatureShowcase.vue'

const router = useRouter()

// 响应式数据
const activeTab = ref('features')
const currentStep = ref(0)
const showFeatureDialog = ref(false)
const showVideoDialog = ref(false)
const selectedFeature = ref(null)
const selectedVideo = ref(null)
const demoStats = ref({})

// 从服务获取数据
const features = ref([])
const videoTutorials = ref([])
const simulatorSteps = ref([])
const architectureData = ref({})

// 初始化数据
onMounted(() => {
  loadDemoData()
})

const loadDemoData = () => {
  // 从服务加载演示数据
  const featuresData = DemoService.getFeatures()
  features.value = featuresData.map(feature => ({
    icon: feature.icon,
    title: feature.title,
    description: feature.description,
    demoDescription: feature.highlights.join('、'),
    id: feature.id,
    highlights: feature.highlights,
    demoSteps: feature.demoSteps,
    category: feature.category,
    difficulty: feature.difficulty,
    estimatedTime: feature.estimatedTime,
    technicalSpecs: feature.technicalSpecs
  }))

  const videosData = DemoService.getVideos()
  videoTutorials.value = videosData || []

  const stepsData = DemoService.getInteractiveSteps()
  simulatorSteps.value = stepsData.map(step => ({
    title: step.title,
    description: step.description,
    id: step.id,
    tips: step.tips,
    mockData: step.mockData,
    estimatedTime: step.estimatedTime,
    difficulty: step.difficulty,
    interactiveElements: step.interactiveElements
  }))

  architectureData.value = DemoService.getArchitecture()
  demoStats.value = DemoService.getDemoStats()
}

// 方法
const handleTabClick = (tab) => {
  console.log('切换到标签:', tab.props.name)
}

const showFeatureDemo = (feature) => {
  selectedFeature.value = feature
  showFeatureDialog.value = true
}

const playFeatureDemo = (feature) => {
  const result = DemoService.startFeatureDemo(feature.id)
  if (result.success) {
    ElMessage.success(result.message)
    showFeatureDemo(feature)
  } else {
    ElMessage.error(result.message)
  }
}

const tryFeature = (feature) => {
  ElMessage.info(`即将体验 ${feature.title} 功能`)
  // 根据不同功能跳转到相应页面
  switch (feature.id) {
    case 'multimodal-input':
      router.push('/interview-selection')
      break
    case 'comprehensive-report':
      // 如果有测试报告，跳转到报告页面
      router.push('/test-report-to-learning')
      break
    case 'ai-interaction':
      router.push('/interview-selection')
      break
    default:
      router.push('/interview-selection')
  }
}

const playMainVideo = () => {
  const result = DemoService.playVideo('main-demo')
  if (result.success) {
    ElMessage.success(result.message)
    selectedVideo.value = result.video
    showVideoDialog.value = true
  } else {
    ElMessage.error(result.message)
  }
}

const playVideo = (video) => {
  const result = DemoService.playVideo(video.id)
  if (result.success) {
    ElMessage.success(result.message)
    selectedVideo.value = result.video
    showVideoDialog.value = true
  } else {
    ElMessage.error(result.message)
  }
}

const nextStep = () => {
  if (currentStep.value < simulatorSteps.length - 1) {
    currentStep.value++
    ElMessage.success(`进入步骤 ${currentStep.value + 1}`)
  }
}

const goToStep = (index) => {
  currentStep.value = index
  ElMessage.info(`跳转到步骤 ${index + 1}`)
}

const resetSimulator = () => {
  currentStep.value = 0
  ElMessage.info('模拟器已重置')
}

const getDifficultyType = (difficulty) => {
  const typeMap = {
    '入门': 'success',
    '中级': 'warning',
    '高级': 'danger'
  }
  return typeMap[difficulty] || 'info'
}

const getFeatureIcon = (featureName) => {
  const iconMap = {
    '高性能': 'Lightning',
    '安全可靠': 'Lock',
    '可扩展': 'Expand',
    '智能化': 'Star',
    '实时性': 'VideoPlay',
    '稳定性': 'Check'
  }
  return iconMap[featureName] || 'Setting'
}

const startRealInterview = () => {
  ElMessageBox.confirm(
    '您即将开始真实的面试流程，是否确认？',
    '开始面试',
    {
      confirmButtonText: '确认开始',
      cancelButtonText: '取消',
      type: 'info'
    }
  ).then(() => {
    router.push('/interview-selection')
  })
}

const goBack = () => {
  router.push('/')
}

const startInterview = () => {
  router.push('/interview-selection')
}

const playFeatureVideo = () => {
  ElMessage.success(`正在播放 ${selectedFeature.value?.title} 演示视频`)
  showFeatureDialog.value = false
}

const startVideoPlayback = () => {
  ElMessage.success(`正在播放: ${selectedVideo.value?.title}`)
  showVideoDialog.value = false
}

// FeatureShowcase 组件事件处理
const handleStartDemo = (feature) => {
  ElMessage.success(`开始演示: ${feature.title}`)
  showFeatureDialog.value = false
  // 这里可以启动具体的演示流程
}

const handleTryFeature = (feature) => {
  showFeatureDialog.value = false
  tryFeature(feature)
}

const handleViewDetails = (feature) => {
  ElMessage.info(`查看 ${feature.title} 详细信息`)
  // 这里可以显示更详细的功能说明
}
</script>

<style scoped>
.demo-page {
  min-height: 100vh;
  background: var(--bg-primary);
}

.demo-header {
  background: var(--gradient-primary);
  color: white;
  padding: var(--spacing-xl) 0;
  text-align: center;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.demo-title {
  font-size: 2.5rem;
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
}

.demo-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  margin-bottom: var(--spacing-lg);
}

.demo-stats {
  display: flex;
  justify-content: center;
  gap: var(--spacing-xl);
  margin: var(--spacing-lg) 0;
  padding: var(--spacing-lg);
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-lg);
  backdrop-filter: blur(10px);
}

.stat-item {
  text-align: center;
  color: white;
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.8;
}

.demo-tabs {
  margin-top: var(--spacing-lg);
}

.demo-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-xl);
}

/* 功能演示样式 */
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

.feature-card {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  text-align: center;
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
  cursor: pointer;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.feature-icon {
  color: var(--color-primary);
  margin-bottom: var(--spacing-lg);
}

.feature-actions {
  margin-top: var(--spacing-lg);
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
}

/* 视频演示样式 */
.video-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-xl);
  margin-top: var(--spacing-lg);
}

.video-player {
  background: white;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.video-placeholder {
  padding: var(--spacing-xxl);
  text-align: center;
  background: var(--bg-secondary);
}

.video-list {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
}

.video-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.video-item:hover {
  background: var(--bg-secondary);
}

.video-info {
  flex: 1;
  margin-left: var(--spacing-md);
}

.video-info h4 {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: 1rem;
  color: var(--text-primary);
}

.video-info p {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.video-tags {
  margin: var(--spacing-sm) 0;
  display: flex;
  gap: var(--spacing-xs);
  flex-wrap: wrap;
}

.video-meta {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
  flex-wrap: wrap;
  margin-top: var(--spacing-sm);
}

.video-duration,
.video-views,
.video-rating {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.video-rating {
  color: var(--color-warning);
}

.video-thumbnail {
  width: 60px;
  height: 40px;
  background: var(--color-primary);
  border-radius: var(--border-radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-right: var(--spacing-md);
}

/* 交互体验样式 */
.demo-simulator {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
  margin-top: var(--spacing-lg);
}

.simulator-steps {
  margin: var(--spacing-xl) 0;
}

.step-card {
  display: flex;
  align-items: center;
  padding: var(--spacing-lg);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-md);
  margin-bottom: var(--spacing-md);
  cursor: pointer;
  transition: all 0.3s ease;
}

.step-card.active {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
}

.step-card.completed {
  border-color: var(--color-success);
  background: var(--color-success-light);
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--color-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: var(--spacing-lg);
}

.step-content {
  flex: 1;
}

.step-content h4 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
}

.step-content p {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-secondary);
}

.step-details {
  margin-top: var(--spacing-sm);
}

.step-meta {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
  flex-wrap: wrap;
}

.step-time {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.interactive-hint {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-sm);
  font-size: 0.9rem;
  color: var(--color-primary);
}

.simulator-actions {
  text-align: center;
  margin-top: var(--spacing-xl);
}

/* 技术架构样式 */
.architecture-overview {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
  margin-top: var(--spacing-lg);
}

.architecture-diagram {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-lg);
  margin: var(--spacing-xl) 0;
}

.arch-layer {
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-md);
  text-align: center;
}

.arch-layer.frontend {
  background: linear-gradient(135deg, #4CAF50, #8BC34A);
  color: white;
}

.arch-layer.backend {
  background: linear-gradient(135deg, #2196F3, #03A9F4);
  color: white;
}

.arch-layer.ai {
  background: linear-gradient(135deg, #FF9800, #FFC107);
  color: white;
}

.arch-layer.data {
  background: linear-gradient(135deg, #9C27B0, #E91E63);
  color: white;
}

.tech-stack {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  justify-content: center;
  margin-top: var(--spacing-md);
}

.tech-item {
  background: rgba(255, 255, 255, 0.2);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: 0.9rem;
}

.architecture-features {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

.feature-highlight {
  text-align: center;
  padding: var(--spacing-lg);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
}

.feature-highlight h4 {
  margin: var(--spacing-sm) 0;
  color: var(--text-primary);
}

.feature-highlight p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}

.feature-metrics {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-sm);
}

.metric-item {
  font-size: 0.8rem;
  color: var(--color-primary);
  background: var(--color-primary-light);
  padding: 2px 6px;
  border-radius: var(--border-radius-sm);
}

.arch-description {
  text-align: center;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
  font-size: 1.1rem;
}

.layer-description {
  font-size: 0.9rem;
  margin: var(--spacing-sm) 0;
  opacity: 0.9;
}

.tech-specifications {
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-xl);
  border-top: 1px solid var(--border-color);
}

.tech-specifications h4 {
  text-align: center;
  margin-bottom: var(--spacing-lg);
  color: var(--text-primary);
}

.spec-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.spec-item {
  background: var(--bg-secondary);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-md);
}

.spec-item strong {
  color: var(--text-primary);
  display: block;
  margin-bottom: var(--spacing-sm);
}

.spec-item ul {
  margin: 0;
  padding-left: var(--spacing-lg);
}

.spec-item li {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.demo-footer {
  text-align: center;
  padding: var(--spacing-xl);
  background: white;
  border-top: 1px solid var(--border-color);
}

/* 对话框样式 */
.feature-demo-content,
.video-player-dialog {
  text-align: center;
  padding: var(--spacing-xl);
}

.demo-video-placeholder,
.video-placeholder-large {
  background: var(--bg-secondary);
  padding: var(--spacing-xxl);
  border-radius: var(--border-radius-lg);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .video-section {
    grid-template-columns: 1fr;
  }
  
  .architecture-diagram {
    grid-template-columns: 1fr;
  }
  
  .architecture-features {
    grid-template-columns: 1fr;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
}
</style>
